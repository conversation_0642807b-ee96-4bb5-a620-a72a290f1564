import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import compression from 'vite-plugin-compression'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    compression({
      algorithm: 'gzip', // 'gzip' or 'brotliCompress'
      ext: '.gz', // compressed file extension
      threshold: 10240, // minimum size in bytes to apply compression (10KB)
      deleteOriginFile: false, // keep original files
      verbose: true, // show compression information
    }),
  ],
  build: {
    reportCompressedSize: true, // Show compressed size in build report
    cssCodeSplit: true, // Split CSS into chunks to avoid render blocking
    cssMinify: true, // Minify CSS
    minify: 'terser', // Use terser for better minification
    terserOptions: {
      compress: {
        drop_console: true, // Remove console logs in production
        drop_debugger: true, // Remove debugger statements
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor code to improve caching
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: [
            '@radix-ui/react-checkbox',
            '@radix-ui/react-dialog',
            '@radix-ui/react-label',
            '@radix-ui/react-popover',
            '@radix-ui/react-radio-group',
            '@radix-ui/react-select',
            '@radix-ui/react-slot',
            '@radix-ui/react-switch',
            '@radix-ui/react-tabs',
          ],
          // Split CSS into a separate chunk
          styles: ['./src/styles/badge-animations.css'],
        },
        // Optimize chunk loading order
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
  },
  // Optimize CSS handling
  css: {
    // Generate source maps for CSS in development
    devSourcemap: true,
  },
  // Optimize asset handling
  assetsInclude: ['**/*.webp', '**/*.svg', '**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif'],
})
