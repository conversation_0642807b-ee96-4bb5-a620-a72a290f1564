#!/usr/bin/env node

import express from 'express';
import compression from 'compression';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 5173;

// Abilita la compressione gzip/deflate per tutte le risposte
app.use(compression({
  // Livello di compressione (1 = veloce, 9 = migliore compressione)
  level: 6,
  // Comprimi tutte le risposte più grandi di 1KB
  threshold: 1024,
  // Funzione per decidere quali risposte comprimere
  filter: (req, res) => {
    // Non comprimere le risposte già compresse
    if (req.headers['accept-encoding'] &&
        req.headers['accept-encoding'].includes('gzip') &&
        !res.getHeader('Content-Encoding')) {
      return true;
    }
    return false;
  }
}));

// Servi i file statici dalla directory dist
app.use(express.static(path.join(__dirname, 'dist'), {
  // Imposta gli header per il caching
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Imposta il Content-Type appropriato per i file .gz
    if (filePath.endsWith('.gz')) {
      res.set('Content-Encoding', 'gzip');
      res.set('Content-Type', getContentType(filePath.slice(0, -3)));
    }
    // Imposta il Content-Type appropriato per i file .br
    if (filePath.endsWith('.br')) {
      res.set('Content-Encoding', 'br');
      res.set('Content-Type', getContentType(filePath.slice(0, -3)));
    }
  }
}));

// Gestisci le route per SPA (Single Page Application)
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Add a special route for preview mode
app.get('/preview', (req, res) => {
  // Add a query parameter to indicate preview mode
  res.redirect('/?preview=true');
});

// Fallback per tutte le altre route
app.use((req, res, next) => {
  // Verifica se la richiesta è per un file esistente
  const filePath = path.join(__dirname, 'dist', req.path);
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    return next();
  }
  // Altrimenti, servi index.html
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Funzione per ottenere il Content-Type in base all'estensione del file
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.html': return 'text/html';
    case '.js': return 'application/javascript';
    case '.css': return 'text/css';
    case '.json': return 'application/json';
    case '.png': return 'image/png';
    case '.jpg': case '.jpeg': return 'image/jpeg';
    case '.gif': return 'image/gif';
    case '.svg': return 'image/svg+xml';
    default: return 'application/octet-stream';
  }
}

// Avvia il server
app.listen(PORT, () => {
  console.log(`Preview server with compression running at http://localhost:${PORT}`);
  console.log('Press Ctrl+C to stop');
});
