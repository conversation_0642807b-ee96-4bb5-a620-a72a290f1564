#!/usr/bin/env node

import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import zlib from 'zlib';

// Ottieni il percorso della directory corrente
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = 5173;
const DIST_DIR = path.join(__dirname, 'dist');

// Mappa delle estensioni dei file ai tipi MIME
const MIME_TYPES = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.webp': 'image/webp',
  '.ico': 'image/x-icon',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'font/otf'
};

// Crea il server HTTP
const server = http.createServer((req, res) => {
  console.log(`${req.method} ${req.url}`);

  // Normalizza l'URL
  let url = req.url;

  // Gestisci la route principale
  if (url === '/' || url === '') {
    url = '/index.html';
  }

  // Costruisci il percorso del file
  let filePath = path.join(DIST_DIR, url);

  // Verifica se il file esiste
  fs.stat(filePath, (err, stats) => {
    if (err) {
      // Se il file non esiste, prova a servire index.html (per SPA)
      if (err.code === 'ENOENT') {
        filePath = path.join(DIST_DIR, 'index.html');
        fs.stat(filePath, (err, stats) => {
          if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('404 Not Found');
            return;
          }
          serveFile(filePath, req, res);
        });
        return;
      }

      // Altri errori
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end('500 Internal Server Error');
      return;
    }

    // Se è una directory, servi index.html
    if (stats.isDirectory()) {
      filePath = path.join(filePath, 'index.html');
      fs.stat(filePath, (err, stats) => {
        if (err) {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('404 Not Found');
          return;
        }
        serveFile(filePath, req, res);
      });
      return;
    }

    // Servi il file
    serveFile(filePath, req, res);
  });
});

// Funzione per servire un file con compressione
function serveFile(filePath, req, res) {
  // Ottieni l'estensione del file
  const extname = path.extname(filePath);
  const contentType = MIME_TYPES[extname] || 'application/octet-stream';

  // Leggi il file
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end('500 Internal Server Error');
      return;
    }

    // Verifica se il client supporta la compressione
    const acceptEncoding = req.headers['accept-encoding'] || '';

    // Imposta gli header di base
    const headers = {
      'Content-Type': contentType,
      'Vary': 'Accept-Encoding',
    };

    // Set cache headers based on file type
    if (filePath.match(/\.(js|css|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|eot)$/)) {
      if (filePath.match(/\-[a-zA-Z0-9]{8}\./)) {
        // Long cache for hashed assets (1 year)
        headers['Cache-Control'] = 'public, max-age=31536000, immutable';
      } else {
        // Shorter cache for non-hashed assets (1 day)
        headers['Cache-Control'] = 'public, max-age=86400';
      }
    } else {
      // No cache for HTML and other files
      headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    }

    // File di piccole dimensioni (< 1KB) non vengono compressi
    if (data.length < 1024) {
      res.writeHead(200, headers);
      res.end(data);
      return;
    }

    // Comprimi con gzip se supportato
    if (acceptEncoding.includes('gzip')) {
      zlib.gzip(data, (err, compressed) => {
        if (err) {
          res.writeHead(200, headers);
          res.end(data);
          return;
        }

        res.writeHead(200, {
          ...headers,
          'Content-Encoding': 'gzip',
          'Content-Length': compressed.length
        });
        res.end(compressed);
        console.log(`Served ${filePath} with gzip compression (${data.length} -> ${compressed.length} bytes)`);
      });
      return;
    }

    // Comprimi con deflate se supportato
    if (acceptEncoding.includes('deflate')) {
      zlib.deflate(data, (err, compressed) => {
        if (err) {
          res.writeHead(200, headers);
          res.end(data);
          return;
        }

        res.writeHead(200, {
          ...headers,
          'Content-Encoding': 'deflate',
          'Content-Length': compressed.length
        });
        res.end(compressed);
        console.log(`Served ${filePath} with deflate compression (${data.length} -> ${compressed.length} bytes)`);
      });
      return;
    }

    // Nessuna compressione supportata
    res.writeHead(200, headers);
    res.end(data);
    console.log(`Served ${filePath} without compression (${data.length} bytes)`);
  });
}

// Avvia il server
server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}/`);
  console.log(`Serving files from ${DIST_DIR}`);
  console.log('Press Ctrl+C to stop');
});
