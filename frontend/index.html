<!doctype html>
<html lang="en">
  <head>
    <!-- Inline script to set dark mode class before page renders -->
    <script>
      // Check localStorage or system preference for dark mode
      const isDarkMode = localStorage.getItem('theme') === 'dark' ||
        (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);

      // Apply dark mode class if needed
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/webp" href="/hithere_logo.webp" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Hi-there</title>
    <!-- Critical CSS inlined to prevent render blocking -->
    <style>
      /* Critical styles needed for initial render */
      body {
        margin: 0;
        font-family: 'Inter', sans-serif;
        background-color: hsl(210 40% 98%);
        color: hsl(222.2 84% 4.9%);
      }
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100%;
      }
      /* Hide content until CSS is loaded */
      .js .content-hidden {
        visibility: hidden;
      }
      /* Fade in content once CSS is loaded */
      .js .content-visible {
        animation: fadeIn 0.3s ease-in;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    </noscript>
    <script src="https://cdn.tailwindcss.com" defer></script>
    <script>
      // Preload tailwind config to avoid render blocking
      document.addEventListener('DOMContentLoaded', function() {
        tailwind.config = {
        darkMode: ["class"],
        theme: {
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
            heading: ['Playfair Display', 'serif'],
          },
          container: {
            center: true,
            padding: "2rem",
            screens: {
              "2xl": "1400px",
            },
          },
          extend: {
            colors: {
              border: "hsl(var(--border))",
              input: "hsl(var(--input))",
              ring: "hsl(var(--ring))",
              background: "hsl(var(--background))",
              foreground: "hsl(var(--foreground))",
              primary: {
                DEFAULT: "hsl(var(--primary))",
                foreground: "hsl(var(--primary-foreground))",
              },
              secondary: {
                DEFAULT: "hsl(var(--secondary))",
                foreground: "hsl(var(--secondary-foreground))",
              },
              destructive: {
                DEFAULT: "hsl(var(--destructive))",
                foreground: "hsl(var(--destructive-foreground))",
              },
              muted: {
                DEFAULT: "hsl(var(--muted))",
                foreground: "hsl(var(--muted-foreground))",
              },
              accent: {
                DEFAULT: "hsl(var(--accent))",
                foreground: "hsl(var(--accent-foreground))",
              },
              popover: {
                DEFAULT: "hsl(var(--popover))",
                foreground: "hsl(var(--popover-foreground))",
              },
              card: {
                DEFAULT: "hsl(var(--card))",
                foreground: "hsl(var(--card-foreground))",
              },
            },
            borderRadius: {
              lg: "var(--radius)",
              md: "calc(var(--radius) - 2px)",
              sm: "calc(var(--radius) - 4px)",
            },
            keyframes: {
              "accordion-down": {
                from: { height: 0 },
                to: { height: "var(--radix-accordion-content-height)" },
              },
              "accordion-up": {
                from: { height: "var(--radix-accordion-content-height)" },
                to: { height: 0 },
              },
            },
            animation: {
              "accordion-down": "accordion-down 0.2s ease-out",
              "accordion-up": "accordion-up 0.2s ease-out",
              "fade-in": "fade-in 1.5s ease-out forwards",
              "slide-in-right": "slide-in-right 1s ease-out forwards",
              "slide-in-left": "slide-in-left 1s ease-out forwards",
            },
            keyframes: {
              "accordion-down": {
                from: { height: 0 },
                to: { height: "var(--radix-accordion-content-height)" },
              },
              "accordion-up": {
                from: { height: "var(--radix-accordion-content-height)" },
                to: { height: 0 },
              },
              "fade-in": {
                "0%": { opacity: "0" },
                "100%": { opacity: "1" },
              },
              "slide-in-right": {
                "0%": { transform: "translateX(100%)", opacity: "0" },
                "100%": { transform: "translateX(0)", opacity: "1" },
              },
              "slide-in-left": {
                "0%": { transform: "translateX(-100%)", opacity: "0" },
                "100%": { transform: "translateX(0)", opacity: "1" },
              },
            },
          },
        },
      }
      });
    </script>
    <style type="text/css">
      :root {
        --background: 210 40% 98%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 170 100% 45%;
        --primary-foreground: 210 40% 98%;
        --secondary: 170 100% 96%;
        --secondary-foreground: 170 100% 35%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 262 83% 58%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 170 100% 45%;
        --radius: 1rem;
      }

      .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 210 40% 98%;
        --primary-foreground: 222.2 47.4% 11.2%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;
      }

      * {
        border-color: hsl(var(--input));
      }
      body {
        background-color: hsl(var(--background));
        color: hsl(var(--foreground));
      }
    </style>
  </head>
  <body class="js">
    <!-- Loading indicator shown while CSS loads -->
    <div class="loading" id="loading">
      <div>Loading...</div>
    </div>

    <!-- Main content initially hidden -->
    <div id="root" class="content-hidden"></div>

    <!-- Inline script to handle CSS loading -->
    <script>
      // Function to show content once CSS is loaded
      function showContent() {
        document.getElementById('loading').style.display = 'none';
        const root = document.getElementById('root');
        root.classList.remove('content-hidden');
        root.classList.add('content-visible');
      }

      // Check if stylesheets are loaded
      function checkStylesheetsLoaded() {
        const styleSheets = document.styleSheets;
        let cssLoaded = true;

        for (let i = 0; i < styleSheets.length; i++) {
          const sheet = styleSheets[i];
          // Skip inline styles and non-CSS stylesheets
          if (!sheet.href || sheet.href.startsWith('data:')) continue;

          try {
            // If we can access cssRules, the stylesheet has loaded
            const rules = sheet.cssRules;
            if (!rules || rules.length === 0) {
              cssLoaded = false;
            }
          } catch (e) {
            // If we can't access cssRules, the stylesheet hasn't loaded
            cssLoaded = false;
          }
        }

        return cssLoaded;
      }

      // Check if stylesheets are loaded, if not, wait for load event
      if (checkStylesheetsLoaded()) {
        showContent();
      } else {
        window.addEventListener('load', showContent);
      }
    </script>

    <script type="module" src="/src/main.tsx"></script>
    <!-- Preload script that doesn't rely on import.meta -->
    <script>
      // Dynamically preload banner image for the current user profile
      // This helps improve LCP performance
      (function() {
        const path = window.location.hash;
        if (path && path.startsWith('#/u/')) {
          const username = path.split('/')[2];
          if (username) {
            // Get the base URL from the current location
            const baseUrl = window.location.origin;
            const preloadLink = document.createElement('link');
            preloadLink.rel = 'preload';
            preloadLink.as = 'image';
            preloadLink.href = `${baseUrl}/media/users/${username}/banner`;
            preloadLink.setAttribute('fetchpriority', 'high');
            document.head.appendChild(preloadLink);
          }
        }
      })();
    </script>
  </body>
</html>
