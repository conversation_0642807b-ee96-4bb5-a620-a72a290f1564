# Come avviare il server di preview

Per visualizzare la versione di produzione dell'applicazione, segui questi passaggi:

1. Assicurati di aver compilato l'applicazione con:
   ```
   npm run build
   ```

2. Avvia il server di preview con:
   ```
   npx vite preview --port 4173
   ```

3. Apri il browser all'indirizzo:
   ```
   http://localhost:4173/
   ```

Questo ti mostrerà l'applicazione esattamente come apparirà in produzione, con tutte le ottimizzazioni applicate.
