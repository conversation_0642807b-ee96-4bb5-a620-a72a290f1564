/* Custom styles can be added here */
@import './styles/globals.css';
@import './styles/rich-text.css';

/* Font styles */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
}

.uppercase, .capitalize {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
}

body, p, div, span, button, input, select, textarea {
  font-family: 'Inter', sans-serif;
}

/* Rich Text Editor Styles */
.ProseMirror:focus {
  outline: none;
}

.ProseMirror p {
  margin-bottom: 0.5em;
}

.ProseMirror h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.ProseMirror h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin-bottom: 0.5em;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5em;
  margin-bottom: 0.5em;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

/* Bio content styles */
.bio-content h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 0.5em;
  margin-bottom: 0.3em;
}

.bio-content h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin-top: 0.5em;
  margin-bottom: 0.3em;
}

.bio-content p {
  margin-bottom: 0.5em;
}

.bio-content ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin-bottom: 0.5em;
}

.bio-content ol {
  list-style-type: decimal;
  padding-left: 1.5em;
  margin-bottom: 0.5em;
}

.bio-content a {
  color: #3b82f6;
  text-decoration: underline;
}

/* Animation keyframes for homepage */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 1s ease-in-out;
}

.animate-slide-in-right {
  animation: slideInRight 1s ease-in-out;
}

.animate-slide-in-left {
  animation: slideInLeft 1s ease-in-out;
}

/* Badge animations */
@keyframes popup {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  70% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes vibrate {
  0%, 100% {
    transform: translateY(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateY(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateY(2px);
  }
}

/* Password dialog shake animation */
@keyframes shake-dialog {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

.shake-animation {
  animation: shake-dialog 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Thank you popup animation */
.thankyou-dialog-popup {
  animation: popup 0.3s ease-out;
}

/* Center dialog in the middle of the screen */
.fixed-center-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  max-width: 90vw;
  width: 450px;
  margin: 0 auto;
  z-index: 9999;
}
