import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../lib/utils';

// Icons
import {
  LayoutDashboard,
  BarChart2,
  User,
  Eye,
  Settings,
  LogOut,
  Users,
  ChevronLeft,
  ChevronRight,
  FileText,
  Share2,
  DollarSign,
  Zap,
  ShoppingBag
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { Button } from './ui/button';

interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  href: string;
  active: boolean;
  collapsed?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  href,
  active,
  collapsed = false
}) => {
  return (
    <Link
      to={href}
      className={cn(
        "flex items-center gap-3 px-4 py-3 rounded-lg transition-colors",
        active
          ? "bg-primary/10 text-primary font-medium"
          : "text-muted-foreground hover:bg-secondary hover:text-foreground",
        collapsed && "justify-center px-2"
      )}
      title={collapsed ? label : undefined}
    >
      <div className="w-5 h-5 flex-shrink-0">{icon}</div>
      {!collapsed && <span>{label}</span>}
    </Link>
  );
};

export const Sidebar: React.FC = () => {
  const location = useLocation();
  const { logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleLogout = () => {
    logout();
  };

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div className={cn(
      "h-screen border-r bg-card flex flex-col transition-all duration-300",
      collapsed ? "w-12" : "w-52"
    )}>
      <div className="flex justify-end p-2 border-b">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleCollapse}
          className="h-8 w-8"
          title={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </Button>
      </div>

      <div className="flex-1 px-3 py-4 space-y-1">
        <SidebarItem
          icon={<LayoutDashboard size={18} />}
          label="Dashboard"
          href="/dashboard"
          active={isActive('/dashboard')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<User size={18} />}
          label="Profile"
          href="/profile"
          active={isActive('/profile')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<Eye size={18} />}
          label="Preview"
          href="/preview"
          active={isActive('/preview')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<Zap size={18} />}
          label="Actions"
          href="/actions"
          active={isActive('/actions')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<FileText size={18} />}
          label="Leads"
          href="/leads"
          active={isActive('/leads')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<BarChart2 size={18} />}
          label="Reports"
          href="/stats"
          active={isActive('/stats')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<DollarSign size={18} />}
          label="Monetization"
          href="/direct-monetization"
          active={isActive('/direct-monetization')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<ShoppingBag size={18} />}
          label="Marketplace"
          href="/marketplace"
          active={isActive('/marketplace')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<ShoppingBag size={18} />}
          label="Amazon Affiliate"
          href="/amazon-affiliate"
          active={isActive('/amazon-affiliate')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<Share2 size={18} />}
          label="Referrals"
          href="/referrals"
          active={isActive('/referrals')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<Users size={18} />}
          label="Users"
          href="/users"
          active={isActive('/users')}
          collapsed={collapsed}
        />
        <SidebarItem
          icon={<Settings size={18} />}
          label="Settings"
          href="/settings"
          active={isActive('/settings')}
          collapsed={collapsed}
        />
      </div>

      <div className={cn("border-t", collapsed ? "p-2" : "p-4")}>
        <button
          onClick={handleLogout}
          className={cn(
            "flex items-center rounded-lg text-muted-foreground hover:bg-secondary hover:text-foreground transition-colors",
            collapsed ? "justify-center p-2" : "gap-3 px-4 py-3 w-full"
          )}
          title={collapsed ? "Logout" : undefined}
        >
          <LogOut size={18} />
          {!collapsed && <span>Logout</span>}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
