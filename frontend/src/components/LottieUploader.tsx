import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from './ui/button';
import { FileText, Upload, X } from 'lucide-react';
import { uploadLottieJson } from '../api/media';

interface LottieUploaderProps {
  onJsonUploaded: (jsonUrl: string) => void;
  currentJsonUrl?: string;
  className?: string;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

export const LottieUploader: React.FC<LottieUploaderProps> = ({
  onJsonUploaded,
  currentJsonUrl,
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [preview, setPreview] = useState<{
    fileName: string;
    size: number;
    content?: any;
  } | null>(null);
  const [animationContainer, setAnimationContainer] = useState<HTMLDivElement | null>(null);
  const [lottieAnimation, setLottieAnimation] = useState<any>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize preview from current JSON URL
  useEffect(() => {
    if (currentJsonUrl && !preview) {
      const fileName = currentJsonUrl.split('/').pop() || 'animation.json';
      setPreview({
        fileName,
        size: 0, // Size unknown for existing files
      });
      loadAnimationPreview(currentJsonUrl);
    }
  }, [currentJsonUrl, preview]);

  // Load animation when preview content is available
  useEffect(() => {
    if (preview && preview.content && animationContainer) {
      loadAnimationPreview('', preview.content);
    }
  }, [preview, animationContainer]);

  const loadAnimationPreview = async (jsonUrl: string, animationData?: any) => {
    try {
      console.log('Loading animation preview:', { jsonUrl, hasAnimationData: !!animationData, hasContainer: !!animationContainer });

      // Load Lottie library if not already loaded
      if (!(window as any).lottie) {
        console.log('Loading Lottie library...');
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script');
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.10.2/lottie.min.js';
          script.onload = () => {
            console.log('Lottie library loaded successfully');
            resolve();
          };
          script.onerror = () => reject(new Error('Failed to load Lottie library'));
          document.head.appendChild(script);
        });
      }

      if (animationContainer && (window as any).lottie) {
        // Clear previous animation
        if (lottieAnimation) {
          console.log('Destroying previous animation');
          lottieAnimation.destroy();
        }

        let anim;

        if (animationData) {
          console.log('Loading animation from data');
          // Use animation data directly if available
          anim = (window as any).lottie.loadAnimation({
            container: animationContainer,
            renderer: 'svg',
            loop: true,
            autoplay: true,
            animationData: animationData
          });
        } else {
          console.log('Loading animation from URL:', jsonUrl);
          // Load from URL
          const fullUrl = jsonUrl.startsWith('http')
            ? jsonUrl
            : `${import.meta.env.VITE_API_URL.replace('/api', '')}${jsonUrl}`;

          console.log('Full URL:', fullUrl);
          anim = (window as any).lottie.loadAnimation({
            container: animationContainer,
            renderer: 'svg',
            loop: true,
            autoplay: true,
            path: fullUrl
          });
        }

        console.log('Animation created:', anim);
        setLottieAnimation(anim);
      } else {
        console.log('Missing requirements:', { hasContainer: !!animationContainer, hasLottie: !!(window as any).lottie });
      }
    } catch (error) {
      console.error('Error loading Lottie animation preview:', error);
    }
  };

  const validateJsonFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`;
    }

    if (!file.name.toLowerCase().endsWith('.json')) {
      return 'Only JSON files are allowed';
    }

    return null;
  };

  const validateLottieJson = (jsonContent: any): string | null => {
    if (!jsonContent.v) {
      return 'Invalid Lottie JSON: missing version (v) property';
    }
    if (!jsonContent.fr) {
      return 'Invalid Lottie JSON: missing frame rate (fr) property';
    }
    if (!jsonContent.layers || !Array.isArray(jsonContent.layers)) {
      return 'Invalid Lottie JSON: missing or invalid layers property';
    }
    return null;
  };

  const handleFileChange = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validationError = validateJsonFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      // Read and validate JSON content
      const fileContent = await file.text();
      const jsonContent = JSON.parse(fileContent);

      const lottieValidationError = validateLottieJson(jsonContent);
      if (lottieValidationError) {
        setError(lottieValidationError);
        return;
      }

      setError(null);
      setPreview({
        fileName: file.name,
        size: file.size,
        content: jsonContent
      });

      // Start upload
      setIsUploading(true);
      setUploadProgress(0);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      try {
        const jsonUrl = await uploadLottieJson(file);
        setUploadProgress(100);
        onJsonUploaded(jsonUrl);

        // Load preview animation with the JSON content
        setTimeout(() => {
          loadAnimationPreview(jsonUrl, jsonContent);
        }, 500);

      } catch (uploadError) {
        console.error('Upload error:', uploadError);
        setError('Failed to upload JSON file');
        setPreview(null);
      } finally {
        clearInterval(progressInterval);
        setIsUploading(false);
        setUploadProgress(0);
      }

    } catch (parseError) {
      setError('Invalid JSON file format');
    }
  }, [onJsonUploaded]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (fileInputRef.current) {
        const dt = new DataTransfer();
        dt.items.add(file);
        fileInputRef.current.files = dt.files;
        fileInputRef.current.dispatchEvent(new Event('change', { bubbles: true }));
      }
    }
  }, []);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemove = () => {
    setPreview(null);
    setError(null);
    if (lottieAnimation) {
      lottieAnimation.destroy();
      setLottieAnimation(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onJsonUploaded(''); // Notify parent that file was removed
  };

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`relative border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
          isDragging
            ? 'border-primary bg-primary/10'
            : 'border-border hover:border-primary/50 hover:bg-secondary/20'
        } h-48`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        {preview ? (
          <div className="relative w-full h-full">
            <div className="flex flex-col items-center justify-center h-full">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="text-primary" size={24} />
                <span className="text-sm font-medium truncate max-w-32">
                  {preview.fileName}
                </span>
              </div>

              {preview.size > 0 && (
                <p className="text-xs text-muted-foreground mb-2">
                  {(preview.size / 1024).toFixed(1)} KB
                </p>
              )}

              {/* Animation preview container */}
              <div
                ref={setAnimationContainer}
                className="w-24 h-24 bg-secondary/20 rounded-md flex items-center justify-center"
              >
                {!lottieAnimation && (
                  <div className="text-xs text-muted-foreground">Preview</div>
                )}
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                handleRemove();
              }}
            >
              <X size={12} />
            </Button>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <Upload className="text-muted-foreground mb-2" size={32} />
            <p className="text-sm text-muted-foreground">
              Upload Lottie JSON
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Drag & drop or click to browse
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              Max size: 5MB • JSON format only
            </p>
          </div>
        )}
      </div>

      {isUploading && (
        <div className="mt-4">
          <div className="w-full bg-secondary rounded-full h-2.5">
            <div
              className="bg-primary h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-sm text-center mt-2 text-muted-foreground">
            Uploading Lottie JSON... {uploadProgress}%
          </p>
        </div>
      )}

      {error && (
        <p className="text-sm text-red-500 mt-2">{error}</p>
      )}

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".json,application/json"
        className="hidden"
      />
    </div>
  );
};
