import React, { useState, useCallback, useEffect } from 'react';
import { Button } from './ui/button';
import { uploadProfileImage } from '../api/media';
import { ImageUploader } from './ImageUploader';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Settings } from 'lucide-react';

interface BackgroundImageUploaderProps {
  onImageUploaded: (imageUrl: string) => void;
  onPropertiesChanged: (properties: BackgroundProperties) => void;
  currentImage?: string;
  currentProperties?: BackgroundProperties;
  className?: string;
}

export interface BackgroundProperties {
  backgroundPosition: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat: 'no-repeat' | 'repeat' | 'space';
  backgroundSize: 'auto' | 'cover';
  backgroundAttachment: 'fixed' | 'scroll';
}

const defaultProperties: BackgroundProperties = {
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  backgroundSize: 'cover',
  backgroundAttachment: 'scroll'
};

export const BackgroundImageUploader: React.FC<BackgroundImageUploaderProps> = ({
  onImageUploaded,
  onPropertiesChanged,
  currentImage,
  currentProperties = defaultProperties,
  className = '',
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [properties, setProperties] = useState<BackgroundProperties>(currentProperties);
  const [showSettings, setShowSettings] = useState(false);

  // Update properties when currentProperties changes
  useEffect(() => {
    setProperties(currentProperties);
  }, [currentProperties]);

  // Log the current image for debugging
  console.log('BackgroundImageUploader: currentImage:', currentImage);
  console.log('BackgroundImageUploader: VITE_API_URL:', import.meta.env.VITE_API_URL);

  // Log the full URL that would be used
  const debugFullUrl = currentImage && !currentImage.startsWith('http') && !currentImage.startsWith('data:')
    ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${currentImage}`
    : currentImage;
  console.log('BackgroundImageUploader: Full URL that would be used:', debugFullUrl);

  // Handle image selection
  const handleImageSelect = useCallback(async (file: File, preview: string) => {
    if (!file || file.name === "removed.txt") {
      // Handle image removal
      console.log('Removing background image');
      onImageUploaded("");

      // Update the background image in the DOM directly for immediate feedback
      const previewContainer = document.querySelector('.profile-preview-container');
      if (previewContainer) {
        (previewContainer as HTMLElement).style.backgroundImage = 'none';
        console.log('Background image removed directly from preview container');
      }
      return;
    }

    console.log('Starting background image upload');
    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    // Immediately set the preview image to show feedback to the user
    if (preview) {
      console.log('Setting temporary preview image');
      // We don't call onImageUploaded here to avoid saving the data URL to the state
    }

    let uploadInterval: NodeJS.Timeout | null = null;

    try {
      // Setup progress tracking
      uploadInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const newProgress = prev + 10;
          if (newProgress >= 100) {
            if (uploadInterval) clearInterval(uploadInterval);
            return 100;
          }
          return newProgress;
        });
      }, 200);

      // Upload the file to the server
      console.log('Uploading background image to server...');
      console.log('VITE_API_URL:', import.meta.env.VITE_API_URL);
      const imageUrl = await uploadProfileImage(file, 'background'); // Use dedicated background upload type
      console.log('Background image uploaded successfully:', imageUrl);

      // Update the parent component with the new image URL
      onImageUploaded(imageUrl);
      console.log('Background image URL passed to parent component:', imageUrl);

      // Apply the background image immediately
      const fullImageUrl = imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')
        ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${imageUrl}`
        : imageUrl;

      console.log('Full background image URL for DOM application:', fullImageUrl);

      // Update the background image in the DOM directly for immediate feedback
      const previewContainer = document.querySelector('.profile-preview-container');
      console.log('Preview container found:', !!previewContainer);
      if (previewContainer) {
        (previewContainer as HTMLElement).style.backgroundImage = `url(${fullImageUrl})`;
        console.log('Background image applied directly to preview container:', fullImageUrl);
        console.log('Container style after applying background:', (previewContainer as HTMLElement).style.cssText);
      } else {
        console.error('Preview container with class .profile-preview-container not found');
        // Try to find the container by other means
        const allContainers = document.querySelectorAll('div[style*="backgroundColor"]');
        console.log('Found', allContainers.length, 'potential containers');

        // Try to apply to all potential containers
        if (allContainers.length > 0) {
          console.log('Attempting to apply background to all potential containers');
          allContainers.forEach((el, index) => {
            (el as HTMLElement).style.backgroundImage = `url(${fullImageUrl})`;
            console.log(`Applied background to potential container ${index}`);
          });
        }
      }

      if (uploadInterval) clearInterval(uploadInterval);
      setUploadProgress(100);

      // Reset state after successful upload
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 500);

    } catch (err) {
      if (uploadInterval) clearInterval(uploadInterval);
      setError('Failed to upload image. Please try again.');
      setIsUploading(false);
      setUploadProgress(0);
      console.error('Background image upload error:', err);
    }
  }, [onImageUploaded]);

  // Handle property changes
  const handlePropertyChange = (property: keyof BackgroundProperties, value: string) => {
    console.log(`Changing ${property} to ${value}`);
    const newProperties = {
      ...properties,
      [property]: value
    } as BackgroundProperties;

    setProperties(newProperties);
    onPropertiesChanged(newProperties);

    // Log the updated properties
    console.log('Updated background properties:', newProperties);
  };

  // Prepare the current image URL for display
  const displayImageUrl = currentImage && !currentImage.startsWith('http') && !currentImage.startsWith('data:')
    ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${currentImage}`
    : currentImage;

  console.log('BackgroundImageUploader: displayImageUrl:', displayImageUrl);

  return (
    <div className={`w-full ${className}`}>
      {/* Debug information removed */}

      <div className="flex items-center justify-between mb-2">
        <Label className="text-sm font-medium">Page Background</Label>
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-8 w-8"
          onClick={() => setShowSettings(!showSettings)}
        >
          <Settings size={16} className={showSettings ? "text-primary" : "text-muted-foreground"} />
        </Button>
      </div>

      {/* Use only the ImageUploader component to handle both display and upload */}
      <ImageUploader
        type="background"
        onImageSelect={handleImageSelect}
        currentImage={displayImageUrl} // Usa displayImageUrl invece di currentImage
        className="mb-4"
      />

      {isUploading && (
        <div className="mt-4">
          <div className="w-full bg-secondary rounded-full h-2.5">
            <div
              className="bg-primary h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-sm text-center mt-2 text-muted-foreground">
            Uploading image... {uploadProgress}%
          </p>
        </div>
      )}

      {error && (
        <p className="text-sm text-red-500 mt-2">{error}</p>
      )}

      {showSettings && (
        <div className="mt-4 space-y-4 p-4 border rounded-md bg-muted/20">
          <div>
            <Label htmlFor="position" className="text-xs block mb-1">Background Position</Label>
            <Select
              value={properties.backgroundPosition}
              onValueChange={(value) => handlePropertyChange('backgroundPosition', value as any)}
            >
              <SelectTrigger id="position" className="w-full">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent className="z-[500]"> {/* Significantly increased z-index to ensure visibility */}
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="repeat" className="text-xs block mb-1">Background Repeat</Label>
            <Select
              value={properties.backgroundRepeat}
              onValueChange={(value) => handlePropertyChange('backgroundRepeat', value as any)}
            >
              <SelectTrigger id="repeat" className="w-full">
                <SelectValue placeholder="Select repeat" />
              </SelectTrigger>
              <SelectContent className="z-[500]"> {/* Significantly increased z-index to ensure visibility */}
                <SelectItem value="no-repeat">No Repeat</SelectItem>
                <SelectItem value="repeat">Repeat</SelectItem>
                <SelectItem value="space">Space</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="size" className="text-xs block mb-1">Size</Label>
            <Select
              value={properties.backgroundSize}
              onValueChange={(value) => handlePropertyChange('backgroundSize', value as any)}
            >
              <SelectTrigger id="size" className="w-full">
                <SelectValue placeholder="Select size" />
              </SelectTrigger>
              <SelectContent className="z-[500]"> {/* Significantly increased z-index to ensure visibility */}
                <SelectItem value="auto">Auto</SelectItem>
                <SelectItem value="cover">Cover</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="attachment" className="text-xs block mb-1">Attachment</Label>
            <Select
              value={properties.backgroundAttachment}
              onValueChange={(value) => handlePropertyChange('backgroundAttachment', value as any)}
            >
              <SelectTrigger id="attachment" className="w-full">
                <SelectValue placeholder="Select attachment" />
              </SelectTrigger>
              <SelectContent className="z-[500]"> {/* Significantly increased z-index to ensure visibility */}
                <SelectItem value="fixed">Fixed</SelectItem>
                <SelectItem value="scroll">Scroll</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  );
};
