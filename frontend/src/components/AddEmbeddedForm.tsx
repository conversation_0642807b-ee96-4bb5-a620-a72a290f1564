import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from './ui/tabs';
import { YouTubeEmbed } from './YouTubeEmbed';
import { TikTokEmbed } from './TikTokEmbed';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';

interface AddEmbeddedFormProps {
  onAdd: (
    title: string,
    embedType: 'youtube' | 'tiktok',
    autoplay: boolean,
    muted: boolean,
    showControls: boolean,
    youtubeCode?: string,
    tikTokUrl?: string,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string,
    visibilityDates?: {
      startDate?: string;
      endDate?: string;
      enabled?: boolean;
    }
  ) => void;
}

const AddEmbeddedForm = ({ onAdd }: AddEmbeddedFormProps) => {
  const [title, setTitle] = useState('');
  const [embedType, setEmbedType] = useState<'youtube' | 'tiktok'>('youtube');
  const [youtubeCode, setYoutubeCode] = useState('');
  const [tikTokUrl, setTikTokUrl] = useState('');
  const [autoplay, setAutoplay] = useState(false);
  const [muted, setMuted] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  // Advanced options
  const [password, setPassword] = useState('');
  const [passwordEnabled, setPasswordEnabled] = useState(false);
  const [visibilityDatesEnabled, setVisibilityDatesEnabled] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [passwordExpiryDate, setPasswordExpiryDate] = useState<Date | undefined>(undefined);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate based on the selected embed type
    const isValid =
      title.trim() &&
      ((embedType === 'youtube' && youtubeCode.trim()) ||
       (embedType === 'tiktok' && tikTokUrl.trim()));

    if (isValid) {
      // Prepare visibility dates
      const visibilityDates = visibilityDatesEnabled
        ? {
            startDate: startDate ? startDate.toISOString() : undefined,
            endDate: endDate ? endDate.toISOString() : undefined,
            enabled: visibilityDatesEnabled,
          }
        : undefined;

      onAdd(
        title,
        embedType,
        autoplay,
        muted,
        showControls,
        embedType === 'youtube' ? youtubeCode : undefined,
        embedType === 'tiktok' ? tikTokUrl : undefined,
        password,
        passwordEnabled,
        passwordEnabled && passwordExpiryDate ? passwordExpiryDate.toISOString() : undefined,
        visibilityDates
      );

      // Reset form
      setTitle('');
      setEmbedType('youtube');
      setYoutubeCode('');
      setTikTokUrl('');
      setAutoplay(false);
      setMuted(true);
      setShowControls(true);
      setPassword('');
      setPasswordEnabled(false);
      setVisibilityDatesEnabled(false);
      setStartDate(undefined);
      setEndDate(undefined);
      setPasswordExpiryDate(undefined);
      setIsExpanded(false);
    }
  };

  // Extract YouTube video ID from various YouTube URL formats
  const handleYoutubeUrlChange = (url: string) => {
    setYoutubeCode(url);

    // Try to extract YouTube video ID if a full URL was pasted
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      try {
        let videoId = '';

        if (url.includes('youtube.com/watch')) {
          // Format: https://www.youtube.com/watch?v=VIDEO_ID
          const urlObj = new URL(url);
          videoId = urlObj.searchParams.get('v') || '';
        } else if (url.includes('youtu.be')) {
          // Format: https://youtu.be/VIDEO_ID
          const urlParts = url.split('/');
          videoId = urlParts[urlParts.length - 1].split('?')[0];
        } else if (url.includes('youtube.com/embed')) {
          // Format: https://www.youtube.com/embed/VIDEO_ID
          const urlParts = url.split('/');
          videoId = urlParts[urlParts.length - 1].split('?')[0];
        }

        if (videoId) {
          setYoutubeCode(videoId);
        }
      } catch (error) {
        // If URL parsing fails, keep the original input
        console.error('Failed to parse YouTube URL:', error);
      }
    }
  };

  // Handle TikTok URL changes
  const handleTikTokUrlChange = (url: string) => {
    setTikTokUrl(url);

    // Validate that it's a TikTok URL
    if (!url.includes('tiktok.com')) {
      // If not a TikTok URL, check if it's a full URL and try to extract the video ID
      try {
        // Try to create a URL object to validate it's a proper URL
        new URL(url);
        // If it doesn't throw an error, it's a valid URL but not TikTok
        console.warn('Not a TikTok URL:', url);
      } catch (error) {
        // Not a valid URL, might be just text
        console.warn('Not a valid URL:', url);
      }
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Add Embedded Video</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          {isExpanded ? (
            <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="options">Options</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4">
                <div>
                  <Label htmlFor="title" className="block text-sm font-medium mb-1">
                    Title
                  </Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Video Title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Embed Type</Label>
                  <RadioGroup
                    value={embedType}
                    onValueChange={(value: string) => setEmbedType(value as 'youtube' | 'tiktok')}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="youtube" id="youtube-add" />
                      <Label htmlFor="youtube-add">YouTube</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="tiktok" id="tiktok-add" />
                      <Label htmlFor="tiktok-add">TikTok</Label>
                    </div>
                  </RadioGroup>
                </div>

                {embedType === 'youtube' && (
                  <div>
                    <Label htmlFor="youtubeCode" className="block text-sm font-medium mb-1">
                      YouTube Video URL or Code
                    </Label>
                    <Input
                      id="youtubeCode"
                      value={youtubeCode}
                      onChange={(e) => handleYoutubeUrlChange(e.target.value)}
                      placeholder="https://www.youtube.com/watch?v=VIDEO_ID or just VIDEO_ID"
                      required={embedType === 'youtube'}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Paste a YouTube URL or just the video ID
                    </p>
                  </div>
                )}

                {embedType === 'tiktok' && (
                  <div>
                    <Label htmlFor="tikTokUrl" className="block text-sm font-medium mb-1">
                      TikTok Video URL
                    </Label>
                    <Input
                      id="tikTokUrl"
                      value={tikTokUrl}
                      onChange={(e) => handleTikTokUrlChange(e.target.value)}
                      placeholder="https://www.tiktok.com/@username/video/1234567890123456789"
                      required={embedType === 'tiktok'}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Paste a TikTok video URL
                    </p>
                  </div>
                )}

                {/* Preview section */}
                {embedType === 'youtube' && youtubeCode && (
                  <div className="mt-4">
                    <Label className="block text-sm font-medium mb-1">Preview</Label>
                    <YouTubeEmbed
                      youtubeCode={youtubeCode}
                      title={title}
                      autoplay={false}
                      muted={muted}
                      showControls={showControls}
                    />
                  </div>
                )}

                {embedType === 'tiktok' && tikTokUrl && (
                  <div className="mt-4">
                    <Label className="block text-sm font-medium mb-1">Preview</Label>
                    <TikTokEmbed
                      tikTokUrl={tikTokUrl}
                      title={title}
                    />
                  </div>
                )}
              </TabsContent>

              <TabsContent value="options" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoplay">Autoplay</Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically play the video when the page loads
                    </p>
                  </div>
                  <Switch
                    id="autoplay"
                    checked={autoplay}
                    onCheckedChange={setAutoplay}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="muted">Muted</Label>
                    <p className="text-xs text-muted-foreground">
                      Start the video with sound muted
                    </p>
                  </div>
                  <Switch
                    id="muted"
                    checked={muted}
                    onCheckedChange={setMuted}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="showControls">Show Controls</Label>
                    <p className="text-xs text-muted-foreground">
                      Display video player controls
                    </p>
                  </div>
                  <Switch
                    id="showControls"
                    checked={showControls}
                    onCheckedChange={setShowControls}
                  />
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="passwordEnabled">Password Protection</Label>
                    <p className="text-xs text-muted-foreground">
                      Require a password to view this content
                    </p>
                  </div>
                  <Switch
                    id="passwordEnabled"
                    checked={passwordEnabled}
                    onCheckedChange={setPasswordEnabled}
                  />
                </div>

                {passwordEnabled && (
                  <div>
                    <Label htmlFor="password" className="block text-sm font-medium mb-1">
                      Password
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter password"
                      required={passwordEnabled}
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="visibilityDatesEnabled">Scheduled Display</Label>
                    <p className="text-xs text-muted-foreground">
                      Set a date range when this content will be visible
                    </p>
                  </div>
                  <Switch
                    id="visibilityDatesEnabled"
                    checked={visibilityDatesEnabled}
                    onCheckedChange={setVisibilityDatesEnabled}
                  />
                </div>

                {visibilityDatesEnabled && (
                  <>
                    <div className="space-y-1">
                      <Label htmlFor="startDate" className="text-xs text-muted-foreground">
                        Start Date (Optional)
                      </Label>
                      {/* DateTimePicker component would be used here */}
                      <p className="text-xs text-muted-foreground">
                        Date picker will be implemented
                      </p>
                    </div>

                    <div className="space-y-1">
                      <Label htmlFor="endDate" className="text-xs text-muted-foreground">
                        End Date (Optional)
                      </Label>
                      {/* DateTimePicker component would be used here */}
                      <p className="text-xs text-muted-foreground">
                        Date picker will be implemented
                      </p>
                    </div>
                  </>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => setIsExpanded(true)}
            >
              + Add Embedded Video
            </Button>
          )}
        </CardContent>
        {isExpanded && (
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!title || (embedType === 'youtube' && !youtubeCode) || (embedType === 'tiktok' && !tikTokUrl)}
            >
              Add Embedded Video
            </Button>
          </CardFooter>
        )}
      </form>
    </Card>
  );
};

export default AddEmbeddedForm;
