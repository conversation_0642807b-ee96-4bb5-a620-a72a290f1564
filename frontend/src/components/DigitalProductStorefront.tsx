import { useState } from 'react';
import { <PERSON> } from '../api/links';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { ShoppingBag, Download, FileText, Package } from 'lucide-react';

interface DigitalProductStorefrontProps {
  link: Link;
  isPreview?: boolean;
}

const DigitalProductStorefront = ({ link, isPreview = false }: DigitalProductStorefrontProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handlePurchase = async () => {
    if (isPreview) {
      alert('This is a preview. Purchase functionality is not available.');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement purchase flow
      // This would typically:
      // 1. Create a purchase intent
      // 2. Redirect to payment processor
      // 3. Handle success/failure callbacks
      console.log('Initiating purchase for:', link.title);
      
      // Mock purchase flow
      setTimeout(() => {
        alert('Purchase flow would be initiated here');
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Purchase failed:', error);
      setIsLoading(false);
    }
  };

  const getProductTypeIcon = (type: string) => {
    switch (type) {
      case 'template':
        return <FileText className="h-5 w-5" />;
      case 'software':
        return <Package className="h-5 w-5" />;
      default:
        return <Download className="h-5 w-5" />;
    }
  };

  const getProductTypeColor = (type: string) => {
    switch (type) {
      case 'template':
        return 'bg-blue-100 text-blue-800';
      case 'preset':
        return 'bg-purple-100 text-purple-800';
      case 'guide':
        return 'bg-green-100 text-green-800';
      case 'ebook':
        return 'bg-orange-100 text-orange-800';
      case 'course':
        return 'bg-red-100 text-red-800';
      case 'software':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto overflow-hidden hover:shadow-lg transition-shadow">
      {/* Product Images */}
      {link.previewImages && link.previewImages.length > 0 && (
        <div className="relative h-48 bg-gradient-to-br from-purple-100 to-blue-100">
          <img
            src={link.previewImages[0]}
            alt={link.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to gradient background if image fails to load
              e.currentTarget.style.display = 'none';
            }}
          />
          <div className="absolute top-3 right-3">
            <Badge className={getProductTypeColor(link.productType || 'other')}>
              <div className="flex items-center gap-1">
                {getProductTypeIcon(link.productType || 'other')}
                <span className="capitalize">{link.productType || 'Digital Product'}</span>
              </div>
            </Badge>
          </div>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg leading-tight">{link.title}</CardTitle>
          <div className="text-right ml-3">
            <div className="text-2xl font-bold text-primary">
              {link.currency} {link.price}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {link.description && (
          <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
            {link.description}
          </p>
        )}

        {/* Product Details */}
        <div className="flex flex-wrap gap-2 mb-4 text-xs text-muted-foreground">
          {link.fileFormat && (
            <Badge variant="outline" className="text-xs">
              {link.fileFormat}
            </Badge>
          )}
          {link.fileSize && (
            <Badge variant="outline" className="text-xs">
              {link.fileSize}
            </Badge>
          )}
        </div>

        {/* Purchase Button */}
        <Button 
          onClick={handlePurchase}
          disabled={isLoading}
          className="w-full"
          size="lg"
        >
          <ShoppingBag className="h-4 w-4 mr-2" />
          {isLoading ? 'Processing...' : `Buy Now - ${link.currency} ${link.price}`}
        </Button>

        {/* Additional Info */}
        <div className="mt-3 text-xs text-muted-foreground text-center">
          <p>Instant download after purchase</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default DigitalProductStorefront;
