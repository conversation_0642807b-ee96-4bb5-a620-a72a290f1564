import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { ShuffleCard } from '../api/links';
import { ProfileImageUploader } from './ProfileImageUploader';
import { Trash2, Plus } from 'lucide-react';

interface AddShuffleCardsFormProps {
  onAdd: (title: string, shuffleCards: ShuffleCard[]) => void;
}

const AddShuffleCardsForm = ({ onAdd }: AddShuffleCardsFormProps) => {
  const [title, setTitle] = useState('');
  const [cards, setCards] = useState<ShuffleCard[]>([
    { imageUrl: '', title: '', url: '', tag: '' }
  ]);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all cards have required fields
    const isValid = cards.every(card =>
      card.imageUrl.trim() !== '' &&
      card.title.trim() !== '' &&
      card.url.trim() !== ''
    );

    if (title.trim() && isValid) {
      onAdd(title, cards);
      setTitle('');
      setCards([{ imageUrl: '', title: '', url: '', tag: '' }]);
      setIsExpanded(false);
    }
  };

  const handleCardChange = (index: number, field: keyof ShuffleCard, value: string) => {
    const updatedCards = [...cards];
    updatedCards[index] = { ...updatedCards[index], [field]: value };
    setCards(updatedCards);
  };

  const handleImageUploaded = (index: number, imageUrl: string) => {
    handleCardChange(index, 'imageUrl', imageUrl);
  };

  const addCard = () => {
    if (cards.length < 3) {
      setCards([...cards, { imageUrl: '', title: '', url: '', tag: '' }]);
    }
  };

  const removeCard = (index: number) => {
    if (cards.length > 1) {
      const updatedCards = [...cards];
      updatedCards.splice(index, 1);
      setCards(updatedCards);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Add Card</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          {isExpanded ? (
            <div className="space-y-6">
              <div>
                <Label htmlFor="title" className="block text-sm font-medium mb-1">
                  Section Title
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Card Section Title"
                  required
                />
              </div>

              {cards.map((card, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium">Card {index + 1}</h3>
                    {cards.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeCard(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div>
                    <Label className="block text-sm font-medium mb-1">
                      Card Image
                    </Label>
                    <ProfileImageUploader
                      type="shuffle"
                      onImageUploaded={(url) => handleImageUploaded(index, url)}
                      currentImage={card.imageUrl}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      For best results, use portrait mode images (taller than wide) for Tinder-like swiping
                    </p>
                  </div>

                  <div>
                    <Label htmlFor={`card-title-${index}`} className="block text-sm font-medium mb-1">
                      Card Title
                    </Label>
                    <Input
                      id={`card-title-${index}`}
                      value={card.title}
                      onChange={(e) => handleCardChange(index, 'title', e.target.value)}
                      placeholder="Card Title"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor={`card-tag-${index}`} className="block text-sm font-medium mb-1">
                      Tag (Optional)
                    </Label>
                    <Input
                      id={`card-tag-${index}`}
                      value={card.tag || ''}
                      onChange={(e) => handleCardChange(index, 'tag', e.target.value)}
                      placeholder="Featured, New, etc."
                    />
                  </div>

                  <div>
                    <Label htmlFor={`card-url-${index}`} className="block text-sm font-medium mb-1">
                      URL
                    </Label>
                    <Input
                      id={`card-url-${index}`}
                      value={card.url}
                      onChange={(e) => handleCardChange(index, 'url', e.target.value)}
                      placeholder="https://example.com"
                      required
                    />
                  </div>
                </div>
              ))}

              {cards.length < 3 && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={addCard}
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Another Card
                </Button>
              )}
            </div>
          ) : (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => setIsExpanded(true)}
            >
              + Add Card
            </Button>
          )}
        </CardContent>
        {isExpanded && (
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
            <Button type="submit">Add Card</Button>
          </CardFooter>
        )}
      </form>
    </Card>
  );
};

export default AddShuffleCardsForm;
