import { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface AddDigitalProductFormProps {
  onAdd: (
    title: string,
    description: string,
    productType: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other',
    price: number,
    currency: string,
    downloadUrl: string,
    previewImages: string[],
    fileSize: string,
    fileFormat: string
  ) => void;
}

const AddDigitalProductForm = ({ onAdd }: AddDigitalProductFormProps) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [productType, setProductType] = useState<'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other'>('other');
  const [price, setPrice] = useState('');
  const [currency, setCurrency] = useState('USD');
  const [downloadUrl, setDownloadUrl] = useState('');
  const [previewImages, setPreviewImages] = useState('');
  const [fileSize, setFileSize] = useState('');
  const [fileFormat, setFileFormat] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && description.trim() && price && downloadUrl.trim()) {
      const previewImageArray = previewImages
        .split(',')
        .map(url => url.trim())
        .filter(url => url.length > 0);

      onAdd(
        title,
        description,
        productType,
        parseFloat(price),
        currency,
        downloadUrl,
        previewImageArray,
        fileSize,
        fileFormat
      );
      
      // Reset form
      setTitle('');
      setDescription('');
      setProductType('other');
      setPrice('');
      setCurrency('USD');
      setDownloadUrl('');
      setPreviewImages('');
      setFileSize('');
      setFileFormat('');
    }
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Add Digital Product</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="product-title">Product Title</Label>
          <Input
            id="product-title"
            placeholder="My Amazing Template"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1"
            required
          />
        </div>

        <div>
          <Label htmlFor="product-description">Description</Label>
          <Textarea
            id="product-description"
            placeholder="Describe your digital product..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="mt-1"
            rows={3}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="product-type">Product Type</Label>
            <Select value={productType} onValueChange={(value: any) => setProductType(value)}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="template">Template</SelectItem>
                <SelectItem value="preset">Preset</SelectItem>
                <SelectItem value="guide">Guide</SelectItem>
                <SelectItem value="ebook">E-book</SelectItem>
                <SelectItem value="course">Course</SelectItem>
                <SelectItem value="software">Software</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="product-currency">Currency</Label>
            <Select value={currency} onValueChange={setCurrency}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="GBP">GBP</SelectItem>
                <SelectItem value="CAD">CAD</SelectItem>
                <SelectItem value="AUD">AUD</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="product-price">Price</Label>
          <Input
            id="product-price"
            type="number"
            step="0.01"
            min="0"
            placeholder="9.99"
            value={price}
            onChange={(e) => setPrice(e.target.value)}
            className="mt-1"
            required
          />
        </div>

        <div>
          <Label htmlFor="download-url">Download URL</Label>
          <Input
            id="download-url"
            type="url"
            placeholder="https://example.com/download/product.zip"
            value={downloadUrl}
            onChange={(e) => setDownloadUrl(e.target.value)}
            className="mt-1"
            required
          />
        </div>

        <div>
          <Label htmlFor="preview-images">Preview Images (comma-separated URLs)</Label>
          <Textarea
            id="preview-images"
            placeholder="https://example.com/image1.jpg, https://example.com/image2.jpg"
            value={previewImages}
            onChange={(e) => setPreviewImages(e.target.value)}
            className="mt-1"
            rows={2}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="file-size">File Size</Label>
            <Input
              id="file-size"
              placeholder="2.5 MB"
              value={fileSize}
              onChange={(e) => setFileSize(e.target.value)}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="file-format">File Format</Label>
            <Input
              id="file-format"
              placeholder="ZIP, PDF, PSD, etc."
              value={fileFormat}
              onChange={(e) => setFileFormat(e.target.value)}
              className="mt-1"
            />
          </div>
        </div>

        <Button type="submit" className="w-full">
          Add Digital Product
        </Button>
      </form>
    </div>
  );
};

export default AddDigitalProductForm;
