import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { updateProfile, updateSlug, getQRCode } from '../api/user';
import { getLinks, Link as LinkType } from '../api/links';
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from './ui/tabs';
import { ProfileImageUploader } from './ProfileImageUploader';
import { ColorPicker } from './ColorPicker';
import { ProfilePreview } from './ProfilePreview';
import { SearchableGoogleFontSelector } from './SearchableGoogleFontSelector';
import RichTextEditor from './RichTextEditor';
import { Download } from 'lucide-react';
import SEOSettings from './SEOSettings';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface ProfileFormProps {
  activeTab?: string;
}

const ProfileForm = ({ activeTab: initialActiveTab }: ProfileFormProps = {}) => {
  const { user } = useAuth();
  const [username, setUsername] = useState('');
  const [bio, setBio] = useState('');
  const [slug, setSlug] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [avatarPosition, setAvatarPosition] = useState<'left' | 'center' | 'right'>('center');
  const [avatarShape, setAvatarShape] = useState<'circle' | 'square'>('circle');
  const [bannerUrl, setBannerUrl] = useState('');
  const [bannerColor, setBannerColor] = useState('#e0f2fe');
  const [pageBackgroundColor, setPageBackgroundColor] = useState('#f8f9fa');
  const [cardBackgroundColor, setCardBackgroundColor] = useState('#ffffff');
  const [fontColor, setFontColor] = useState('#000000');
  const [fontFamily, setFontFamily] = useState('');
  const [backgroundImage, setBackgroundImage] = useState('');
  const [backgroundPosition, setBackgroundPosition] = useState<'left' | 'center' | 'right' | 'top' | 'bottom'>('center');
  const [backgroundRepeat, setBackgroundRepeat] = useState<'no-repeat' | 'repeat' | 'space'>('no-repeat');
  const [backgroundSize, setBackgroundSize] = useState<'auto' | 'cover'>('cover');
  const [backgroundAttachment, setBackgroundAttachment] = useState<'fixed' | 'scroll'>('scroll');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [activeTab, setActiveTab] = useState(initialActiveTab || 'profile');
  const [links, setLinks] = useState<LinkType[]>([]);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [profileUrl, setProfileUrl] = useState<string>('');
  const [isLoadingQR, setIsLoadingQR] = useState(false);

  useEffect(() => {
    if (user) {
      setUsername(user.username || '');
      setSlug(user.slug || '');
      // These would come from the profile endpoint
      setBio(user.bio || '');
      setAvatarUrl(user.avatarUrl || '');
      setAvatarPosition(user.avatarPosition as 'left' | 'center' | 'right' || 'center');
      setAvatarShape(user.avatarShape as 'circle' | 'square' || 'circle');
      setBannerUrl(user.bannerUrl || '');
      setBannerColor(user.bannerColor || '#e0f2fe');
      setPageBackgroundColor(user.pageBackgroundColor || '#f8f9fa');
      setCardBackgroundColor(user.cardBackgroundColor || '#ffffff');
      setFontColor(user.fontColor || '#000000');
      setFontFamily(user.fontFamily || '');
      setBackgroundImage(user.backgroundImage || '');
      setBackgroundPosition(user.backgroundPosition as 'left' | 'center' | 'right' | 'top' | 'bottom' || 'center');
      setBackgroundRepeat(user.backgroundRepeat as 'no-repeat' | 'repeat' | 'space' || 'no-repeat');
      setBackgroundSize(user.backgroundSize as 'auto' | 'cover' || 'cover');
      setBackgroundAttachment(user.backgroundAttachment as 'fixed' | 'scroll' || 'scroll');
    }
  }, [user]);

  // Fetch user's active links
  useEffect(() => {
    const fetchLinks = async () => {
      try {
        const activeLinks = await getLinks(false);
        setLinks(activeLinks);
      } catch (error) {
        console.error('Failed to load links:', error);
      }
    };

    fetchLinks();
  }, []);

  // Fetch QR code when the tab changes to 'qrcode'
  useEffect(() => {
    if (activeTab === 'qrcode' && !qrCode) {
      fetchQRCode();
    }
  }, [activeTab, qrCode]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    // Log the profile data being saved
    console.log('ProfileForm: Saving profile with data:', {
      username,
      bio,
      avatarUrl,
      avatarPosition,
      avatarShape,
      bannerUrl,
      bannerColor,
      pageBackgroundColor,
      cardBackgroundColor,
      fontColor,
      fontFamily,
      backgroundImage,
      backgroundPosition,
      backgroundRepeat,
      backgroundSize,
      backgroundAttachment
    });

    try {
      await updateProfile({
        username,
        bio,
        avatarUrl,
        avatarPosition,
        avatarShape,
        bannerUrl,
        bannerColor,
        pageBackgroundColor,
        cardBackgroundColor,
        fontColor,
        fontFamily,
        backgroundImage,
        backgroundPosition,
        backgroundRepeat,
        backgroundSize,
        backgroundAttachment
      });
      setMessage('Profile updated successfully!');

      // Force a re-render to ensure the background image is updated in the preview
      setTimeout(() => {
        console.log('ProfileForm: Forcing re-render after profile update');
        // This is a trick to force a re-render
        setBackgroundImage(prev => prev);
      }, 100);
    } catch (error: any) {
      setMessage(`Error: ${error.response?.data?.message || 'Failed to update profile'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarUploaded = (imageUrl: string) => {
    setAvatarUrl(imageUrl);
    setMessage('Profile picture uploaded successfully!');
  };

  const handleBannerUploaded = (imageUrl: string) => {
    setBannerUrl(imageUrl);
    setMessage('Banner image uploaded successfully!');
  };

  const handleBackgroundImageUploaded = async (imageUrl: string) => {
    console.log('ProfileForm: Background image uploaded:', imageUrl);
    setBackgroundImage(imageUrl);
    setMessage('Background image uploaded successfully!');

    // Force a re-render to ensure the background image is updated in the preview
    setTimeout(() => {
      console.log('ProfileForm: Forcing re-render after background image change');
      // This is a trick to force a re-render
      setBackgroundImage(prev => prev);
    }, 100);

    // Automatically save the profile with the new background image
    try {
      console.log('ProfileForm: Auto-saving profile with new background image:', imageUrl);
      setIsLoading(true);
      await updateProfile({
        backgroundImage: imageUrl,
        backgroundPosition,
        backgroundRepeat,
        backgroundSize,
        backgroundAttachment
      });
      console.log('ProfileForm: Profile auto-saved successfully with new background image');
    } catch (error: any) {
      console.error('ProfileForm: Error auto-saving profile with new background image:', error);
      setMessage(`Error: ${error.response?.data?.message || 'Failed to save background image'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackgroundPropertiesChanged = async (properties: {
    backgroundPosition: 'left' | 'center' | 'right' | 'top' | 'bottom';
    backgroundRepeat: 'no-repeat' | 'repeat' | 'space';
    backgroundSize: 'auto' | 'cover';
    backgroundAttachment: 'fixed' | 'scroll';
  }) => {
    setBackgroundPosition(properties.backgroundPosition);
    setBackgroundRepeat(properties.backgroundRepeat);
    setBackgroundSize(properties.backgroundSize);
    setBackgroundAttachment(properties.backgroundAttachment);

    // Automatically save the profile with the new background properties
    try {
      console.log('ProfileForm: Auto-saving profile with new background properties:', properties);
      setIsLoading(true);
      await updateProfile({
        backgroundImage,
        backgroundPosition: properties.backgroundPosition,
        backgroundRepeat: properties.backgroundRepeat,
        backgroundSize: properties.backgroundSize,
        backgroundAttachment: properties.backgroundAttachment
      });
      console.log('ProfileForm: Profile auto-saved successfully with new background properties');
      setMessage('Background properties updated successfully!');
    } catch (error: any) {
      console.error('ProfileForm: Error auto-saving profile with new background properties:', error);
      setMessage(`Error: ${error.response?.data?.message || 'Failed to save background properties'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchQRCode = async () => {
    try {
      setIsLoadingQR(true);
      const response = await getQRCode();
      setQrCode(response.qrCode);
      setProfileUrl(response.profileUrl);
    } catch (error: any) {
      setMessage(`Error: ${error.response?.data?.message || 'Failed to generate QR code'}`);
    } finally {
      setIsLoadingQR(false);
    }
  };

  const handleDownloadQRCode = () => {
    if (!qrCode) return;

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = qrCode;
    link.download = `${slug || 'profile'}-qrcode.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleSlugUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      await updateSlug({ slug });
      setMessage('URL slug updated successfully!');
    } catch (error: any) {
      setMessage(`Error: ${error.response?.data?.message || 'Failed to update slug'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">Info</TabsTrigger>
          <TabsTrigger value="images">Images&Font</TabsTrigger>
          <TabsTrigger value="url">Custom URL</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
          <TabsTrigger value="qrcode">QR Code</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <div className="grid md:grid-cols-5 gap-4"> {/* Changed from gap-6 to gap-4 to reduce spacing */}
            <div className="md:col-span-3"> {/* Takes 3/5 of the space (increased edit column) */}
            <Card>
              <CardHeader>
                <CardTitle>Information</CardTitle>
                <CardDescription>
                  Update your profile details that will be displayed on your public page.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleProfileUpdate}>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="username" className="block text-sm font-medium mb-1">
                      Username
                    </label>
                    <Input
                      id="username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      placeholder="Your name"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <label htmlFor="bio" className="block text-sm font-medium">
                        Bio
                      </label>
                    </div>
                    <RichTextEditor
                      value={bio}
                      onChange={setBio}
                      placeholder="A short bio about yourself"
                      className="min-h-[120px]"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      A brief description that will appear on your profile. You can formattare il testo usando gli strumenti sopra.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-2">Font Color</h3>
                    <ColorPicker
                      color={fontColor}
                      onChange={setFontColor}
                      label="Select a color for your text"
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      This color will be used for the text on your profile page.
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? 'Saving...' : 'Save Profile'}
                  </Button>
                </CardFooter>
              </form>
            </Card>
            </div>

            {/* Real-time preview */}
            <div className="md:col-span-2"> {/* Takes 2/5 of the space (reduced preview column) */}
              <h3 className="text-lg font-medium mb-4">Live Preview</h3>
              <div className="sticky top-6">
                <div className="w-full overflow-hidden rounded-lg"> {/* Full width container for background color */}
                  <ProfilePreview
                  username={username}
                  slug={slug}
                  bio={bio}
                  avatarUrl={avatarUrl}
                  avatarPosition={avatarPosition}
                  avatarShape={avatarShape}
                  bannerUrl={bannerUrl}
                  bannerColor={bannerColor}
                  pageBackgroundColor={pageBackgroundColor}
                  cardBackgroundColor={cardBackgroundColor}
                  fontColor={fontColor}
                  fontFamily={fontFamily}
                  backgroundImage={backgroundImage}
                  backgroundPosition={backgroundPosition}
                  backgroundRepeat={backgroundRepeat}
                  backgroundSize={backgroundSize}
                  backgroundAttachment={backgroundAttachment}
                  links={links}
                  showCountrySelector={false}
                  verifiedProfile={user?.verifiedProfile}
                  verifiedProfileGold={user?.verifiedProfileGold}
                  onBackgroundImageChange={handleBackgroundImageUploaded}
                  onBackgroundPropertiesChange={handleBackgroundPropertiesChanged}
                  showBackgroundButton={false}
                />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="seo">
          <div className="grid md:grid-cols-5 gap-4">
            <div className="md:col-span-3">
              <SEOSettings />
            </div>

            <div className="md:col-span-2">
              <h3 className="text-lg font-medium mb-4">Live Preview</h3>
              <div className="sticky top-6">
                <div className="w-full overflow-hidden rounded-lg"> {/* Full width container for background color */}
                  <ProfilePreview
                    username={username}
                    slug={slug}
                    bio={bio}
                    avatarUrl={avatarUrl}
                    avatarPosition={avatarPosition}
                    avatarShape={avatarShape}
                    bannerUrl={bannerUrl}
                    bannerColor={bannerColor}
                    pageBackgroundColor={pageBackgroundColor}
                    cardBackgroundColor={cardBackgroundColor}
                    fontColor={fontColor}
                    fontFamily={fontFamily}
                    backgroundImage={backgroundImage}
                    backgroundPosition={backgroundPosition}
                    backgroundRepeat={backgroundRepeat}
                    backgroundSize={backgroundSize}
                    backgroundAttachment={backgroundAttachment}
                    links={links}
                    showCountrySelector={false}
                    verifiedProfile={user?.verifiedProfile}
                    verifiedProfileGold={user?.verifiedProfileGold}
                    onBackgroundImageChange={handleBackgroundImageUploaded}
                    onBackgroundPropertiesChange={handleBackgroundPropertiesChanged}
                    showBackgroundButton={false}
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="images">
          <div className="grid md:grid-cols-5 gap-4"> {/* Changed from gap-6 to gap-4 to reduce spacing */}
            <div className="md:col-span-3"> {/* Takes 3/5 of the space (increased edit column) */}
            <Card>
              <CardHeader>
                <CardTitle>Images & Font</CardTitle>
                <CardDescription>
                  Upload and manage your profile picture, banner image, and font settings.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Profile Picture</h3>
                    <ProfileImageUploader
                      type="avatar"
                      onImageUploaded={handleAvatarUploaded}
                      currentImage={avatarUrl}
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      Recommended size: 400x400px. Max file size: 5MB.
                    </p>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">Avatar Position</h4>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant={avatarPosition === 'left' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAvatarPosition('left')}
                        >
                          Left
                        </Button>
                        <Button
                          type="button"
                          variant={avatarPosition === 'center' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAvatarPosition('center')}
                        >
                          Center
                        </Button>
                        <Button
                          type="button"
                          variant={avatarPosition === 'right' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAvatarPosition('right')}
                        >
                          Right
                        </Button>
                      </div>
                    </div>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">Avatar Shape</h4>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant={avatarShape === 'circle' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAvatarShape('circle')}
                        >
                          Circle
                        </Button>
                        <Button
                          type="button"
                          variant={avatarShape === 'square' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAvatarShape('square')}
                        >
                          Square
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Banner Image</h3>
                    <ProfileImageUploader
                      type="banner"
                      onImageUploaded={handleBannerUploaded}
                      currentImage={bannerUrl}
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      Recommended size: 1200x400px. Max file size: 5MB.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Banner Background Color</h3>
                    <ColorPicker
                      color={bannerColor}
                      onChange={setBannerColor}
                      label="Select a background color for your banner"
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      This color will be used as the background for your banner. If you upload a banner image, this color will still be visible if the image has transparency.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Page Background Color</h3>
                    <ColorPicker
                      color={pageBackgroundColor}
                      onChange={setPageBackgroundColor}
                      label="Select a background color for your page"
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      This color will be used as the background for your entire profile page.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Card Background Color</h3>
                    <ColorPicker
                      color={cardBackgroundColor}
                      onChange={setCardBackgroundColor}
                      label="Select a background color for your link cards"
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      This color will be used as the background for your link cards.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Custom Background</h3>
                    <div className="mb-4">
                      <ProfileImageUploader
                        type="background"
                        onImageUploaded={handleBackgroundImageUploaded}
                        currentImage={backgroundImage}
                      />
                      <p className="text-xs text-muted-foreground mt-2">
                        Upload an image to use as a custom background for your profile page.
                      </p>
                    </div>

                    <div className="space-y-4 mt-6">
                      <div>
                        <Label htmlFor="bg-position" className="text-sm font-medium mb-2">Background Position</Label>
                        <Select
                          value={backgroundPosition}
                          onValueChange={(value) => setBackgroundPosition(value as any)}
                        >
                          <SelectTrigger id="bg-position" className="w-full">
                            <SelectValue placeholder="Select position" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="left">Left</SelectItem>
                            <SelectItem value="center">Center</SelectItem>
                            <SelectItem value="right">Right</SelectItem>
                            <SelectItem value="top">Top</SelectItem>
                            <SelectItem value="bottom">Bottom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="bg-repeat" className="text-sm font-medium mb-2">Background Repeat</Label>
                        <Select
                          value={backgroundRepeat}
                          onValueChange={(value) => setBackgroundRepeat(value as any)}
                        >
                          <SelectTrigger id="bg-repeat" className="w-full">
                            <SelectValue placeholder="Select repeat" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="no-repeat">No Repeat</SelectItem>
                            <SelectItem value="repeat">Repeat</SelectItem>
                            <SelectItem value="space">Space</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="bg-size" className="text-sm font-medium mb-2">Background Size</Label>
                        <Select
                          value={backgroundSize}
                          onValueChange={(value) => setBackgroundSize(value as any)}
                        >
                          <SelectTrigger id="bg-size" className="w-full">
                            <SelectValue placeholder="Select size" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="auto">Auto</SelectItem>
                            <SelectItem value="cover">Cover</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="bg-attachment" className="text-sm font-medium mb-2">Background Attachment</Label>
                        <Select
                          value={backgroundAttachment}
                          onValueChange={(value) => setBackgroundAttachment(value as any)}
                        >
                          <SelectTrigger id="bg-attachment" className="w-full">
                            <SelectValue placeholder="Select attachment" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="fixed">Fixed</SelectItem>
                            <SelectItem value="scroll">Scroll</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Font Family</h3>
                    <SearchableGoogleFontSelector
                      value={fontFamily}
                      onChange={setFontFamily}
                      label="Select a font for your profile"
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      This font will be used for all text on your profile page. You can search for any Google Font.
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleProfileUpdate} disabled={isLoading}>
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </Button>
              </CardFooter>
            </Card>
            </div>

            {/* Real-time preview */}
            <div className="md:col-span-2"> {/* Takes 2/5 of the space (reduced preview column) */}
              <h3 className="text-lg font-medium mb-4">Live Preview</h3>
              <div className="sticky top-6">
                <div className="max-w-full mx-auto"> {/* Changed from max-w-lg to max-w-full to reduce margins */}
                  <ProfilePreview
                  username={username}
                  slug={slug}
                  bio={bio}
                  avatarUrl={avatarUrl}
                  avatarPosition={avatarPosition}
                  avatarShape={avatarShape}
                  bannerUrl={bannerUrl}
                  bannerColor={bannerColor}
                  pageBackgroundColor={pageBackgroundColor}
                  cardBackgroundColor={cardBackgroundColor}
                  fontColor={fontColor}
                  fontFamily={fontFamily}
                  backgroundImage={backgroundImage}
                  backgroundPosition={backgroundPosition}
                  backgroundRepeat={backgroundRepeat}
                  backgroundSize={backgroundSize}
                  backgroundAttachment={backgroundAttachment}
                  links={links}
                  showCountrySelector={false}
                  verifiedProfile={user?.verifiedProfile}
                  verifiedProfileGold={user?.verifiedProfileGold}
                  onBackgroundImageChange={handleBackgroundImageUploaded}
                  onBackgroundPropertiesChange={handleBackgroundPropertiesChanged}
                  showBackgroundButton={false}
                />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="url">
          <div className="grid md:grid-cols-5 gap-4"> {/* Changed from gap-6 to gap-4 to reduce spacing */}
            <div className="md:col-span-3"> {/* Takes 3/5 of the space (increased edit column) */}
            <Card>
              <CardHeader>
                <CardTitle>Custom URL</CardTitle>
                <CardDescription>
                  Customize your profile link to make it more memorable and on-brand.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleSlugUpdate}>
                <CardContent>
                  <div>
                    <label htmlFor="slug" className="block text-sm font-medium mb-1">
                      URL Slug
                    </label>
                    <div className="flex items-center">
                      <span className="text-muted-foreground mr-2">linktree-clone.com/#/u/</span>
                      <Input
                        id="slug"
                        value={slug}
                        onChange={(e) => setSlug(e.target.value)}
                        placeholder="your-custom-url"
                        className="flex-1"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? 'Updating...' : 'Update URL'}
                  </Button>
                </CardFooter>
              </form>
            </Card>
            </div>

            {/* Real-time preview */}
            <div className="md:col-span-2"> {/* Takes 2/5 of the space (reduced preview column) */}
              <h3 className="text-lg font-medium mb-4">Live Preview</h3>
              <div className="sticky top-6">
                <div className="w-full overflow-hidden rounded-lg"> {/* Full width container for background color */}
                  <ProfilePreview
                  username={username}
                  slug={slug}
                  bio={bio}
                  avatarUrl={avatarUrl}
                  avatarPosition={avatarPosition}
                  avatarShape={avatarShape}
                  bannerUrl={bannerUrl}
                  bannerColor={bannerColor}
                  pageBackgroundColor={pageBackgroundColor}
                  cardBackgroundColor={cardBackgroundColor}
                  fontColor={fontColor}
                  fontFamily={fontFamily}
                  backgroundImage={backgroundImage}
                  backgroundPosition={backgroundPosition}
                  backgroundRepeat={backgroundRepeat}
                  backgroundSize={backgroundSize}
                  backgroundAttachment={backgroundAttachment}
                  links={links}
                  showCountrySelector={false}
                  verifiedProfile={user?.verifiedProfile}
                  verifiedProfileGold={user?.verifiedProfileGold}
                  onBackgroundImageChange={handleBackgroundImageUploaded}
                  onBackgroundPropertiesChange={handleBackgroundPropertiesChanged}
                  showBackgroundButton={false}
                />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="qrcode">
          <div className="grid md:grid-cols-5 gap-4">
            <div className="md:col-span-3">
              <Card>
                <CardHeader>
                  <CardTitle>Profile QR Code</CardTitle>
                  <CardDescription>
                    Share your profile easily with this unique QR code
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center">
                  {isLoadingQR ? (
                    <div className="flex items-center justify-center h-64 w-64 bg-gray-100 rounded-lg">
                      <p className="text-muted-foreground">Loading QR code...</p>
                    </div>
                  ) : qrCode ? (
                    <div className="flex flex-col items-center">
                      <img
                        src={qrCode}
                        alt="Profile QR Code"
                        className="h-64 w-64 border border-border rounded-lg"
                      />
                      <p className="mt-4 text-sm text-center text-muted-foreground">
                        This QR code links to your profile at:<br />
                        <span className="font-medium">{profileUrl}</span>
                      </p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-64 w-64 bg-gray-100 rounded-lg">
                      <p className="text-muted-foreground">Failed to load QR code</p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-center">
                  <Button
                    onClick={handleDownloadQRCode}
                    disabled={!qrCode || isLoadingQR}
                    className="flex items-center gap-2"
                  >
                    <Download size={16} />
                    Download QR Code
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="md:col-span-2">
              <h3 className="text-lg font-medium mb-4">Live Preview</h3>
              <div className="sticky top-6">
                <div className="w-full overflow-hidden rounded-lg"> {/* Full width container for background color */}
                  <ProfilePreview
                    username={username}
                    slug={slug}
                    bio={bio}
                    avatarUrl={avatarUrl}
                    avatarPosition={avatarPosition}
                    avatarShape={avatarShape}
                    bannerUrl={bannerUrl}
                    bannerColor={bannerColor}
                    pageBackgroundColor={pageBackgroundColor}
                    cardBackgroundColor={cardBackgroundColor}
                    fontColor={fontColor}
                    fontFamily={fontFamily}
                    backgroundImage={backgroundImage}
                    backgroundPosition={backgroundPosition}
                    backgroundRepeat={backgroundRepeat}
                    backgroundSize={backgroundSize}
                    backgroundAttachment={backgroundAttachment}
                    links={links}
                    showCountrySelector={false}
                    verifiedProfile={user?.verifiedProfile}
                    verifiedProfileGold={user?.verifiedProfileGold}
                    onBackgroundImageChange={handleBackgroundImageUploaded}
                    onBackgroundPropertiesChange={handleBackgroundPropertiesChanged}
                    showBackgroundButton={false}
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {message && (
        <div className={`p-4 rounded-md ${message.includes('Error') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
          {message}
        </div>
      )}
    </div>
  );
};

export default ProfileForm;
