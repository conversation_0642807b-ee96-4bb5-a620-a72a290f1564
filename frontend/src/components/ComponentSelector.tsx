import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from './ui/tabs';
import AddLinkForm from './AddLinkForm';
import AddAnnouncementForm from './AddAnnouncementForm';
import AddShuffleCardsForm from './AddShuffleCardsForm';
import AddMiniLeadForm from './AddMiniLeadForm';
import AddVideoForm from './AddVideoForm';
import AddEmbeddedForm from './AddEmbeddedForm';
import AddPaymentForm from './AddPaymentForm';
import { ShuffleCard, MiniLeadField, VisibilityDates } from '../api/links';

interface ComponentSelectorProps {
  onAddLink: (title: string, url: string, icon?: string) => void;
  onAddAnnouncement: (
    title: string,
    text: string,
    backgroundColor: string,
    textColor: string,
    url?: string
  ) => void;
  onAddShuffleCards: (title: string, shuffleCards: ShuffleCard[]) => void;
  onAddMiniLead: (title: string, introText: string, fields: MiniLeadField[]) => void;
  onAddVideo: (
    title: string,
    videoUrl: string,
    thumbnailUrl: string,
    orientation: 'portrait' | 'landscape',
    duration: number
  ) => void;
  onAddEmbedded: (
    title: string,
    embedType: 'youtube' | 'tiktok',
    autoplay: boolean,
    muted: boolean,
    showControls: boolean,
    youtubeCode?: string,
    tikTokUrl?: string,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string,
    visibilityDates?: VisibilityDates
  ) => void;
  onAddPayment?: (title: string, description: string) => void;
}

const ComponentSelector = ({
  onAddLink,
  onAddAnnouncement,
  onAddShuffleCards,
  onAddMiniLead,
  onAddVideo,
  onAddEmbedded,
  onAddPayment
}: ComponentSelectorProps) => {
  const [activeTab, setActiveTab] = useState('link');

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>Add New Component</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="link" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-7 mb-4">
            <TabsTrigger value="link">Link</TabsTrigger>
            <TabsTrigger value="announcement">Post</TabsTrigger>
            <TabsTrigger value="shuffle">Card</TabsTrigger>
            <TabsTrigger value="minilead">Mini Lead</TabsTrigger>
            <TabsTrigger value="video">Video</TabsTrigger>
            <TabsTrigger value="embedded">Embedded</TabsTrigger>
            <TabsTrigger value="payment">Payment</TabsTrigger>
          </TabsList>

          <TabsContent value="link">
            <AddLinkForm onAdd={onAddLink} />
          </TabsContent>

          <TabsContent value="announcement">
            <AddAnnouncementForm onAdd={onAddAnnouncement} />
          </TabsContent>

          <TabsContent value="shuffle">
            <AddShuffleCardsForm onAdd={onAddShuffleCards} />
          </TabsContent>

          <TabsContent value="minilead">
            <AddMiniLeadForm onAdd={onAddMiniLead} />
          </TabsContent>

          <TabsContent value="video">
            <AddVideoForm onAdd={onAddVideo} />
          </TabsContent>

          <TabsContent value="embedded">
            <AddEmbeddedForm onAdd={onAddEmbedded} />
          </TabsContent>

          <TabsContent value="payment">
            {onAddPayment && <AddPaymentForm onAdd={onAddPayment} />}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ComponentSelector;
