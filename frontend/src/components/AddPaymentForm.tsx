import { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';

interface AddPaymentFormProps {
  onAdd: (title: string, description: string) => void;
}

const AddPaymentForm = ({ onAdd }: AddPaymentFormProps) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && description.trim()) {
      onAdd(title, description);
      setTitle('');
      setDescription('');
    }
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Add New Payment</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <Label htmlFor="payment-title">Payment Title</Label>
          <Input
            id="payment-title"
            placeholder="Premium Subscription"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1"
            required
          />
        </div>
        <div className="mb-4">
          <Label htmlFor="payment-description">Payment Description</Label>
          <Input
            id="payment-description"
            placeholder="Get access to exclusive content"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="mt-1"
            required
          />
        </div>
        <Button type="submit" className="w-full">
          Add Payment Link
        </Button>
      </form>
    </div>
  );
};

export default AddPaymentForm;
