import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox';
import { MiniLeadField } from '../api/links';
import { Trash2, Plus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface AddMiniLeadFormProps {
  onAdd: (title: string, introText: string, fields: MiniLeadField[]) => void;
}

const AddMiniLeadForm = ({ onAdd }: AddMiniLeadFormProps) => {
  const [title, setTitle] = useState('');
  const [introText, setIntroText] = useState('');
  const [fields, setFields] = useState<MiniLeadField[]>([
    { type: 'email', required: true, label: 'Email' }
  ]);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (title.trim() && introText.trim() && fields.length > 0) {
      onAdd(title, introText, fields);
      setTitle('');
      setIntroText('');
      setFields([{ type: 'email', required: true, label: 'Email' }]);
      setIsExpanded(false);
    }
  };

  const handleFieldChange = (index: number, field: keyof MiniLeadField, value: any) => {
    const updatedFields = [...fields];
    updatedFields[index] = { ...updatedFields[index], [field]: value };
    setFields(updatedFields);
  };

  const addField = () => {
    setFields([...fields, { type: 'email', required: false, label: '' }]);
  };

  const removeField = (index: number) => {
    if (fields.length > 1) {
      const updatedFields = [...fields];
      updatedFields.splice(index, 1);
      setFields(updatedFields);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Add Mini Lead Form</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          {isExpanded ? (
            <div className="space-y-6">
              <div>
                <Label htmlFor="title" className="block text-sm font-medium mb-1">
                  Form Title
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Lead Form Title"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="introText" className="block text-sm font-medium mb-1">
                  Introduction Text
                </Label>
                <Textarea
                  id="introText"
                  value={introText}
                  onChange={(e) => setIntroText(e.target.value)}
                  placeholder="Brief description of what this form is for"
                  required
                  rows={3}
                />
              </div>
              
              <div className="space-y-4">
                <Label className="block text-sm font-medium">Form Fields</Label>
                
                {fields.map((field, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">Field {index + 1}</h3>
                      {fields.length > 1 && (
                        <Button 
                          type="button" 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => removeField(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-type-${index}`} className="block text-sm font-medium mb-1">
                        Field Type
                      </Label>
                      <Select
                        value={field.type}
                        onValueChange={(value) => handleFieldChange(index, 'type', value)}
                      >
                        <SelectTrigger id={`field-type-${index}`}>
                          <SelectValue placeholder="Select field type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="email">Email</SelectItem>
                          <SelectItem value="phone">Phone Number</SelectItem>
                          <SelectItem value="activity">Activity Registration</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-label-${index}`} className="block text-sm font-medium mb-1">
                        Field Label
                      </Label>
                      <Input
                        id={`field-label-${index}`}
                        value={field.label || ''}
                        onChange={(e) => handleFieldChange(index, 'label', e.target.value)}
                        placeholder={`${field.type.charAt(0).toUpperCase() + field.type.slice(1)} Label`}
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-required-${index}`}
                        checked={field.required}
                        onCheckedChange={(checked) => 
                          handleFieldChange(index, 'required', checked === true)
                        }
                      />
                      <Label
                        htmlFor={`field-required-${index}`}
                        className="text-sm font-medium"
                      >
                        Required Field
                      </Label>
                    </div>
                  </div>
                ))}
                
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={addField}
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Another Field
                </Button>
              </div>
            </div>
          ) : (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => setIsExpanded(true)}
            >
              + Add Mini Lead Form
            </Button>
          )}
        </CardContent>
        {isExpanded && (
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
            <Button type="submit">Add Mini Lead Form</Button>
          </CardFooter>
        )}
      </form>
    </Card>
  );
};

export default AddMiniLeadForm;
