import { useState } from 'react';
import { Link, Badge, VisibilityDates } from '../api/links';
import { Card, CardContent, CardFooter } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import SocialIconSelector from './SocialIconSelector';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog';
import { Pencil, Archive, ArchiveRestore, Trash2, GripVertical, Lock, Calendar } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from './ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { format } from 'date-fns';
import { DateTimePicker } from './DateTimePicker';
import { isAmazonProductLink } from '../utils/amazonAffiliateUtils';
import { formatUrl } from '../utils/urlFormatter';
import {
  FaTelegram, FaTiktok, FaFacebook, FaInstagram, FaTwitter,
  FaLinkedin, FaYoutube, FaGithub, FaDiscord, FaTwitch,
  FaReddit, FaPinterest, FaSnapchat, FaWhatsapp, FaSpotify,
  FaAmazon, FaApple, FaEtsy, FaGoogle, FaMedium, FaPatreon,
  FaPaypal, FaShopify, FaSoundcloud, FaStackOverflow, FaSteam,
  FaVimeo, FaWordpress, FaYelp, FaLink
} from 'react-icons/fa';

// Import badge animations
import '../styles/badge-animations.css';

interface LinkCardProps {
  link: Link;
  onEdit: (
    id: string,
    title: string,
    url: string,
    icon?: string,
    badge?: Badge | null,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string | null,
    visibilityDates?: VisibilityDates | null
  ) => void;
  onDelete: (id: string) => void;
  onArchive?: (id: string) => void;
  onUnarchive?: (id: string) => void;
  onToggleEnabled?: (id: string, enabled: boolean) => void;
  isDraggable?: boolean;
  dragHandleProps?: any;
}

// Helper function to render the appropriate icon
const renderIcon = (iconName: string) => {
  const iconProps = { size: 20 };
  const getIcon = () => {
    switch (iconName) {
      case 'telegram': return <FaTelegram {...iconProps} />;
      case 'tiktok': return <FaTiktok {...iconProps} />;
      case 'facebook': return <FaFacebook {...iconProps} />;
      case 'instagram': return <FaInstagram {...iconProps} />;
      case 'twitter': return <FaTwitter {...iconProps} />;
      case 'linkedin': return <FaLinkedin {...iconProps} />;
      case 'youtube': return <FaYoutube {...iconProps} />;
      case 'github': return <FaGithub {...iconProps} />;
      case 'discord': return <FaDiscord {...iconProps} />;
      case 'twitch': return <FaTwitch {...iconProps} />;
      case 'reddit': return <FaReddit {...iconProps} />;
      case 'pinterest': return <FaPinterest {...iconProps} />;
      case 'snapchat': return <FaSnapchat {...iconProps} />;
      case 'whatsapp': return <FaWhatsapp {...iconProps} />;
      case 'spotify': return <FaSpotify {...iconProps} />;
      case 'amazon': return <FaAmazon {...iconProps} />;
      case 'apple': return <FaApple {...iconProps} />;
      case 'etsy': return <FaEtsy {...iconProps} />;
      case 'google': return <FaGoogle {...iconProps} />;
      case 'medium': return <FaMedium {...iconProps} />;
      case 'patreon': return <FaPatreon {...iconProps} />;
      case 'paypal': return <FaPaypal {...iconProps} />;
      case 'shopify': return <FaShopify {...iconProps} />;
      case 'soundcloud': return <FaSoundcloud {...iconProps} />;
      case 'stackoverflow': return <FaStackOverflow {...iconProps} />;
      case 'steam': return <FaSteam {...iconProps} />;
      case 'vimeo': return <FaVimeo {...iconProps} />;
      case 'wordpress': return <FaWordpress {...iconProps} />;
      case 'yelp': return <FaYelp {...iconProps} />;
      default: return <FaLink {...iconProps} />;
    }
  };

  // Return just the icon - the container is handled in the parent component
  return getIcon();
};

const LinkCard = ({
  link,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  onToggleEnabled,
  isDraggable = false,
  dragHandleProps
}: LinkCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("general");
  const [title, setTitle] = useState(link.title);
  const [url, setUrl] = useState(link.url);
  const [icon, setIcon] = useState(link.icon || 'link');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);

  // Badge state
  const [hasBadge, setHasBadge] = useState(!!link.badge);
  const [badgeText, setBadgeText] = useState(link.badge?.text || 'New');
  const [badgeBackgroundColor, setBadgeBackgroundColor] = useState(
    link.badge?.backgroundColor || 'rgba(59, 130, 246, 0.2)'
  );
  const [badgeTextColor, setBadgeTextColor] = useState(
    link.badge?.textColor || '#3b82f6'
  );
  const [badgeAnimation, setBadgeAnimation] = useState<'popup' | 'shake' | 'vibrate'>(
    (link.badge?.animation as any) || 'popup'
  );
  const [badgeAnimationDelay, setBadgeAnimationDelay] = useState(
    link.badge?.animationDelay || 3000
  );

  // Advanced options state
  const [password, setPassword] = useState(link.password || '');
  const [passwordEnabled, setPasswordEnabled] = useState(link.passwordEnabled || false);
  const [passwordExpiryDate, setPasswordExpiryDate] = useState<Date | undefined>(
    link.passwordExpiryDate ? new Date(link.passwordExpiryDate) : undefined
  );
  // State for password expiry date

  // Visibility dates state
  const [visibilityDatesEnabled, setVisibilityDatesEnabled] = useState(
    link.visibilityDates?.enabled || false
  );
  const [visibilityStartDate, setVisibilityStartDate] = useState<Date | undefined>(
    link.visibilityDates?.startDate ? new Date(link.visibilityDates.startDate) : undefined
  );
  const [visibilityEndDate, setVisibilityEndDate] = useState<Date | undefined>(
    link.visibilityDates?.endDate ? new Date(link.visibilityDates.endDate) : undefined
  );
  // State for visibility dates

  const handleSave = () => {
    const badge = hasBadge ? {
      text: badgeText,
      backgroundColor: badgeBackgroundColor,
      textColor: badgeTextColor,
      animation: badgeAnimation,
      animationDelay: badgeAnimationDelay
    } : null;

    // Prepare visibility dates if enabled
    const visibilityDates = visibilityDatesEnabled ? {
      startDate: visibilityStartDate ? format(visibilityStartDate, 'yyyy-MM-dd\'T\'HH:mm:ss') : undefined,
      endDate: visibilityEndDate ? format(visibilityEndDate, 'yyyy-MM-dd\'T\'HH:mm:ss') : undefined,
      enabled: true
    } : null;

    // Make sure url is a string (not undefined) and properly formatted
    const rawUrl = url || '';
    // Format the URL to ensure it has https:// if it starts with www.
    const safeUrl = formatUrl(rawUrl);

    onEdit(
      link._id,
      title,
      safeUrl,
      icon,
      badge,
      password,
      passwordEnabled,
      passwordExpiryDate ? format(passwordExpiryDate, 'yyyy-MM-dd\'T\'HH:mm:ss') : null,
      visibilityDates
    );
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTitle(link.title);
    setUrl(link.url);
    setIcon(link.icon || 'link');
    setHasBadge(!!link.badge);
    setBadgeText(link.badge?.text || 'New');
    setBadgeBackgroundColor(link.badge?.backgroundColor || 'rgba(59, 130, 246, 0.2)');
    setBadgeTextColor(link.badge?.textColor || '#3b82f6');
    setBadgeAnimation((link.badge?.animation as any) || 'popup');
    setBadgeAnimationDelay(link.badge?.animationDelay || 3000);

    // Reset advanced options
    setPassword(link.password || '');
    setPasswordEnabled(link.passwordEnabled || false);
    setPasswordExpiryDate(link.passwordExpiryDate ? new Date(link.passwordExpiryDate) : undefined);

    // Reset visibility dates
    setVisibilityDatesEnabled(link.visibilityDates?.enabled || false);
    setVisibilityStartDate(link.visibilityDates?.startDate ? new Date(link.visibilityDates.startDate) : undefined);
    setVisibilityEndDate(link.visibilityDates?.endDate ? new Date(link.visibilityDates.endDate) : undefined);

    setIsEditing(false);
    setActiveTab("general");
  };

  const handleDelete = () => {
    setShowDeleteDialog(false);
    onDelete(link._id);
  };

  const handleArchive = () => {
    setShowArchiveDialog(false);
    if (onArchive) {
      onArchive(link._id);
    }
  };

  const handleUnarchive = () => {
    if (onUnarchive) {
      onUnarchive(link._id);
    }
  };

  const handleToggleEnabled = (enabled: boolean) => {
    if (onToggleEnabled) {
      onToggleEnabled(link._id, enabled);
    }
  };

  // Define badge animation styles
  const getBadgeAnimationStyle = () => {
    if (!link.badge) return {};

    return {
      backgroundColor: link.badge.backgroundColor,
      color: link.badge.textColor,
    };
  };

  // Get badge animation class
  const getBadgeAnimationClass = () => {
    if (!link.badge || !link.badge.animation) return '';
    return `badge-${link.badge.animation}`;
  };

  return (
    <Card className="mb-3 relative">
      {/* Enable/Disable Switch (when not editing) */}
      {!isEditing && onToggleEnabled && (
        <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
          <span className="text-xs text-muted-foreground">
            {link.enabled ? 'Visible' : 'Hidden'}
          </span>
          <Switch
            checked={link.enabled}
            onCheckedChange={handleToggleEnabled}
            aria-label={`${link.enabled ? 'Hide' : 'Show'} ${link.title} on public profile`}
          />
        </div>
      )}

      {/* Disabled overlay */}
      {!isEditing && !link.enabled && (
        <div className="absolute inset-0 bg-gray-200/50 z-5 flex items-center justify-center">
          <div className="bg-white/80 px-3 py-1 rounded-md text-sm text-gray-500 font-medium">
            Hidden from public profile
          </div>
        </div>
      )}

      {/* Badge display (when not editing) */}
      {!isEditing && link.badge && (
        <div
          className={`absolute -top-2 left-2 px-2 py-0.5 rounded-full text-xs font-medium z-10 ${getBadgeAnimationClass()}`}
          style={getBadgeAnimationStyle()}
        >
          {link.badge.text}
        </div>
      )}

      {/* Password protection indicator */}
      {!isEditing && link.passwordEnabled && (
        <div className="absolute -top-2 right-16 px-2 py-0.5 rounded-full text-xs font-medium z-10 bg-amber-100 text-amber-800 flex items-center gap-1">
          <Lock size={10} />
          <span>Protected</span>
        </div>
      )}

      {/* Amazon affiliate indicator - shown in dashboard */}
      {!isEditing && link.url && isAmazonProductLink(link.url) && (
        <div className="absolute -top-2 right-36 px-2 py-0.5 rounded-full text-xs font-medium z-10 bg-yellow-100 text-yellow-800 flex items-center gap-1">
          <FaAmazon size={10} />
          <span>Amazon</span>
        </div>
      )}

      <CardContent className={`pt-4 pb-2 ${isEditing ? '' : 'flex items-center'}`}>
        {/* Drag handle */}
        {isDraggable && !isEditing && (
          <div
            className="mr-2 cursor-grab text-muted-foreground hover:text-foreground"
            {...dragHandleProps}
          >
            <GripVertical size={16} />
          </div>
        )}

        {isEditing ? (
          <div className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-2">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="badge">Badge</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-3">
                <div>
                  <Label htmlFor="icon" className="text-sm font-medium">Icon</Label>
                  <SocialIconSelector
                    selectedIcon={icon}
                    onSelect={setIcon}
                  />
                </div>
                <div>
                  <Label htmlFor="title" className="text-sm font-medium">Title</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Link Title"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="url" className="text-sm font-medium">URL</Label>
                  <Input
                    id="url"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder="https://example.com"
                    className="mt-1"
                  />
                </div>
              </TabsContent>

              <TabsContent value="badge" className="space-y-3">
                <div className="flex items-center mb-3">
                  <Label htmlFor="hasBadge" className="text-sm font-medium mr-2">Enable Badge</Label>
                  <input
                    type="checkbox"
                    id="hasBadge"
                    checked={hasBadge}
                    onChange={(e) => setHasBadge(e.target.checked)}
                    className="h-4 w-4"
                  />
                </div>

                {hasBadge && (
                  <>
                    <div>
                      <Label htmlFor="badgeText" className="text-sm font-medium">Badge Text</Label>
                      <Input
                        id="badgeText"
                        value={badgeText}
                        onChange={(e) => setBadgeText(e.target.value)}
                        placeholder="New"
                        className="mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-sm font-medium">Background Color</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full mt-1 h-8 flex justify-between items-center"
                            >
                              <div
                                className="w-5 h-5 rounded-full mr-2"
                                style={{ backgroundColor: badgeBackgroundColor }}
                              />
                              <span className="text-xs truncate">{badgeBackgroundColor}</span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-3">
                            <HexColorPicker
                              color={badgeBackgroundColor}
                              onChange={setBadgeBackgroundColor}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Text Color</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full mt-1 h-8 flex justify-between items-center"
                            >
                              <div
                                className="w-5 h-5 rounded-full mr-2"
                                style={{ backgroundColor: badgeTextColor }}
                              />
                              <span className="text-xs truncate">{badgeTextColor}</span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-3">
                            <HexColorPicker
                              color={badgeTextColor}
                              onChange={setBadgeTextColor}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="badgeAnimation" className="text-sm font-medium">Animation</Label>
                      <Select
                        value={badgeAnimation}
                        onValueChange={(value) => setBadgeAnimation(value as any)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select animation" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="popup">Popup</SelectItem>
                          <SelectItem value="shake">Shake</SelectItem>
                          <SelectItem value="vibrate">Vibrate</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="badgeAnimationDelay" className="text-sm font-medium">
                        Animation Delay (ms)
                      </Label>
                      <Input
                        id="badgeAnimationDelay"
                        type="number"
                        value={badgeAnimationDelay}
                        onChange={(e) => setBadgeAnimationDelay(Number(e.target.value))}
                        min={0}
                        step={500}
                        className="mt-1"
                      />
                    </div>

                    <div className="mt-3 p-2 border rounded-md">
                      <Label className="text-xs text-muted-foreground mb-2 block">Preview:</Label>
                      <div className="relative h-8 flex items-center justify-center">
                        <div
                          className={`px-2 py-0.5 rounded-full text-xs font-medium preview-${badgeAnimation}`}
                          style={{
                            backgroundColor: badgeBackgroundColor,
                            color: badgeTextColor
                          }}
                        >
                          {badgeText || 'Badge'}
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </TabsContent>

              <TabsContent value="advanced" className="space-y-3">
                {/* Password Protection Section */}
                <div className="border rounded-md p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="passwordEnabled" className="text-sm font-medium flex items-center gap-1">
                      <Lock size={14} />
                      Password Protection
                    </Label>
                    <Switch
                      id="passwordEnabled"
                      checked={passwordEnabled}
                      onCheckedChange={setPasswordEnabled}
                    />
                  </div>

                  {passwordEnabled && (
                    <>
                      <div>
                        <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                        <Input
                          id="password"
                          type="text"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="Enter password"
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="passwordExpiryDate" className="text-sm font-medium">Auto-disable Date & Time (Optional)</Label>
                        <div className="mt-1 flex">
                          <div className="flex-1">
                            <DateTimePicker
                              date={passwordExpiryDate}
                              onDateChange={setPasswordExpiryDate}
                              placeholder="Select date and time"
                            />
                          </div>
                          {passwordExpiryDate && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="ml-1"
                              onClick={() => setPasswordExpiryDate(undefined)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Password protection will automatically disable after this date and time.
                        </p>
                      </div>
                    </>
                  )}
                </div>

                {/* Visibility Dates Section */}
                <div className="border rounded-md p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="visibilityDatesEnabled" className="text-sm font-medium flex items-center gap-1">
                      <Calendar size={14} />
                      Schedule Visibility
                    </Label>
                    <Switch
                      id="visibilityDatesEnabled"
                      checked={visibilityDatesEnabled}
                      onCheckedChange={setVisibilityDatesEnabled}
                    />
                  </div>

                  {visibilityDatesEnabled && (
                    <>
                      <div>
                        <Label htmlFor="visibilityStartDate" className="text-sm font-medium">Start Date & Time (Optional)</Label>
                        <div className="mt-1 flex">
                          <div className="flex-1">
                            <DateTimePicker
                              date={visibilityStartDate}
                              onDateChange={setVisibilityStartDate}
                              placeholder="Select start date and time"
                            />
                          </div>
                          {visibilityStartDate && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="ml-1"
                              onClick={() => setVisibilityStartDate(undefined)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Link will be visible from this date and time onwards.
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="visibilityEndDate" className="text-sm font-medium">End Date & Time (Optional)</Label>
                        <div className="mt-1 flex">
                          <div className="flex-1">
                            <DateTimePicker
                              date={visibilityEndDate}
                              onDateChange={setVisibilityEndDate}
                              placeholder="Select end date and time"
                            />
                          </div>
                          {visibilityEndDate && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="ml-1"
                              onClick={() => setVisibilityEndDate(undefined)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Link will be hidden after this date and time.
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <>
            <div className="text-primary flex items-center justify-center w-6 h-6 flex-shrink-0">
              {renderIcon(link.icon || 'link')}
            </div>
            <div className="flex-1 ml-3 min-w-0">
              <div className="flex items-center">
                <h3 className="font-medium truncate">{link.title}</h3>
                {link.passwordEnabled && (
                  <Lock size={14} className="ml-1 text-amber-600" />
                )}
                {link.visibilityDates?.enabled && (
                  <Calendar size={14} className="ml-1 text-blue-600" />
                )}
              </div>
              <a
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-blue-500 hover:underline truncate block"
              >
                {link.url}
              </a>
              <div className="text-xs text-muted-foreground mt-1 flex items-center gap-2">
                <span>Clicks: {link.clickCount}</span>
                {link.visibilityDates?.enabled && (
                  <span className="flex items-center gap-1 text-blue-600">
                    <Calendar size={12} />
                    <span>
                      {link.visibilityDates.startDate && !link.visibilityDates.endDate &&
                        `From ${new Date(link.visibilityDates.startDate).toLocaleString()}`}
                      {!link.visibilityDates.startDate && link.visibilityDates.endDate &&
                        `Until ${new Date(link.visibilityDates.endDate).toLocaleString()}`}
                      {link.visibilityDates.startDate && link.visibilityDates.endDate &&
                        `${new Date(link.visibilityDates.startDate).toLocaleString()} - ${new Date(link.visibilityDates.endDate).toLocaleString()}`}
                    </span>
                  </span>
                )}
              </div>
            </div>
            <div className="flex gap-1 ml-2 flex-shrink-0">
              <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setIsEditing(true)}>
                <Pencil className="h-4 w-4" />
              </Button>
              {link.archived ? (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleUnarchive}
                >
                  <ArchiveRestore className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowArchiveDialog(true)}
                >
                  <Archive className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}
      </CardContent>

      {isEditing && (
        <CardFooter className="flex justify-between pt-0 pb-3">
          <Button variant="outline" size="sm" onClick={handleCancel}>
            Cancel
          </Button>
          <Button size="sm" onClick={handleSave}>
            Save
          </Button>
        </CardFooter>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete the link "{link.title}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Archive Confirmation Dialog */}
      <AlertDialog open={showArchiveDialog} onOpenChange={setShowArchiveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive this link?</AlertDialogTitle>
            <AlertDialogDescription>
              This will archive the link "{link.title}". Archived links won't appear on your public profile, but you can unarchive them later.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleArchive}>Archive</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

export default LinkCard;
