import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../lib/utils';

// Icons
import {
  LayoutDashboard,
  BarChart2,
  User,
  Eye,
  Settings,
  Users
} from 'lucide-react';

interface RightSidebarItemProps {
  icon: React.ReactNode;
  label: string;
  href: string;
  active: boolean;
}

const RightSidebarItem: React.FC<RightSidebarItemProps> = ({
  icon,
  label,
  href,
  active
}) => {
  return (
    <Link
      to={href}
      className={cn(
        "flex items-center gap-3 px-4 py-3 rounded-lg transition-colors",
        active
          ? "bg-primary/10 text-primary font-medium"
          : "text-muted-foreground hover:bg-secondary hover:text-foreground"
      )}
    >
      <div className="w-5 h-5">{icon}</div>
      <span>{label}</span>
    </Link>
  );
};

export const RightSidebar: React.FC = () => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="w-64 h-screen border-l bg-card flex flex-col">
      <div className="p-6">
        {/* Header removed as requested */}
      </div>

      <div className="flex-1 px-3 py-4 space-y-1">
        <RightSidebarItem
          icon={<LayoutDashboard size={18} />}
          label="Dashboard"
          href="/dashboard"
          active={isActive('/dashboard')}
        />
        <RightSidebarItem
          icon={<User size={18} />}
          label="Profile"
          href="/profile"
          active={isActive('/profile')}
        />
        <RightSidebarItem
          icon={<Eye size={18} />}
          label="Preview"
          href="/preview"
          active={isActive('/preview')}
        />
        <RightSidebarItem
          icon={<BarChart2 size={18} />}
          label="Reports"
          href="/stats"
          active={isActive('/stats')}
        />
        <RightSidebarItem
          icon={<Users size={18} />}
          label="Users"
          href="/users"
          active={isActive('/users')}
        />
        <RightSidebarItem
          icon={<Settings size={18} />}
          label="Settings"
          href="/settings"
          active={isActive('/settings')}
        />
      </div>
    </div>
  );
};

export default RightSidebar;
