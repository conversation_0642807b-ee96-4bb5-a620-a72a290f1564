import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import { HexColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import RichTextEditor from './RichTextEditor';
import GradientPicker from './GradientPicker';
import { AlignLeft, AlignCenter, AlignRight } from 'lucide-react';

interface AddAnnouncementFormProps {
  onAdd: (
    title: string,
    text: string,
    backgroundColor: string,
    textColor: string,
    url?: string,
    backgroundGradient?: {
      color1: string;
      color2: string;
      direction: string;
    },
    useGradient?: boolean,
    buttonText?: string,
    buttonBackgroundColor?: string,
    buttonTextColor?: string,
    titleAlignment?: 'left' | 'center' | 'right',
    textAlignment?: 'left' | 'center' | 'right'
  ) => void;
}

const AddAnnouncementForm = ({ onAdd }: AddAnnouncementFormProps) => {
  const [activeTab, setActiveTab] = useState('content');
  const [title, setTitle] = useState('');
  const [text, setText] = useState('');
  const [url, setUrl] = useState('');
  const [backgroundColor, setBackgroundColor] = useState('#f3f4f6');
  const [textColor, setTextColor] = useState('#000000');
  const [useGradient, setUseGradient] = useState(false);
  const [gradientColor1, setGradientColor1] = useState('#8B5CF6');
  const [gradientColor2, setGradientColor2] = useState('#EC4899');
  const [gradientDirection, setGradientDirection] = useState('to right');
  const [buttonText, setButtonText] = useState('Learn More');
  const [buttonBackgroundColor, setButtonBackgroundColor] = useState('#3b82f6');
  const [buttonTextColor, setButtonTextColor] = useState('#ffffff');
  const [titleAlignment, setTitleAlignment] = useState<'left' | 'center' | 'right'>('left');
  const [textAlignment, setTextAlignment] = useState<'left' | 'center' | 'right'>('left');
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && text.trim()) {
      onAdd(
        title,
        text,
        backgroundColor,
        textColor,
        url.trim() || undefined,
        {
          color1: gradientColor1,
          color2: gradientColor2,
          direction: gradientDirection
        },
        useGradient,
        buttonText,
        buttonBackgroundColor,
        buttonTextColor,
        titleAlignment,
        textAlignment
      );
      setTitle('');
      setText('');
      setUrl('');
      setBackgroundColor('#f3f4f6');
      setTextColor('#000000');
      setUseGradient(false);
      setGradientColor1('#8B5CF6');
      setGradientColor2('#EC4899');
      setGradientDirection('to right');
      setButtonText('Learn More');
      setButtonBackgroundColor('#3b82f6');
      setButtonTextColor('#ffffff');
      setTitleAlignment('left');
      setTextAlignment('left');
      setActiveTab('content');
      setIsExpanded(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Add New Post</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          {isExpanded ? (
            <div className="space-y-4">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="style">Style</TabsTrigger>
                  <TabsTrigger value="button">Button</TabsTrigger>
                </TabsList>

                <TabsContent value="content" className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <Label htmlFor="title" className="block text-sm font-medium">
                        Title
                      </Label>
                      <div className="flex space-x-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className={`p-1 h-7 w-7 ${titleAlignment === 'left' ? 'bg-muted' : ''}`}
                          onClick={() => setTitleAlignment('left')}
                        >
                          <AlignLeft className="h-3 w-3" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className={`p-1 h-7 w-7 ${titleAlignment === 'center' ? 'bg-muted' : ''}`}
                          onClick={() => setTitleAlignment('center')}
                        >
                          <AlignCenter className="h-3 w-3" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className={`p-1 h-7 w-7 ${titleAlignment === 'right' ? 'bg-muted' : ''}`}
                          onClick={() => setTitleAlignment('right')}
                        >
                          <AlignRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Post Title"
                      required
                      style={{ textAlign: titleAlignment }}
                    />
                  </div>
                  <div>
                    <div className="mb-1">
                      <Label htmlFor="text" className="block text-sm font-medium">
                        Text
                      </Label>
                    </div>
                    <RichTextEditor
                      value={text}
                      onChange={setText}
                      placeholder="Post text content"
                      className="min-h-[120px]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="url" className="block text-sm font-medium mb-1">
                      URL (Optional)
                    </Label>
                    <Input
                      id="url"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="style" className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <Switch
                      id="useGradient"
                      checked={useGradient}
                      onCheckedChange={setUseGradient}
                    />
                    <Label htmlFor="useGradient" className="cursor-pointer">
                      Use Gradient Background
                    </Label>
                  </div>

                  {useGradient ? (
                    <GradientPicker
                      color1={gradientColor1}
                      color2={gradientColor2}
                      direction={gradientDirection}
                      onChange={(color1, color2, direction) => {
                        setGradientColor1(color1);
                        setGradientColor2(color2);
                        setGradientDirection(direction);
                      }}
                      label="Background Gradient"
                    />
                  ) : (
                    <div>
                      <Label className="block text-sm font-medium mb-1">
                        Background Color
                      </Label>
                      <div className="flex items-center gap-2">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-10 h-10 p-0 border"
                              style={{ backgroundColor }}
                            />
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-3">
                            <HexColorPicker color={backgroundColor} onChange={setBackgroundColor} />
                          </PopoverContent>
                        </Popover>
                        <Input
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="font-mono"
                        />
                      </div>
                    </div>
                  )}

                  <div>
                    <Label className="block text-sm font-medium mb-1">
                      Text Color
                    </Label>
                    <div className="flex items-center gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-10 h-10 p-0 border"
                            style={{ backgroundColor: textColor }}
                          />
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-3">
                          <HexColorPicker color={textColor} onChange={setTextColor} />
                        </PopoverContent>
                      </Popover>
                      <Input
                        value={textColor}
                        onChange={(e) => setTextColor(e.target.value)}
                        className="font-mono"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="button" className="space-y-4">
                  <div>
                    <Label htmlFor="buttonText" className="block text-sm font-medium mb-1">
                      Button Text
                    </Label>
                    <Input
                      id="buttonText"
                      value={buttonText}
                      onChange={(e) => setButtonText(e.target.value)}
                      placeholder="Learn More"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="block text-sm font-medium mb-1">
                        Button Background
                      </Label>
                      <div className="flex items-center gap-2">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-10 h-10 p-0 border"
                              style={{ backgroundColor: buttonBackgroundColor }}
                            />
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-3">
                            <HexColorPicker color={buttonBackgroundColor} onChange={setButtonBackgroundColor} />
                          </PopoverContent>
                        </Popover>
                        <Input
                          value={buttonBackgroundColor}
                          onChange={(e) => setButtonBackgroundColor(e.target.value)}
                          className="font-mono"
                        />
                      </div>
                    </div>

                    <div>
                      <Label className="block text-sm font-medium mb-1">
                        Button Text Color
                      </Label>
                      <div className="flex items-center gap-2">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-10 h-10 p-0 border"
                              style={{ backgroundColor: buttonTextColor }}
                            />
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-3">
                            <HexColorPicker color={buttonTextColor} onChange={setButtonTextColor} />
                          </PopoverContent>
                        </Popover>
                        <Input
                          value={buttonTextColor}
                          onChange={(e) => setButtonTextColor(e.target.value)}
                          className="font-mono"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-4 border rounded-md">
                    <p className="text-sm font-medium mb-2">Button Preview:</p>
                    <Button
                      className="w-full"
                      style={{
                        backgroundColor: buttonBackgroundColor,
                        color: buttonTextColor
                      }}
                    >
                      {buttonText || 'Learn More'}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => setIsExpanded(true)}
            >
              + Add New Post
            </Button>
          )}
        </CardContent>
        {isExpanded && (
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
            <Button type="submit">Add Post</Button>
          </CardFooter>
        )}
      </form>
    </Card>
  );
};

export default AddAnnouncementForm;
