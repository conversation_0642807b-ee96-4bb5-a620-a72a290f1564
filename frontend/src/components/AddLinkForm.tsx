import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import SocialIconSelector from './SocialIconSelector';
import { formatUrl } from '../utils/urlFormatter';

interface AddLinkFormProps {
  onAdd: (title: string, url: string, icon?: string) => void;
}

const AddLinkForm = ({ onAdd }: AddLinkFormProps) => {
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [icon, setIcon] = useState('link');
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && url.trim()) {
      // Format the URL to ensure it has https:// if it starts with www.
      const formattedUrl = formatUrl(url.trim());
      onAdd(title, formattedUrl, icon);
      setTitle('');
      setUrl('');
      setIcon('link');
      setIsExpanded(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Add New Link</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          {isExpanded ? (
            <div className="space-y-4">
              <div>
                <label htmlFor="icon" className="block text-sm font-medium mb-1">
                  Icon
                </label>
                <SocialIconSelector
                  selectedIcon={icon}
                  onSelect={setIcon}
                />
              </div>
              <div>
                <label htmlFor="title" className="block text-sm font-medium mb-1">
                  Title
                </label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Link Title"
                  required
                />
              </div>
              <div>
                <label htmlFor="url" className="block text-sm font-medium mb-1">
                  URL
                </label>
                <Input
                  id="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://example.com"
                  required
                />
              </div>
            </div>
          ) : (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => setIsExpanded(true)}
            >
              + Add New Link
            </Button>
          )}
        </CardContent>
        {isExpanded && (
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
            <Button type="submit">Add Link</Button>
          </CardFooter>
        )}
      </form>
    </Card>
  );
};

export default AddLinkForm;
