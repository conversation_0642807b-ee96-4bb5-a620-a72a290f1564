import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { VideoProcessor } from './VideoProcessor';

interface AddVideoFormProps {
  onAdd: (
    title: string,
    videoUrl: string,
    thumbnailUrl: string,
    orientation: 'landscape', // Only support landscape mode
    duration: number,
    videoId?: string
  ) => void;
}

const AddVideoForm = ({ onAdd }: AddVideoFormProps) => {
  const [title, setTitle] = useState('');
  const [videoUrl, setVideoUrl] = useState('');
  const [thumbnailUrl, setThumbnailUrl] = useState('');
  const [duration, setDuration] = useState(0);
  const [videoId, setVideoId] = useState('');
  const orientation = 'landscape'; // Always use landscape mode
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && thumbnailUrl.trim()) {
      // Pass the videoUrl and videoId that were set by handleVideoUploaded
      onAdd(title, videoUrl, thumbnailUrl, orientation, duration, videoId);
      setTitle('');
      setVideoUrl('');
      setThumbnailUrl('');
      setDuration(0);
      setVideoId('');
      setIsExpanded(false);
    }
  };

  const handleVideoUploaded = async (newVideoUrl: string, newThumbnailUrl: string, newDuration: number, newVideoId?: string) => {
    // Store both video URL and thumbnail URL
    if (newVideoUrl) {
      setVideoUrl(newVideoUrl);
      setDuration(newDuration);
      // Store videoId for Bunny CDN videos
      if (newVideoId) {
        setVideoId(newVideoId);
      }

      // Se è un video di Bunny CDN (URL contiene bunnycdn o .m3u8), salva automaticamente
      if (newVideoUrl.includes('bunnycdn') || newVideoUrl.includes('.m3u8') || newVideoUrl.includes('vz-bde34908-dcf.b-cdn.net')) {
        console.log('Auto-saving new video component after Bunny CDN upload');

        // Genera un titolo temporaneo se l'utente non l'ha ancora inserito
        const videoTitle = title.trim() || 'Nuovo Video';

        // Salva direttamente nel database chiamando onAdd
        onAdd(videoTitle, newVideoUrl, newThumbnailUrl || '', orientation, newDuration, newVideoId);

        // Reset dei campi del form
        setTitle('');
        setVideoUrl('');
        setThumbnailUrl('');
        setDuration(0);
        setVideoId('');
        setIsExpanded(false);
      }
    }

    if (newThumbnailUrl) {
      setThumbnailUrl(newThumbnailUrl);
    }
  };

  // Orientation is always landscape

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Add New Video</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          {isExpanded ? (
            <div className="space-y-4">
              <div>
                <Label htmlFor="title" className="block text-sm font-medium mb-1">
                  Title
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Video Title"
                  required
                />
              </div>

              <VideoProcessor
                onVideoUploaded={handleVideoUploaded}
              />
            </div>
          ) : (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => setIsExpanded(true)}
            >
              + Add New Video
            </Button>
          )}
        </CardContent>
        {isExpanded && (
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!thumbnailUrl}
            >
              Add Video
            </Button>
          </CardFooter>
        )}
      </form>
    </Card>
  );
};

export default AddVideoForm;
