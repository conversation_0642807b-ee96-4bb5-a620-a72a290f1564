import { useEffect, useRef, useState } from 'react';
import { Link } from '../api/links';
import { X } from 'lucide-react';
import { Button } from './ui/button';

interface LottiePopupProps {
  link: Link;
  onClose: () => void;
  preloadedAnimation?: any; // Optional preloaded animation instance
}

// Global cache for preloaded animations
const preloadedAnimations: Record<string, any> = {};

// Function to preload Lottie library (can be called from anywhere)
export const preloadLottieLibrary = async (): Promise<void> => {
  if ((window as any).lottie) return Promise.resolve();

  return new Promise<void>((resolve, reject) => {
    const lottieScript = document.createElement('script');
    lottieScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.10.2/lottie.min.js';
    lottieScript.async = true;
    lottieScript.onload = () => resolve();
    lottieScript.onerror = () => reject(new Error('Failed to load Lottie library'));
    document.body.appendChild(lottieScript);
  });
};

// Function to preload a specific animation (can be called from anywhere)
export const preloadLottieAnimation = async (lottieUrl: string): Promise<any> => {
  // If already preloaded, return the cached instance
  if (preloadedAnimations[lottieUrl]) {
    return preloadedAnimations[lottieUrl];
  }

  // Make sure the library is loaded first
  await preloadLottieLibrary();

  // Create a temporary container for preloading
  const tempContainer = document.createElement('div');
  tempContainer.style.position = 'absolute';
  tempContainer.style.opacity = '0';
  tempContainer.style.pointerEvents = 'none';
  tempContainer.style.width = '1px';
  tempContainer.style.height = '1px';
  tempContainer.style.overflow = 'hidden';
  document.body.appendChild(tempContainer);

  try {
    const lottie = (window as any).lottie;

    // Create the animation but don't autoplay it
    const anim = lottie.loadAnimation({
      container: tempContainer,
      renderer: 'svg',
      loop: true,
      autoplay: false, // Don't autoplay during preload
      path: lottieUrl
    });

    // Wait for the animation to load
    await new Promise<void>((resolve, reject) => {
      anim.addEventListener('DOMLoaded', () => resolve());
      anim.addEventListener('error', () => reject(new Error('Failed to load animation')));
    });

    // Store in cache
    preloadedAnimations[lottieUrl] = {
      animation: anim,
      container: tempContainer
    };

    return preloadedAnimations[lottieUrl];
  } catch (error) {
    // Clean up on error
    document.body.removeChild(tempContainer);
    console.error('Error preloading Lottie animation:', error);
    throw error;
  }
};

const LottiePopup = ({ link, onClose, preloadedAnimation }: LottiePopupProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const animationRef = useRef<any>(null);

  // Load and display the animation as soon as the component mounts
  useEffect(() => {
    const displayAnimation = async () => {
      try {
        // If we have a preloaded animation, use it
        if (preloadedAnimation) {
          // Move the animation to our container
          if (containerRef.current) {
            const lottie = (window as any).lottie;

            // Create a new animation in our container
            animationRef.current = lottie.loadAnimation({
              container: containerRef.current,
              renderer: 'svg',
              loop: true,
              autoplay: true,
              animationData: preloadedAnimation.animation.animationData
            });

            animationRef.current.addEventListener('DOMLoaded', () => {
              setLoaded(true);
            });

            return;
          }
        }

        // Otherwise load it now
        await preloadLottieLibrary();

        if (containerRef.current && (link.lottieUrl || link.lottieJsonUrl)) {
          const lottie = (window as any).lottie;
          const animationSource = link.lottieJsonUrl || link.lottieUrl;

          // Check if we have this animation in cache
          if (animationSource && preloadedAnimations[animationSource]) {
            const cachedAnim = preloadedAnimations[animationSource].animation;

            // Create a new animation with the same data
            animationRef.current = lottie.loadAnimation({
              container: containerRef.current,
              renderer: 'svg',
              loop: true,
              autoplay: true,
              animationData: cachedAnim.animationData
            });
          } else {
            // Determine the full URL for the animation
            let fullAnimationUrl = animationSource;
            if (link.lottieJsonUrl && !link.lottieJsonUrl.startsWith('http')) {
              // For uploaded JSON files, construct the full URL
              fullAnimationUrl = `${import.meta.env.VITE_API_URL.replace('/api', '')}${link.lottieJsonUrl}`;
            }

            // Load the animation directly
            animationRef.current = lottie.loadAnimation({
              container: containerRef.current,
              renderer: 'svg',
              loop: true,
              autoplay: true,
              path: fullAnimationUrl
            });
          }

          // Handle animation load success
          animationRef.current.addEventListener('DOMLoaded', () => {
            setLoaded(true);
          });

          // Handle animation load error
          animationRef.current.addEventListener('error', () => {
            setError(true);
          });
        }
      } catch (err) {
        console.error('Error loading Lottie animation:', err);
        setError(true);
      }
    };

    displayAnimation();

    // Cleanup function
    return () => {
      if (animationRef.current) {
        animationRef.current.destroy();
      }
    };
  }, [link.lottieUrl, link.lottieJsonUrl, preloadedAnimation]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 animate-in fade-in">
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-hidden">
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-2 right-2 z-10"
          onClick={onClose}
        >
          <X size={18} />
        </Button>

        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">{link.title}</h3>

          <div
            ref={containerRef}
            className="w-full h-64 flex items-center justify-center"
          >
            {!loaded && !error && (
              <div className="text-sm text-muted-foreground">Loading animation...</div>
            )}

            {error && (
              <div className="text-sm text-destructive">
                Failed to load animation. Please check the URL and try again.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LottiePopup;
