import { useState, useEffect } from 'react';
import { Link } from '../api/links';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Slider } from './ui/slider';
import { Switch } from './ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Edit, Trash, Archive, RotateCcw, Zap } from 'lucide-react';
import { preloadLottieAnimation } from './LottiePopup';
import { LottieUploader } from './LottieUploader';

interface LottieAnimationCardProps {
  link: Link;
  onEdit: (
    id: string,
    title: string,
    lottieUrl: string,
    triggerEvent: 'pageLoad' | 'inactivity' | 'linkClick',
    triggerDelay: number,
    linkId?: string,
    lottieJsonUrl?: string
  ) => void;
  onDelete: (id: string) => void;
  onArchive?: (id: string) => void;
  onUnarchive?: (id: string) => void;
  onToggleEnabled?: (id: string, enabled: boolean) => void;
  activeLinks: Link[];
}

const LottieAnimationCard = ({
  link,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  onToggleEnabled,
  activeLinks
}: LottieAnimationCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(link.title);
  const [lottieUrl, setLottieUrl] = useState(link.lottieUrl || '');
  const [lottieJsonUrl, setLottieJsonUrl] = useState(link.lottieJsonUrl || '');
  const [triggerEvent, setTriggerEvent] = useState<'pageLoad' | 'inactivity' | 'linkClick'>(
    (link.triggerEvent as 'pageLoad' | 'inactivity' | 'linkClick') || 'pageLoad'
  );
  const [triggerDelay, setTriggerDelay] = useState(link.triggerDelay || 0);
  const [linkId, setLinkId] = useState<string>(link.linkId || '');
  const [showLinkSelector, setShowLinkSelector] = useState(triggerEvent === 'linkClick');
  const [preloadStatus, setPreloadStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [sourceType, setSourceType] = useState<'url' | 'upload'>(
    link.lottieJsonUrl ? 'upload' : 'url'
  );

  // Update state when link prop changes
  useEffect(() => {
    setTitle(link.title);
    setLottieUrl(link.lottieUrl || '');
    setTriggerEvent((link.triggerEvent as 'pageLoad' | 'inactivity' | 'linkClick') || 'pageLoad');
    setTriggerDelay(link.triggerDelay || 0);
    setLinkId(link.linkId || '');
    setShowLinkSelector((link.triggerEvent as 'pageLoad' | 'inactivity' | 'linkClick') === 'linkClick');

    // Log available links for debugging
    console.log('LottieAnimationCard - Available links for selection:', activeLinks);

    // Preload the animation when the component mounts
    if (link.lottieUrl) {
      // Preload in background without showing loading state
      preloadLottieAnimation(link.lottieUrl)
        .then(() => {
          console.log('Animation preloaded successfully in card:', link.title);
          setPreloadStatus('success');
        })
        .catch(error => {
          console.error('Failed to preload animation in card:', error);
          // Don't set error state to avoid confusing the user for existing animations
        });
    }
  }, [link, activeLinks]);

  // Preload animation when URL changes during editing
  useEffect(() => {
    // Only run this effect when editing and URL has changed from original
    if (!isEditing || lottieUrl === link.lottieUrl || !lottieUrl || lottieUrl.length < 10) {
      return;
    }

    // Debounce the preloading to avoid unnecessary requests while typing
    const debounceTimer = setTimeout(async () => {
      try {
        setPreloadStatus('loading');

        // Attempt to preload the animation
        await preloadLottieAnimation(lottieUrl);

        setPreloadStatus('success');
        console.log('Animation preloaded successfully during edit:', lottieUrl);
      } catch (error) {
        console.error('Failed to preload animation during edit:', error);
        setPreloadStatus('error');
      }
    }, 800); // Wait 800ms after the user stops typing

    return () => clearTimeout(debounceTimer);
  }, [lottieUrl, isEditing, link.lottieUrl]);

  const handleJsonUploaded = (jsonUrl: string) => {
    setLottieJsonUrl(jsonUrl);
  };

  const handleSave = () => {
    if (!title.trim()) {
      alert('Please enter a title');
      return;
    }

    if (sourceType === 'url' && !lottieUrl.trim()) {
      alert('Please enter a Lottie animation URL');
      return;
    }

    if (sourceType === 'upload' && !lottieJsonUrl.trim()) {
      alert('Please upload a Lottie JSON file');
      return;
    }

    if (triggerEvent === 'linkClick' && !linkId) {
      alert('Please select a link to trigger the animation');
      return;
    }

    onEdit(
      link._id,
      title,
      sourceType === 'url' ? lottieUrl : '',
      triggerEvent,
      triggerDelay,
      triggerEvent === 'linkClick' ? linkId : undefined,
      sourceType === 'upload' ? lottieJsonUrl : undefined
    );

    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset to original values
    setTitle(link.title);
    setLottieUrl(link.lottieUrl || '');
    setLottieJsonUrl(link.lottieJsonUrl || '');
    setTriggerEvent((link.triggerEvent as 'pageLoad' | 'inactivity' | 'linkClick') || 'pageLoad');
    setTriggerDelay(link.triggerDelay || 0);
    setLinkId(link.linkId || '');
    setSourceType(link.lottieJsonUrl ? 'upload' : 'url');
    setShowLinkSelector(triggerEvent === 'linkClick');
    setIsEditing(false);
  };

  const handleTriggerEventChange = (value: string) => {
    const event = value as 'pageLoad' | 'inactivity' | 'linkClick';
    setTriggerEvent(event);
    setShowLinkSelector(event === 'linkClick');
    if (event !== 'linkClick') {
      setLinkId('');
    }
  };

  const handleToggleEnabled = () => {
    if (onToggleEnabled) {
      onToggleEnabled(link._id, !link.enabled);
    }
  };

  const getTriggerDescription = () => {
    switch (triggerEvent) {
      case 'pageLoad':
        return `Triggers ${triggerDelay} seconds after page load`;
      case 'inactivity':
        return `Triggers after ${triggerDelay} seconds of inactivity`;
      case 'linkClick':
        const targetLink = activeLinks.find(l => l._id === linkId);
        return `Triggers ${triggerDelay} seconds after clicking "${targetLink?.title || 'Unknown link'}"`;
      default:
        return '';
    }
  };

  return (
    <Card className={`mb-4 ${link.archived ? 'opacity-60' : ''}`}>
      {/* Enable/Disable Switch (when not editing) */}
      {!isEditing && onToggleEnabled && (
        <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
          <span className="text-xs text-muted-foreground">
            {link.enabled ? 'Visible' : 'Hidden'}
          </span>
          <Switch
            checked={link.enabled}
            onCheckedChange={handleToggleEnabled}
            aria-label={`${link.enabled ? 'Hide' : 'Show'} ${link.title} on public profile`}
          />
        </div>
      )}

      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl font-bold">
          {!isEditing ? link.title : 'Edit Lottie Animation'}
        </CardTitle>
        {!isEditing && (
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsEditing(true)}
              className="h-8 w-8"
              title="Edit"
            >
              <Edit size={16} />
            </Button>
            {onArchive && !link.archived && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onArchive(link._id)}
                className="h-8 w-8"
                title="Archive"
              >
                <Archive size={16} />
              </Button>
            )}
            {onUnarchive && link.archived && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onUnarchive(link._id)}
                className="h-8 w-8"
                title="Unarchive"
              >
                <RotateCcw size={16} />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(link._id)}
              className="h-8 w-8 text-destructive hover:text-destructive"
              title="Delete"
            >
              <Trash size={16} />
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {isEditing ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Animation Title"
              />
            </div>

            <div className="space-y-2">
              <Label>Lottie Animation Source</Label>
              <Tabs value={sourceType} onValueChange={(value) => setSourceType(value as 'url' | 'upload')}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="url">URL</TabsTrigger>
                  <TabsTrigger value="upload">Upload JSON</TabsTrigger>
                </TabsList>

                <TabsContent value="url" className="space-y-2">
                  <div className="relative">
                    <Input
                      id="lottieUrl"
                      value={lottieUrl}
                      onChange={(e) => setLottieUrl(e.target.value)}
                      placeholder="https://example.com/animation.json"
                      className={`${
                        preloadStatus === 'success' ? 'border-green-500 pr-10' :
                        preloadStatus === 'error' ? 'border-red-500 pr-10' :
                        preloadStatus === 'loading' ? 'pr-10' : ''
                      }`}
                    />
                    {preloadStatus === 'loading' && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                      </div>
                    )}
                    {preloadStatus === 'success' && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 6L9 17l-5-5"></path>
                        </svg>
                      </div>
                    )}
                    {preloadStatus === 'error' && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Enter the URL to a Lottie JSON file or a hosted Lottie animation
                    {preloadStatus === 'success' && (
                      <span className="text-green-500 ml-1">• Animation preloaded and ready</span>
                    )}
                    {preloadStatus === 'error' && (
                      <span className="text-red-500 ml-1">• Failed to preload animation, check URL</span>
                    )}
                  </p>
                </TabsContent>

                <TabsContent value="upload" className="space-y-2">
                  <LottieUploader
                    onJsonUploaded={handleJsonUploaded}
                    currentJsonUrl={lottieJsonUrl}
                  />
                  <p className="text-xs text-muted-foreground">
                    Upload a Lottie JSON file from your computer with live preview
                  </p>
                </TabsContent>
              </Tabs>
            </div>

            <div className="space-y-2">
              <Label htmlFor="triggerEvent">Trigger Event</Label>
              <Select
                value={triggerEvent}
                onValueChange={handleTriggerEventChange}
              >
                <SelectTrigger id="triggerEvent">
                  <SelectValue placeholder="Select trigger event" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pageLoad">Page Load</SelectItem>
                  <SelectItem value="inactivity">User Inactivity</SelectItem>
                  <SelectItem value="linkClick">Link Click</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="triggerDelay">
                {triggerEvent === 'pageLoad'
                  ? 'Delay after page load (seconds)'
                  : triggerEvent === 'inactivity'
                    ? 'Inactivity time before trigger (seconds)'
                    : 'Delay after link click (seconds)'}
              </Label>
              <div className="flex items-center gap-4">
                <Slider
                  id="triggerDelay"
                  value={[triggerDelay]}
                  min={0}
                  max={triggerEvent === 'inactivity' ? 60 : 10}
                  step={1}
                  onValueChange={(value: number[]) => setTriggerDelay(value[0])}
                  className="flex-1"
                />
                <span className="w-12 text-center">{triggerDelay}s</span>
              </div>
            </div>

            {showLinkSelector && (
              <div className="space-y-2">
                <Label htmlFor="linkId">Select Link to Trigger Animation</Label>
                <Select
                  value={linkId || "placeholder"}
                  onValueChange={setLinkId}
                >
                  <SelectTrigger id="linkId">
                    <SelectValue placeholder="Select a link" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="placeholder" disabled>Select a link</SelectItem>
                    {activeLinks.length === 0 ? (
                      <SelectItem value="no-links" disabled>No links available</SelectItem>
                    ) : (
                      activeLinks.map((link) => (
                        <SelectItem key={link._id} value={link._id}>
                          {link.title}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex justify-end gap-2 pt-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                Save Changes
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Zap size={16} className="text-primary" />
              <span className="text-sm font-medium">{getTriggerDescription()}</span>
            </div>
            {link.lottieUrl && (
              <div className="text-sm text-muted-foreground break-all">
                <span className="font-medium">Animation URL:</span> {link.lottieUrl}
              </div>
            )}
            {link.lottieJsonUrl && (
              <div className="text-sm text-muted-foreground break-all">
                <span className="font-medium">Uploaded JSON:</span> {link.lottieJsonUrl.split('/').pop()}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LottieAnimationCard;
