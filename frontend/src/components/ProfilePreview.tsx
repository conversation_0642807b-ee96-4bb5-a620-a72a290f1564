import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import { Link as LinkType } from '../api/links';
import { CountryProfile } from '../api/user';
import { countries } from '../data/countries';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { OptimizedImage } from './OptimizedImage';
import { YouTubeEmbed } from './YouTubeEmbed';
import { TikTokEmbed } from './TikTokEmbed';
import { VideoPlayer } from './VideoPlayer';
import { Lock, Play, Image as ImageIcon } from 'lucide-react';
import { Input } from './ui/input';
import { ThemeIsolator } from './ThemeIsolator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog';
import {
  FaTelegram, FaTiktok, FaFacebook, FaInstagram, FaTwitter,
  FaLinkedin, FaYoutube, FaGithub, FaDiscord, FaTwitch,
  FaReddit, FaPinterest, FaSnapchat, FaWhatsapp, FaSpotify,
  FaAmazon, FaApple, FaEtsy, FaGoogle, FaMedium, FaPatreon,
  FaPaypal, FaShopify, FaSoundcloud, FaStackOverflow, FaSteam,
  FaVimeo, FaWordpress, FaYelp, FaLink, FaStar
} from 'react-icons/fa';
import { DraggableWidget } from './DraggableWidget';
import { BackgroundImageUploader, BackgroundProperties } from './BackgroundImageUploader';
import DigitalProductStorefront from './DigitalProductStorefront';
import TipJarWidget from './TipJarWidget';

// Import badge animations
import '../styles/badge-animations.css';

interface ProfilePreviewProps {
  username: string;
  slug?: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: 'left' | 'center' | 'right';
  avatarShape?: 'circle' | 'square';
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
  backgroundSize?: 'auto' | 'cover';
  backgroundAttachment?: 'fixed' | 'scroll';
  links?: LinkType[];
  countryProfiles?: CountryProfile[];
  showCountrySelector?: boolean;
  verifiedProfile?: boolean;
  verifiedProfileGold?: boolean;
  onBackgroundImageChange?: (url: string) => void;
  onBackgroundPropertiesChange?: (properties: BackgroundProperties) => void;
  showBackgroundButton?: boolean;
  isPreview?: boolean; // Add this prop to indicate if this is a preview
}

// Helper function to render the appropriate icon
const renderIcon = (iconName: string) => {
  const iconProps = { size: 18 };
  const getIcon = () => {
    switch (iconName) {
      case 'telegram': return <FaTelegram {...iconProps} />;
      case 'tiktok': return <FaTiktok {...iconProps} />;
      case 'facebook': return <FaFacebook {...iconProps} />;
      case 'instagram': return <FaInstagram {...iconProps} />;
      case 'twitter': return <FaTwitter {...iconProps} />;
      case 'linkedin': return <FaLinkedin {...iconProps} />;
      case 'youtube': return <FaYoutube {...iconProps} />;
      case 'github': return <FaGithub {...iconProps} />;
      case 'discord': return <FaDiscord {...iconProps} />;
      case 'twitch': return <FaTwitch {...iconProps} />;
      case 'reddit': return <FaReddit {...iconProps} />;
      case 'pinterest': return <FaPinterest {...iconProps} />;
      case 'snapchat': return <FaSnapchat {...iconProps} />;
      case 'whatsapp': return <FaWhatsapp {...iconProps} />;
      case 'spotify': return <FaSpotify {...iconProps} />;
      case 'amazon': return <FaAmazon {...iconProps} />;
      case 'apple': return <FaApple {...iconProps} />;
      case 'etsy': return <FaEtsy {...iconProps} />;
      case 'google': return <FaGoogle {...iconProps} />;
      case 'medium': return <FaMedium {...iconProps} />;
      case 'patreon': return <FaPatreon {...iconProps} />;
      case 'paypal': return <FaPaypal {...iconProps} />;
      case 'shopify': return <FaShopify {...iconProps} />;
      case 'soundcloud': return <FaSoundcloud {...iconProps} />;
      case 'stackoverflow': return <FaStackOverflow {...iconProps} />;
      case 'steam': return <FaSteam {...iconProps} />;
      case 'vimeo': return <FaVimeo {...iconProps} />;
      case 'wordpress': return <FaWordpress {...iconProps} />;
      case 'yelp': return <FaYelp {...iconProps} />;
      default: return <FaLink {...iconProps} />;
    }
  };

  return getIcon();
};

export const ProfilePreview: React.FC<ProfilePreviewProps> = ({
  username,
  bio,
  avatarUrl,
  avatarPosition = 'center',
  avatarShape = 'circle',
  bannerUrl,
  bannerColor = '#e0f2fe', // Default light blue background
  pageBackgroundColor = '#f8f9fa',
  cardBackgroundColor = '#ffffff',
  fontColor = '#000000',
  fontFamily = '',
  backgroundImage = '',
  backgroundPosition = 'center',
  backgroundRepeat = 'no-repeat',
  backgroundSize = 'cover',
  backgroundAttachment = 'scroll',
  links = [],
  countryProfiles = [],
  showCountrySelector = false,
  verifiedProfile = false,
  verifiedProfileGold = false,
  onBackgroundImageChange,
  onBackgroundPropertiesChange,
  showBackgroundButton = false,
  isPreview = false, // Default to false
}) => {
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [showBackgroundWidget, setShowBackgroundWidget] = useState(false);
  const [localBackgroundImage, setLocalBackgroundImage] = useState<string>(backgroundImage || '');

  // Update localBackgroundImage when backgroundImage prop changes
  useEffect(() => {
    console.log('ProfilePreview: backgroundImage prop changed:', backgroundImage);
    setLocalBackgroundImage(backgroundImage || '');

    // Apply the background image directly to the DOM for immediate feedback
    if (backgroundImage) {
      const fullImageUrl = backgroundImage && !backgroundImage.startsWith('http') && !backgroundImage.startsWith('data:')
        ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${backgroundImage}`
        : backgroundImage;

      console.log('ProfilePreview: Applying background image on mount:', fullImageUrl);
      console.log('ProfilePreview: VITE_API_URL:', import.meta.env.VITE_API_URL);

      // Find the container and apply the background
      setTimeout(() => {
        const container = document.querySelector('.profile-preview-container');
        console.log('ProfilePreview: Container found:', !!container);
        if (container) {
          (container as HTMLElement).style.backgroundImage = `url(${fullImageUrl})`;
          console.log('ProfilePreview: Background image applied to container on mount');
          console.log('ProfilePreview: Container style:', (container as HTMLElement).style.cssText);
        } else {
          console.error('ProfilePreview: Container with class .profile-preview-container not found');
          // Try to find the container by other means
          const allContainers = document.querySelectorAll('div[style*="backgroundColor"]');
          console.log('ProfilePreview: Found', allContainers.length, 'potential containers');
        }
      }, 100);
    }
  }, [backgroundImage]);

  // Password protection state
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [passwordInput, setPasswordInput] = useState('');
  const [passwordError, setPasswordError] = useState(false);
  const [currentPasswordProtectedLinkId, setCurrentPasswordProtectedLinkId] = useState<string | null>(null);
  const [unlockedLinks, setUnlockedLinks] = useState<string[]>([]);
  const passwordDialogRef = useRef<HTMLDivElement>(null);

  // Get the active country profile if one is selected
  const activeCountryProfile = selectedCountry
    ? countryProfiles.find(profile => profile.countryCode === selectedCountry && profile.enabled)
    : null;

  // Ensure we have valid values for all customization options
  const effectiveBio = activeCountryProfile?.bio || bio || '';
  const effectiveAvatarUrl = activeCountryProfile?.avatarUrl || avatarUrl;
  const effectiveBannerUrl = activeCountryProfile?.bannerUrl || bannerUrl;
  const effectiveBannerColor = activeCountryProfile?.bannerColor || bannerColor || '#e0f2fe';
  const effectivePageBgColor = activeCountryProfile?.pageBackgroundColor || pageBackgroundColor || '#f8f9fa';
  const effectiveCardBgColor = activeCountryProfile?.cardBackgroundColor || cardBackgroundColor || '#ffffff';
  const effectiveFontColor = activeCountryProfile?.fontColor || fontColor || '#000000';
  const effectiveFontFamily = activeCountryProfile?.fontFamily || fontFamily || '';
  const effectiveAvatarPosition = activeCountryProfile?.avatarPosition as any || avatarPosition || 'center';
  const effectiveAvatarShape = activeCountryProfile?.avatarShape as any || avatarShape || 'circle';
  const effectiveBackgroundImage = activeCountryProfile?.backgroundImage || localBackgroundImage || '';
  const effectiveBackgroundPosition = activeCountryProfile?.backgroundPosition as any || backgroundPosition || 'center';
  const effectiveBackgroundRepeat = activeCountryProfile?.backgroundRepeat as any || backgroundRepeat || 'no-repeat';
  const effectiveBackgroundSize = activeCountryProfile?.backgroundSize as any || backgroundSize || 'cover';
  const effectiveBackgroundAttachment = activeCountryProfile?.backgroundAttachment as any || backgroundAttachment || 'scroll';

  // Log avatar position for debugging
  console.log('ProfilePreview: avatarPosition prop:', avatarPosition);
  console.log('ProfilePreview: effectiveAvatarPosition:', effectiveAvatarPosition);

  // Get enabled country profiles
  const enabledCountryProfiles = countryProfiles.filter(profile => profile.enabled);

  // Handle video click for password protected videos
  const handleVideoClick = (link: LinkType) => {
    console.log('Video clicked in preview:', link.title);

    // Check if the link is password protected
    if (link.passwordEnabled && !unlockedLinks.includes(link._id)) {
      // Show password dialog
      setCurrentPasswordProtectedLinkId(link._id);
      setPasswordInput('');
      setPasswordError(false);
      setShowPasswordDialog(true);
    } else {
      // Video is not password protected or already unlocked
      console.log('Video is not password protected or already unlocked');
    }
  };

  // Handle password submission
  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentPasswordProtectedLinkId) return;

    // Find the link
    const link = links.find(l => l._id === currentPasswordProtectedLinkId);

    if (!link) return;

    // Check if password is correct (in a real app, this would be verified by the server)
    if (passwordInput === link.password) {
      // Password correct
      setShowPasswordDialog(false);
      setPasswordInput('');
      setPasswordError(false);

      // Add link to unlocked links
      setUnlockedLinks(prev => [...prev, currentPasswordProtectedLinkId!]);
    } else {
      // Wrong password
      setPasswordError(true);

      // Add shaking animation
      if (passwordDialogRef.current) {
        passwordDialogRef.current.classList.add('shake-animation');
        setTimeout(() => {
          if (passwordDialogRef.current) {
            passwordDialogRef.current.classList.remove('shake-animation');
          }
        }, 2000);
      }
    }
  };

  // Handle background image change
  const handleBackgroundImageChange = (imageUrl: string) => {
    console.log('ProfilePreview: Background image changed to:', imageUrl);

    // Update local state
    setLocalBackgroundImage(imageUrl);

    if (onBackgroundImageChange) {
      // Call the parent component's callback to save the image URL
      console.log('ProfilePreview: Calling onBackgroundImageChange with:', imageUrl);
      onBackgroundImageChange(imageUrl);

      // Apply the background image immediately to the DOM for visual feedback
      const fullImageUrl = imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')
        ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${imageUrl}`
        : imageUrl;

      console.log('ProfilePreview: Applying background image directly to DOM:', fullImageUrl);
      console.log('ProfilePreview: VITE_API_URL in handleBackgroundImageChange:', import.meta.env.VITE_API_URL);

      // Use the ref to access the container directly
      if (containerRef.current) {
        containerRef.current.style.backgroundImage = fullImageUrl ? `url(${fullImageUrl})` : 'none';
        console.log('ProfilePreview: Background image applied to container via ref');
        console.log('ProfilePreview: Container style after applying image:', containerRef.current.style.cssText);
      } else {
        console.error('ProfilePreview: Container ref is not available');

        // Fallback: try to find the container by class
        const container = document.querySelector('.profile-preview-container');
        console.log('ProfilePreview: Container found by class selector:', !!container);

        if (container) {
          (container as HTMLElement).style.backgroundImage = fullImageUrl ? `url(${fullImageUrl})` : 'none';
          console.log('ProfilePreview: Background image applied to container by class');
          console.log('ProfilePreview: Container style after applying background:', (container as HTMLElement).style.cssText);
        } else {
          console.error('ProfilePreview: Container with class .profile-preview-container not found in handleBackgroundImageChange');

          // Try to find the container by other means
          const allContainers = document.querySelectorAll('div[style*="backgroundColor"]');
          console.log('ProfilePreview: Found', allContainers.length, 'potential containers in handleBackgroundImageChange');

          // Try to apply to all potential containers
          if (allContainers.length > 0) {
            console.log('ProfilePreview: Attempting to apply background to all potential containers');
            allContainers.forEach((el, index) => {
              (el as HTMLElement).style.backgroundImage = fullImageUrl ? `url(${fullImageUrl})` : 'none';
              console.log(`ProfilePreview: Applied background to potential container ${index}`);
            });
          }
        }
      }

      // Force a re-render to ensure the background image is updated in the React component
      setTimeout(() => {
        console.log('ProfilePreview: Forcing re-render after background image change');
        setShowBackgroundWidget(showBackgroundWidget); // This will trigger a re-render
      }, 100);
    } else {
      console.warn('ProfilePreview: onBackgroundImageChange callback is not defined');
    }
  };

  // Ref for the container element
  const containerRef = useRef<HTMLDivElement | null>(null);

  // Handle background properties change
  const handleBackgroundPropertiesChange = (properties: BackgroundProperties) => {
    console.log('ProfilePreview: Background properties changed:', properties);

    if (onBackgroundPropertiesChange) {
      // Call the parent component's callback to save the properties
      onBackgroundPropertiesChange(properties);

      // Apply the background properties immediately to the DOM for visual feedback
      console.log('ProfilePreview: Applying background properties directly to DOM:', properties);

      // Use the ref to access the container directly
      if (containerRef.current) {
        containerRef.current.style.backgroundPosition = properties.backgroundPosition;
        containerRef.current.style.backgroundRepeat = properties.backgroundRepeat;
        containerRef.current.style.backgroundSize = properties.backgroundSize;
        containerRef.current.style.backgroundAttachment = properties.backgroundAttachment;
        console.log('ProfilePreview: Background properties applied to container via ref');
        console.log('ProfilePreview: Container style after applying properties:', containerRef.current.style.cssText);
      } else {
        console.error('ProfilePreview: Container ref is not available');

        // Fallback: try to find the container by class
        const container = document.querySelector('.profile-preview-container');
        console.log('ProfilePreview: Container found by class selector:', !!container);

        if (container) {
          (container as HTMLElement).style.backgroundPosition = properties.backgroundPosition;
          (container as HTMLElement).style.backgroundRepeat = properties.backgroundRepeat;
          (container as HTMLElement).style.backgroundSize = properties.backgroundSize;
          (container as HTMLElement).style.backgroundAttachment = properties.backgroundAttachment;
          console.log('ProfilePreview: Background properties applied to container by class');
        } else {
          console.error('ProfilePreview: Container with class .profile-preview-container not found');

          // Try to find the container by other means
          const allContainers = document.querySelectorAll('div[style*="backgroundColor"]');
          console.log('ProfilePreview: Found', allContainers.length, 'potential containers');

          // Try to apply to all potential containers
          if (allContainers.length > 0) {
            console.log('ProfilePreview: Attempting to apply properties to all potential containers');
            allContainers.forEach((el, index) => {
              (el as HTMLElement).style.backgroundPosition = properties.backgroundPosition;
              (el as HTMLElement).style.backgroundRepeat = properties.backgroundRepeat;
              (el as HTMLElement).style.backgroundSize = properties.backgroundSize;
              (el as HTMLElement).style.backgroundAttachment = properties.backgroundAttachment;
              console.log(`ProfilePreview: Applied properties to potential container ${index}`);
            });
          }
        }
      }

      // Force a re-render to ensure the background properties are updated in the React component
      setTimeout(() => {
        console.log('ProfilePreview: Forcing re-render after background properties change');
        setShowBackgroundWidget(showBackgroundWidget); // This will trigger a re-render
      }, 100);
    }
  };

  // Log the background image URL for debugging
  console.log('ProfilePreview: backgroundImage prop:', backgroundImage);
  console.log('ProfilePreview: localBackgroundImage state:', localBackgroundImage);
  console.log('ProfilePreview: effectiveBackgroundImage:', effectiveBackgroundImage);
  console.log('ProfilePreview: VITE_API_URL:', import.meta.env.VITE_API_URL);
  console.log('ProfilePreview: isPreview:', isPreview);

  // Log the full URL that will be used
  const debugFullUrl = effectiveBackgroundImage && !effectiveBackgroundImage.startsWith('http') && !effectiveBackgroundImage.startsWith('data:')
    ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${effectiveBackgroundImage}`
    : effectiveBackgroundImage;
  console.log('ProfilePreview: Full background image URL that will be used:', debugFullUrl);

  // Prepare the background image URL
  const backgroundImageUrl = effectiveBackgroundImage && !effectiveBackgroundImage.startsWith('http') && !effectiveBackgroundImage.startsWith('data:')
    ? `${import.meta.env.VITE_API_URL.replace('/api', '')}${effectiveBackgroundImage}`
    : effectiveBackgroundImage;

  console.log('ProfilePreview: backgroundImageUrl:', backgroundImageUrl);

  // Create a unique key for the container to force re-render when background changes
  const containerKey = `profile-preview-${backgroundImageUrl || 'none'}-${effectiveBackgroundPosition}-${effectiveBackgroundRepeat}-${effectiveBackgroundSize}-${effectiveBackgroundAttachment}`;

  // Error handling state
  const [hasError, setHasError] = useState(false);

  // Error boundary effect
  useEffect(() => {
    // Reset error state when key props change
    setHasError(false);
  }, [containerKey, links, countryProfiles]);

  // If there was an error rendering, show a simplified version
  if (hasError) {
    return (
      <ThemeIsolator>
        <div className="p-4 rounded-lg flex justify-center">
          <div className="w-full max-w-xl bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-center mb-4">Preview Error</h3>
            <p className="text-sm text-gray-600 text-center">
              There was an error rendering the preview. Please check your settings and try again.
            </p>
          </div>
        </div>
      </ThemeIsolator>
    );
  }

  // Wrap the render in a try-catch to handle any errors
  try {
    return (
      <ThemeIsolator>
        <div
          key={containerKey}
          style={{
            backgroundColor: effectivePageBgColor,
            backgroundImage: backgroundImageUrl ? `url(${backgroundImageUrl})` : 'none',
            backgroundPosition: effectiveBackgroundPosition,
            backgroundRepeat: effectiveBackgroundRepeat,
            backgroundSize: effectiveBackgroundSize,
            backgroundAttachment: effectiveBackgroundAttachment,
            minHeight: '100%',
            width: '100%',
            position: 'relative'
          }}
          className="p-4 rounded-lg flex justify-center profile-preview-container"
          ref={(el) => {
            // Store the ref for later use
            containerRef.current = el;

            if (el && backgroundImageUrl) {
              console.log('ProfilePreview: Container ref mounted with backgroundImageUrl:', backgroundImageUrl);
              console.log('ProfilePreview: Container style on mount:', el.style.cssText);
            }
          }}
          onError={() => {
            console.error('Error in ProfilePreview container');
            setHasError(true);
          }}
        >
      {/* Custom Background Button - only show when showBackgroundButton is true */}
      {showBackgroundButton && (
        <Button
          variant="outline"
          size="sm"
          className="absolute top-2 left-2 z-10 bg-white/80 hover:bg-white"
          onClick={() => setShowBackgroundWidget(!showBackgroundWidget)}
        >
          <ImageIcon size={16} className="mr-1" />
          <span className="text-xs">Page Background</span>
        </Button>
      )}

      {/* Background Widget - positioned with absolute positioning */}
      {showBackgroundButton && showBackgroundWidget && (
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="pointer-events-auto">
            <DraggableWidget
              title="Page Background"
              onClose={() => setShowBackgroundWidget(false)}
              initialPosition={{ x: 50, y: 50 }}
              className="bg-white dark:bg-gray-900"
            >
              <BackgroundImageUploader
                onImageUploaded={handleBackgroundImageChange}
                onPropertiesChanged={handleBackgroundPropertiesChange}
                currentImage={effectiveBackgroundImage}
                currentProperties={{
                  backgroundPosition: effectiveBackgroundPosition,
                  backgroundRepeat: effectiveBackgroundRepeat,
                  backgroundSize: effectiveBackgroundSize,
                  backgroundAttachment: effectiveBackgroundAttachment
                }}
              />
            </DraggableWidget>
          </div>
        </div>
      )}

      {/* Main content container - always centered */}
      <div className="w-full max-w-xl relative z-10"> {/* Fixed width container for content, matching live page */}

      {/* Verified Profile Badge - positioned above the card */}
      {verifiedProfile && (
        <div className="flex items-center gap-1 px-2 py-1 mb-2 w-fit rounded-full bg-blue-500/70 badge-popup text-white text-xs font-medium">
          <FaStar size={10} />
          <span>Verified profile</span>
        </div>
      )}

      {/* Verified Profile Gold Badge */}
      {verifiedProfileGold && (
        <div className="flex items-center gap-1 px-2 py-1 mb-2 w-fit rounded-full bg-amber-500/70 badge-popup text-white text-xs font-medium">
          <FaStar size={10} />
          <span>Verified profile</span>
        </div>
      )}

      <Card className="overflow-hidden" style={{ backgroundColor: effectiveCardBgColor }}>
        <div className="relative">
          {/* Banner */}
          <div
            className="h-[138px] relative"
            style={{ backgroundColor: effectiveBannerColor }}
          >
            {effectiveBannerUrl ? (
              <OptimizedImage
                src={effectiveBannerUrl}
                alt="Profile banner"
                className="w-full h-full object-cover"
                priority={true}
                placeholder="blur"
                blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjE5MiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZTBmMmZlIi8+PC9zdmc+"
              />
            ) : (
              <div className="w-full h-full" style={{ backgroundColor: effectiveBannerColor }}></div>
            )}





            {/* Country selector */}
            {showCountrySelector && enabledCountryProfiles.length > 0 && (
              <div className="absolute top-2 left-2 z-10">
                <Select
                  value={selectedCountry || 'default'}
                  onValueChange={(value) => setSelectedCountry(value === 'default' ? null : value)}
                >
                  <SelectTrigger className="w-[140px] bg-white/80 hover:bg-white text-xs h-8">
                    <SelectValue placeholder="Default Profile" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Profile</SelectItem>
                    {enabledCountryProfiles.map((profile) => {
                      try {
                        const country = countries.find(c => c.code === profile.countryCode);
                        // Add null check for profile.countryCode
                        if (!profile.countryCode) return null;

                        // Validate the profile data
                        console.log('Rendering country profile:', profile);

                        return (
                          <SelectItem key={profile.countryCode} value={profile.countryCode}>
                            {country ? country.name : profile.countryCode}
                          </SelectItem>
                        );
                      } catch (error) {
                        console.error('Error rendering country profile:', error, profile);
                        return null; // Skip this item if there's an error
                      }
                    })}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Avatar */}
          <div
            className={`absolute -bottom-10 ${
              effectiveAvatarPosition === 'left'
                ? 'left-6'
                : effectiveAvatarPosition === 'right'
                  ? 'right-6'
                  : 'left-0 right-0 mx-auto'
            } flex ${effectiveAvatarPosition === 'center' ? 'justify-center' : ''}`}
            ref={(el) => {
              if (el) {
                console.log('ProfilePreview: Avatar element classes:', el.className);
                console.log('ProfilePreview: Avatar element style:', el.style.cssText);
              }
            }}
          >
            {effectiveAvatarUrl ? (
              <OptimizedImage
                src={effectiveAvatarUrl}
                alt={username}
                className={`w-20 h-20 ${
                  effectiveAvatarShape === 'square' ? 'rounded-lg' : 'rounded-full'
                } border-4 border-background shadow-md object-cover`}
                priority={false}
                placeholder="blur"
                blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjE2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+"
              />
            ) : (
              <div
                className={`w-20 h-20 ${
                  effectiveAvatarShape === 'square' ? 'rounded-lg' : 'rounded-full'
                } border-4 border-background shadow-md bg-[#f0f0f0] flex items-center justify-center text-2xl font-bold text-gray-400`}
              >
                {username.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </div>

        <CardContent
          className="pt-4 pb-4 text-center"
          style={{
            color: effectiveFontColor,
            fontFamily: effectiveFontFamily && effectiveFontFamily !== 'default' ? `'${effectiveFontFamily}', sans-serif` : undefined
          }}
        >
          <h3 className="font-bold text-lg">{username}</h3>
          {effectiveBio && (
            <div
              className="text-sm mt-1 mb-3 bio-content"
              style={{ color: effectiveFontColor }}
              dangerouslySetInnerHTML={{ __html: effectiveBio }}
            />
          )}

          {/* Display components */}
          {links && links.length > 0 && (
            <div className="space-y-2 mt-4">
              {links.slice(0, 3).map((link) => {
                // Render different components based on type
                switch (link.type) {
                  case 'announcement':
                    return (
                      <div
                        key={link._id}
                        className="w-full p-3 rounded-lg text-sm relative"
                        style={
                          link.useGradient && link.backgroundGradient
                            ? {
                                background: `linear-gradient(${link.backgroundGradient.direction}, ${link.backgroundGradient.color1}, ${link.backgroundGradient.color2})`,
                                color: link.textColor || '#000000'
                              }
                            : {
                                backgroundColor: link.backgroundColor || '#f3f4f6',
                                color: link.textColor || '#000000'
                              }
                        }
                      >
                        <h4
                          className="font-medium"
                          style={{ textAlign: link.titleAlignment || 'left' }}
                        >
                          {link.title}
                        </h4>
                        <div
                          className="text-xs mt-1"
                          style={{ textAlign: link.textAlignment || 'left' }}
                          dangerouslySetInnerHTML={{ __html: link.text || '' }}
                        />

                        {link.buttonText && link.url && (
                          <div className="mt-3">
                            <Button
                              size="sm"
                              className="w-full"
                              style={{
                                backgroundColor: link.buttonBackgroundColor || '#3b82f6',
                                color: link.buttonTextColor || '#ffffff'
                              }}
                            >
                              {link.buttonText}
                            </Button>
                          </div>
                        )}

                        {link.url && !link.buttonText && (
                          <div className="text-xs mt-1 underline">
                            Learn more
                          </div>
                        )}
                      </div>
                    );

                  case 'shuffle':
                    return (
                      <div
                        key={link._id}
                        className="w-full rounded-lg border border-border text-sm relative bg-white/50"
                      >
                        <h4 className="font-medium mb-2 px-2 pt-2">{link.title}</h4>
                        <div className="h-64 overflow-hidden relative">
                          {link.shuffleCards && link.shuffleCards.length > 0 && (
                            <div className="relative h-full w-full flex justify-center">
                              {link.shuffleCards.slice(0, 1).map((card, index) => (
                                <div key={index} className="h-full w-36">
                                  {card.imageUrl && (
                                    <div className="relative h-full w-full overflow-hidden rounded-md">
                                      <OptimizedImage
                                        src={card.imageUrl.startsWith('http') ? card.imageUrl : `${import.meta.env.VITE_API_URL.replace('/api', '')}${card.imageUrl}`}
                                        alt={card.title}
                                        className="w-full h-full object-cover"
                                        priority={false}
                                        placeholder="blur"
                                        blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2YzZjRmNiIvPjwvc3ZnPg=="
                                      />
                                      {/* Tag at top right */}
                                      {card.tag && (
                                        <div className="absolute top-2 right-2 bg-blue-500 text-white text-[10px] px-1.5 py-0.5 rounded-full">
                                          {card.tag}
                                        </div>
                                      )}
                                      {/* Title at bottom */}
                                      <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-[10px] p-2 truncate">
                                        {card.title}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    );

                  case 'minilead':
                    return (
                      <div
                        key={link._id}
                        className="w-full p-3 rounded-lg border border-border text-sm relative bg-white/50"
                      >
                        <h4 className="font-medium">{link.title}</h4>
                        <p className="text-xs mt-1 mb-3">{link.introText}</p>
                        <div className="space-y-2">
                          {link.fields && link.fields.map((field, index) => (
                            <div key={index} className="flex flex-col">
                              <label className="text-xs font-medium mb-1">
                                {(field.label || field.type).charAt(0).toUpperCase() + (field.label || field.type).slice(1)}
                                {field.required && <span className="text-red-500">*</span>}
                              </label>
                              <div className="w-full p-1.5 text-xs border rounded-md bg-background/50">
                                {field.type === 'email' && '✉️'}
                                {field.type === 'phone' && '📱'}
                                {field.type === 'activity' && '📝'}
                                {field.label || field.type}
                              </div>
                            </div>
                          ))}
                          <div className="w-full mt-2 p-1.5 bg-primary text-primary-foreground rounded-md text-xs font-medium text-center">
                            Submit
                          </div>
                        </div>
                      </div>
                    );

                  case 'video':
                    return (
                      <div
                        key={link._id}
                        className="w-full rounded-lg border border-border text-sm relative bg-white/50"
                      >
                        <h4 className="font-medium mb-2 px-2 pt-2">{link.title}</h4>
                        {link.thumbnailUrl && (
                          <div className="relative cursor-pointer">
                            {/* Use VideoPlayer only if we have both video and thumbnail and either not password protected or already unlocked */}
                            {link.videoUrl && (!link.passwordEnabled || unlockedLinks.includes(link._id)) ? (
                              <VideoPlayer
                                videoUrl={link.videoUrl}
                                thumbnailUrl={link.thumbnailUrl}
                                orientation={link.orientation || 'landscape'}
                                autoPlay={false}
                                controls={true}
                                onClick={() => console.log('Video played in preview')}
                              />
                            ) : (
                              <div
                                className="relative"
                                onClick={() => handleVideoClick(link)}
                                style={{
                                  aspectRatio: link.orientation === 'portrait' ? '9/16' : '16/9',
                                  maxWidth: link.orientation === 'portrait' ? '240px' : '100%',
                                  margin: '0 auto'
                                }}
                              >
                                <OptimizedImage
                                  src={link.thumbnailUrl.startsWith('http') ? link.thumbnailUrl : `${import.meta.env.VITE_API_URL.replace('/api', '')}${link.thumbnailUrl}`}
                                  alt={link.title}
                                  className="w-full h-full object-cover rounded-md"
                                  priority={false}
                                  placeholder="blur"
                                  blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMjAyMDIwIi8+PC9zdmc+"
                                />
                                <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-md">
                                  <div className="bg-white/80 rounded-full p-2">
                                    {link.passwordEnabled && !unlockedLinks.includes(link._id) ? (
                                      <Lock className="text-amber-600" size={24} />
                                    ) : (
                                      <Play className="text-primary" size={24} />
                                    )}
                                  </div>
                                </div>
                                {link.videoUrl && (
                                  <div className="absolute bottom-2 right-2 bg-black/70 text-white text-[10px] px-2 py-1 rounded-full">
                                    {link.videoUrl.includes('bunnycdn') || link.videoUrl.includes('.m3u8') ? 'Bunny CDN' : 'Video'}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                        {link.passwordEnabled && (
                          <div className="absolute -top-2 left-2 px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 flex items-center gap-1">
                            <Lock size={10} />
                            <span className="text-[10px]">Protected</span>
                          </div>
                        )}
                      </div>
                    );

                  case 'embedded':
                    return (
                      <div
                        key={link._id}
                        className="w-full rounded-lg border border-border text-sm relative bg-white/50"
                      >
                        <h4 className="font-medium mb-2 px-2 pt-2">{link.title}</h4>

                        {/* Show content only if not password protected or already unlocked */}
                        {!link.passwordEnabled || unlockedLinks.includes(link._id) ? (
                          <>
                            {/* YouTube embed */}
                            {(link.embedType === 'youtube' || !link.embedType) && link.youtubeCode && (
                              <div className="relative">
                                <YouTubeEmbed
                                  youtubeCode={link.youtubeCode}
                                  title={link.title}
                                  autoplay={false} // Don't autoplay in the preview
                                  muted={link.muted !== false} // Default to true if undefined
                                  showControls={link.showControls !== false} // Default to true if undefined
                                  onClick={() => console.log('YouTube video played in preview')}
                                />
                              </div>
                            )}

                            {/* TikTok embed */}
                            {link.embedType === 'tiktok' && link.tikTokUrl && (
                              <div className="relative">
                                <TikTokEmbed
                                  tikTokUrl={link.tikTokUrl}
                                  title={link.title}
                                  onClick={() => console.log('TikTok video played in preview')}
                                />
                              </div>
                            )}
                          </>
                        ) : (
                          /* Password protected content */
                          <div
                            className="relative cursor-pointer"
                            onClick={() => handleVideoClick(link)}
                            style={{ aspectRatio: '16/9' }}
                          >
                            <div className="w-full h-full bg-gray-100 rounded-md flex items-center justify-center">
                              <div className="text-center">
                                <Lock className="mx-auto text-amber-600 mb-2" size={32} />
                                <p className="text-sm text-gray-600">Password Protected Content</p>
                                <p className="text-xs text-gray-500 mt-1">Click to unlock</p>
                              </div>
                            </div>
                          </div>
                        )}

                        {link.passwordEnabled && (
                          <div className="absolute -top-2 left-2 px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 flex items-center gap-1">
                            <Lock size={10} />
                            <span className="text-[10px]">Protected</span>
                          </div>
                        )}
                      </div>
                    );

                  case 'digitalproduct':
                    return (
                      <div key={link._id} className="w-full">
                        <DigitalProductStorefront link={link} isPreview={true} />
                      </div>
                    );

                  case 'tipjar':
                    return (
                      <div key={link._id} className="w-full">
                        <TipJarWidget link={link} isPreview={true} />
                      </div>
                    );

                  case 'link':
                  default:
                    return (
                      <div
                        key={link._id}
                        className="w-full p-2 rounded-lg border border-border flex items-center gap-2 text-sm relative"
                        style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}
                      >
                        {link.badge && (
                          <div
                            className={`absolute -top-2 -right-1 px-2 py-0.5 rounded-full text-xs font-medium ${link.badge.animation ? `badge-${link.badge.animation}` : ''}`}
                            style={{
                              backgroundColor: link.badge.backgroundColor || 'rgba(59, 130, 246, 0.2)',
                              color: link.badge.textColor || '#3b82f6'
                            }}
                          >
                            {link.badge.text}
                          </div>
                        )}
                        <div className="text-primary flex items-center justify-center w-5 h-5">
                          {renderIcon(link.icon || 'link')}
                        </div>
                        <span className="flex-1 text-left truncate" style={{ color: effectiveFontColor }}>
                          {link.title}
                        </span>
                      </div>
                    );
                }
              })}
              {links.length > 3 && (
                <div className="text-xs text-muted-foreground mt-1">
                  +{links.length - 3} more components
                </div>
              )}
            </div>
          )}
        </CardContent>
    </Card>

      {/* Password Dialog */}
      <AlertDialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <AlertDialogContent
          ref={passwordDialogRef}
          className={`${passwordError ? 'password-error' : ''} fixed-center-dialog`}
        >
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Lock size={16} className="text-amber-600" />
              Password Protected Content
            </AlertDialogTitle>
            <AlertDialogDescription>
              This content is password protected. Please enter the password to continue.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <form onSubmit={handlePasswordSubmit} className="py-4">
            <Input
              type="password"
              placeholder="Enter password"
              value={passwordInput}
              onChange={(e) => setPasswordInput(e.target.value)}
              className={`w-full ${passwordError ? 'border-red-500' : ''}`}
              autoFocus
            />
            {passwordError && (
              <p className="text-red-500 text-sm mt-2">Incorrect password. Please try again.</p>
            )}
          </form>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setShowPasswordDialog(false);
              setPasswordInput('');
              setPasswordError(false);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handlePasswordSubmit}>
              Submit
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </div>
    </ThemeIsolator>
    );
  } catch (error) {
    console.error('Error rendering ProfilePreview:', error);
    setHasError(true);
    return (
      <ThemeIsolator>
        <div className="p-4 rounded-lg flex justify-center">
          <div className="w-full max-w-xl bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-center mb-4">Preview Error</h3>
            <p className="text-sm text-gray-600 text-center">
              There was an error rendering the preview. Please check your settings and try again.
            </p>
            <p className="text-xs text-gray-500 text-center mt-2">
              Error: {error instanceof Error ? error.message : String(error)}
            </p>
          </div>
        </div>
      </ThemeIsolator>
    );
  }
};
