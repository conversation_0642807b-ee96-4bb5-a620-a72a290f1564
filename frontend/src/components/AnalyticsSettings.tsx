import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { updateProfile } from '../api/user';

const AnalyticsSettings = () => {
  const { user, updateUserData } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [googleAnalyticsId, setGoogleAnalyticsId] = useState(user?.googleAnalyticsId || '');
  const [microsoftClarityId, setMicrosoftClarityId] = useState(user?.microsoftClarityId || '');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const updatedUser = await updateProfile({
        googleAnalyticsId,
        microsoftClarityId
      });

      updateUserData(updatedUser);
      setMessage('Analytics settings updated successfully!');
    } catch (error: any) {
      setMessage(`Error: ${error.response?.data?.message || 'Failed to update analytics settings'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Analytics Settings</CardTitle>
        <CardDescription>
          Configure analytics tracking for your profile page
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {message && (
            <div className={`p-3 rounded-md ${message.includes('Error') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
              {message}
            </div>
          )}

          <div className="space-y-4">
            <div>
              <Label htmlFor="googleAnalyticsId">Google Analytics ID (GA4)</Label>
              <div className="mt-1">
                <Input
                  id="googleAnalyticsId"
                  type="text"
                  placeholder="G-XXXXXXXXXX"
                  value={googleAnalyticsId}
                  onChange={(e) => setGoogleAnalyticsId(e.target.value)}
                />
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Enter your Google Analytics 4 measurement ID to track page views and events
              </p>
            </div>

            <div>
              <Label htmlFor="microsoftClarityId">Microsoft Clarity ID</Label>
              <div className="mt-1">
                <Input
                  id="microsoftClarityId"
                  type="text"
                  placeholder="xxxxxxxxxx"
                  value={microsoftClarityId}
                  onChange={(e) => setMicrosoftClarityId(e.target.value)}
                />
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Enter your Microsoft Clarity project ID to enable heatmaps and session recordings
              </p>
            </div>
          </div>

          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Analytics Settings'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default AnalyticsSettings;
