import { useState } from 'react';
import { <PERSON> } from '../api/links';
import { <PERSON>, CardContent, CardFooter } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Switch } from './ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog';
import { Pencil, Archive, ArchiveRestore, Trash2, GripVertical, ShoppingBag } from 'lucide-react';

interface DigitalProductCardProps {
  link: Link;
  onEdit: (link: Link) => void;
  onDelete: (id: string) => void;
  onArchive?: (id: string) => void;
  onUnarchive?: (id: string) => void;
  onToggleEnabled: (id: string, enabled: boolean) => void;
  isDraggable?: boolean;
  dragHandleProps?: any;
}

const DigitalProductCard = ({
  link,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  onToggleEnabled,
  isDraggable = false,
  dragHandleProps = {}
}: DigitalProductCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [title, setTitle] = useState(link.title);
  const [description, setDescription] = useState(link.description || '');
  const [productType, setProductType] = useState(link.productType || 'other');
  const [price, setPrice] = useState(link.price?.toString() || '0');
  const [currency, setCurrency] = useState(link.currency || 'USD');
  const [downloadUrl, setDownloadUrl] = useState(link.downloadUrl || '');
  const [fileSize, setFileSize] = useState(link.fileSize || '');
  const [fileFormat, setFileFormat] = useState(link.fileFormat || '');
  const [enabled, setEnabled] = useState(link.enabled);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    onEdit({
      ...link,
      title,
      description,
      productType: productType as any,
      price: parseFloat(price),
      currency,
      downloadUrl,
      fileSize,
      fileFormat,
      enabled
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTitle(link.title);
    setDescription(link.description || '');
    setProductType(link.productType || 'other');
    setPrice(link.price?.toString() || '0');
    setCurrency(link.currency || 'USD');
    setDownloadUrl(link.downloadUrl || '');
    setFileSize(link.fileSize || '');
    setFileFormat(link.fileFormat || '');
    setEnabled(link.enabled);
    setIsEditing(false);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    onDelete(link._id);
    setIsDeleteDialogOpen(false);
  };

  const handleArchive = () => {
    if (onArchive) {
      onArchive(link._id);
    }
  };

  const handleUnarchive = () => {
    if (onUnarchive) {
      onUnarchive(link._id);
    }
  };

  const handleToggleEnabled = () => {
    const newEnabledState = !enabled;
    setEnabled(newEnabledState);
    onToggleEnabled(link._id, newEnabledState);
  };

  if (isEditing) {
    return (
      <Card className="mb-4 border-l-4 border-l-purple-500">
        <CardContent className="p-4">
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-title">Product Title</Label>
              <Input
                id="edit-title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mt-1"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-type">Product Type</Label>
                <Select value={productType} onValueChange={(value) => setProductType(value as any)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="template">Template</SelectItem>
                    <SelectItem value="preset">Preset</SelectItem>
                    <SelectItem value="guide">Guide</SelectItem>
                    <SelectItem value="ebook">E-book</SelectItem>
                    <SelectItem value="course">Course</SelectItem>
                    <SelectItem value="software">Software</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="edit-currency">Currency</Label>
                <Select value={currency} onValueChange={setCurrency}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="AUD">AUD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="edit-price">Price</Label>
              <Input
                id="edit-price"
                type="number"
                step="0.01"
                min="0"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="edit-download-url">Download URL</Label>
              <Input
                id="edit-download-url"
                type="url"
                value={downloadUrl}
                onChange={(e) => setDownloadUrl(e.target.value)}
                className="mt-1"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-file-size">File Size</Label>
                <Input
                  id="edit-file-size"
                  value={fileSize}
                  onChange={(e) => setFileSize(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="edit-file-format">File Format</Label>
                <Input
                  id="edit-file-format"
                  value={fileFormat}
                  onChange={(e) => setFileFormat(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <>
      <Card className="mb-4 border-l-4 border-l-purple-500">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1">
              {isDraggable && (
                <div {...dragHandleProps} className="cursor-grab">
                  <GripVertical className="h-5 w-5 text-muted-foreground" />
                </div>
              )}
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <ShoppingBag className="h-5 w-5 text-purple-600" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold truncate">{link.title}</h3>
                <p className="text-sm text-muted-foreground truncate">
                  {link.productType} • {link.currency} {link.price}
                  {link.fileSize && ` • ${link.fileSize}`}
                </p>
                {link.description && (
                  <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                    {link.description}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2 ml-4">
              <Switch
                checked={enabled}
                onCheckedChange={handleToggleEnabled}
                aria-label="Toggle visibility"
              />
              <Button variant="ghost" size="icon" onClick={handleEdit}>
                <Pencil className="h-4 w-4" />
              </Button>
              {onArchive && (
                <Button variant="ghost" size="icon" onClick={handleArchive}>
                  <Archive className="h-4 w-4" />
                </Button>
              )}
              {onUnarchive && (
                <Button variant="ghost" size="icon" onClick={handleUnarchive}>
                  <ArchiveRestore className="h-4 w-4" />
                </Button>
              )}
              <Button variant="ghost" size="icon" onClick={handleDelete}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Digital Product</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this digital product? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DigitalProductCard;
