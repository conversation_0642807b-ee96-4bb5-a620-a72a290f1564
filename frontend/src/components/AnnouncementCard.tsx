import { useState } from 'react';
import { Link } from '../api/links';
import { <PERSON>, CardContent, CardFooter } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog';
import { Pencil, Archive, ArchiveRestore, Trash2, GripVertical, Megaphone, AlignLeft, AlignCenter, AlignRight } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from './ui/tabs';
import RichTextEditor from './RichTextEditor';
import GradientPicker from './GradientPicker';

interface AnnouncementCardProps {
  link: Link;
  onEdit: (
    id: string,
    title: string,
    text: string,
    backgroundColor: string,
    textColor: string,
    url?: string,
    backgroundGradient?: {
      color1: string;
      color2: string;
      direction: string;
    },
    useGradient?: boolean,
    buttonText?: string,
    buttonBackgroundColor?: string,
    buttonTextColor?: string,
    titleAlignment?: 'left' | 'center' | 'right',
    textAlignment?: 'left' | 'center' | 'right'
  ) => void;
  onDelete: (id: string) => void;
  onArchive?: (id: string) => void;
  onUnarchive?: (id: string) => void;
  onToggleEnabled?: (id: string, enabled: boolean) => void;
  isDraggable?: boolean;
  dragHandleProps?: any;
}

const AnnouncementCard = ({
  link,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  onToggleEnabled,
  isDraggable = false,
  dragHandleProps
}: AnnouncementCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const [title, setTitle] = useState(link.title);
  const [text, setText] = useState(link.text || '');
  const [url, setUrl] = useState(link.url || '');
  const [backgroundColor, setBackgroundColor] = useState(link.backgroundColor || '#f3f4f6');
  const [textColor, setTextColor] = useState(link.textColor || '#000000');
  const [useGradient, setUseGradient] = useState(link.useGradient || false);
  const [gradientColor1, setGradientColor1] = useState(link.backgroundGradient?.color1 || '#8B5CF6');
  const [gradientColor2, setGradientColor2] = useState(link.backgroundGradient?.color2 || '#EC4899');
  const [gradientDirection, setGradientDirection] = useState(link.backgroundGradient?.direction || 'to right');
  const [titleAlignment, setTitleAlignment] = useState(link.titleAlignment || 'left');
  const [textAlignment, setTextAlignment] = useState(link.textAlignment || 'left');
  const [buttonText, setButtonText] = useState(link.buttonText || 'Learn More');
  const [buttonBackgroundColor, setButtonBackgroundColor] = useState(link.buttonBackgroundColor || '#3b82f6');
  const [buttonTextColor, setButtonTextColor] = useState(link.buttonTextColor || '#ffffff');
  const [showPreview, setShowPreview] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);

  const handleSave = () => {
    onEdit(
      link._id,
      title,
      text,
      backgroundColor,
      textColor,
      url || undefined,
      {
        color1: gradientColor1,
        color2: gradientColor2,
        direction: gradientDirection
      },
      useGradient,
      buttonText,
      buttonBackgroundColor,
      buttonTextColor,
      titleAlignment,
      textAlignment
    );
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTitle(link.title);
    setText(link.text || '');
    setUrl(link.url || '');
    setBackgroundColor(link.backgroundColor || '#f3f4f6');
    setTextColor(link.textColor || '#000000');
    setUseGradient(link.useGradient || false);
    setGradientColor1(link.backgroundGradient?.color1 || '#8B5CF6');
    setGradientColor2(link.backgroundGradient?.color2 || '#EC4899');
    setGradientDirection(link.backgroundGradient?.direction || 'to right');
    setTitleAlignment(link.titleAlignment || 'left');
    setTextAlignment(link.textAlignment || 'left');
    setButtonText(link.buttonText || 'Learn More');
    setButtonBackgroundColor(link.buttonBackgroundColor || '#3b82f6');
    setButtonTextColor(link.buttonTextColor || '#ffffff');
    setActiveTab('content');
    setShowPreview(false);
    setIsEditing(false);
  };

  const handleDelete = () => {
    setShowDeleteDialog(false);
    onDelete(link._id);
  };

  const handleArchive = () => {
    setShowArchiveDialog(false);
    if (link.archived && onUnarchive) {
      onUnarchive(link._id);
    } else if (!link.archived && onArchive) {
      onArchive(link._id);
    }
  };

  const handleToggleEnabled = (enabled: boolean) => {
    if (onToggleEnabled) {
      onToggleEnabled(link._id, enabled);
    }
  };

  return (
    <Card className="mb-3 relative">
      {/* Enable/Disable Switch (when not editing) */}
      {!isEditing && onToggleEnabled && (
        <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
          <span className="text-xs text-muted-foreground">
            {link.enabled ? 'Visible' : 'Hidden'}
          </span>
          <Switch
            checked={link.enabled}
            onCheckedChange={handleToggleEnabled}
            aria-label={`${link.enabled ? 'Hide' : 'Show'} ${link.title} on public profile`}
          />
        </div>
      )}

      {/* Disabled overlay */}
      {!isEditing && !link.enabled && (
        <div className="absolute inset-0 bg-gray-200/50 z-5 flex items-center justify-center">
          <div className="bg-white/80 px-3 py-1 rounded-md text-sm text-gray-500 font-medium">
            Hidden from public profile
          </div>
        </div>
      )}
      <CardContent className={`pt-4 pb-2 ${isEditing ? '' : 'flex items-center'}`}>
        {/* Drag handle */}
        {isDraggable && !isEditing && (
          <div
            className="mr-2 cursor-grab text-muted-foreground hover:text-foreground"
            {...dragHandleProps}
          >
            <GripVertical size={16} />
          </div>
        )}

        {isEditing ? (
          <div className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="style">Style</TabsTrigger>
                <TabsTrigger value="button">Button</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <Label htmlFor="title" className="block text-sm font-medium">
                      Title
                    </Label>
                    <div className="flex space-x-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className={`p-1 h-7 w-7 ${titleAlignment === 'left' ? 'bg-muted' : ''}`}
                        onClick={() => setTitleAlignment('left')}
                      >
                        <AlignLeft className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className={`p-1 h-7 w-7 ${titleAlignment === 'center' ? 'bg-muted' : ''}`}
                        onClick={() => setTitleAlignment('center')}
                      >
                        <AlignCenter className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className={`p-1 h-7 w-7 ${titleAlignment === 'right' ? 'bg-muted' : ''}`}
                        onClick={() => setTitleAlignment('right')}
                      >
                        <AlignRight className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Post Title"
                    required
                    className="text-base"
                    style={{ textAlign: titleAlignment }}
                  />
                </div>
                <div>
                  <div className="mb-1">
                    <Label htmlFor="text" className="block text-sm font-medium">
                      Text
                    </Label>
                  </div>
                  <RichTextEditor
                    value={text}
                    onChange={setText}
                    placeholder="Post text content"
                    className="min-h-[120px]"
                  />
                </div>
                <div>
                  <Label htmlFor="url" className="block text-sm font-medium mb-1">
                    URL (Optional)
                  </Label>
                  <Input
                    id="url"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>
              </TabsContent>

              <TabsContent value="style" className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Switch
                    id="useGradient"
                    checked={useGradient}
                    onCheckedChange={setUseGradient}
                  />
                  <Label htmlFor="useGradient" className="cursor-pointer">
                    Use Gradient Background
                  </Label>
                </div>

                {useGradient ? (
                  <GradientPicker
                    color1={gradientColor1}
                    color2={gradientColor2}
                    direction={gradientDirection}
                    onChange={(color1, color2, direction) => {
                      setGradientColor1(color1);
                      setGradientColor2(color2);
                      setGradientDirection(direction);
                    }}
                    label="Background Gradient"
                  />
                ) : (
                  <div>
                    <Label className="block text-sm font-medium mb-1">
                      Background Color
                    </Label>
                    <div className="flex items-center gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-10 h-10 p-0 border"
                            style={{ backgroundColor }}
                          />
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-3">
                          <HexColorPicker color={backgroundColor} onChange={setBackgroundColor} />
                        </PopoverContent>
                      </Popover>
                      <Input
                        value={backgroundColor}
                        onChange={(e) => setBackgroundColor(e.target.value)}
                        className="font-mono"
                      />
                    </div>
                  </div>
                )}

                <div>
                  <Label className="block text-sm font-medium mb-1">
                    Text Color
                  </Label>
                  <div className="flex items-center gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-10 h-10 p-0 border"
                          style={{ backgroundColor: textColor }}
                        />
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-3">
                        <HexColorPicker color={textColor} onChange={setTextColor} />
                      </PopoverContent>
                    </Popover>
                    <Input
                      value={textColor}
                      onChange={(e) => setTextColor(e.target.value)}
                      className="font-mono"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="button" className="space-y-4">
                <div>
                  <Label htmlFor="buttonText" className="block text-sm font-medium mb-1">
                    Button Text
                  </Label>
                  <Input
                    id="buttonText"
                    value={buttonText}
                    onChange={(e) => setButtonText(e.target.value)}
                    placeholder="Learn More"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="block text-sm font-medium mb-1">
                      Button Background
                    </Label>
                    <div className="flex items-center gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-10 h-10 p-0 border"
                            style={{ backgroundColor: buttonBackgroundColor }}
                          />
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-3">
                          <HexColorPicker color={buttonBackgroundColor} onChange={setButtonBackgroundColor} />
                        </PopoverContent>
                      </Popover>
                      <Input
                        value={buttonBackgroundColor}
                        onChange={(e) => setButtonBackgroundColor(e.target.value)}
                        className="font-mono"
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="block text-sm font-medium mb-1">
                      Button Text Color
                    </Label>
                    <div className="flex items-center gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-10 h-10 p-0 border"
                            style={{ backgroundColor: buttonTextColor }}
                          />
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-3">
                          <HexColorPicker color={buttonTextColor} onChange={setButtonTextColor} />
                        </PopoverContent>
                      </Popover>
                      <Input
                        value={buttonTextColor}
                        onChange={(e) => setButtonTextColor(e.target.value)}
                        className="font-mono"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-4 border rounded-md">
                  <p className="text-sm font-medium mb-2">Button Preview:</p>
                  <Button
                    className="w-full"
                    style={{
                      backgroundColor: buttonBackgroundColor,
                      color: buttonTextColor
                    }}
                  >
                    {buttonText || 'Learn More'}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <>
            <div className="text-primary flex items-center justify-center w-6 h-6 flex-shrink-0">
              <Megaphone size={18} />
            </div>
            <div className="flex-1 ml-3 min-w-0">
              <h3 className="font-medium truncate">{link.title}</h3>
              <div
                className="p-3 mt-1 rounded"
                style={
                  link.useGradient && link.backgroundGradient
                    ? {
                        background: `linear-gradient(${link.backgroundGradient.direction}, ${link.backgroundGradient.color1}, ${link.backgroundGradient.color2})`,
                        color: link.textColor || '#000000'
                      }
                    : {
                        backgroundColor: link.backgroundColor || '#f3f4f6',
                        color: link.textColor || '#000000'
                      }
                }
              >
                <div
                  className="text-sm preview-content"
                  style={{ textAlign: link.textAlignment || 'left' }}
                  dangerouslySetInnerHTML={{ __html: link.text || '' }}
                />

                {link.buttonText && (
                  <div className="mt-3">
                    <Button
                      size="sm"
                      className="w-full"
                      style={{
                        backgroundColor: link.buttonBackgroundColor || '#3b82f6',
                        color: link.buttonTextColor || '#ffffff'
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        if (link.url) {
                          window.open(link.url, '_blank', 'noopener,noreferrer');
                        }
                      }}
                    >
                      {link.buttonText}
                    </Button>
                  </div>
                )}
              </div>

              {link.url && !link.buttonText && (
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-500 hover:underline truncate block mt-1"
                >
                  {link.url}
                </a>
              )}

              <div className="text-xs text-muted-foreground mt-1">
                Clicks: {link.clickCount}
              </div>
            </div>
            <div className="flex gap-1 ml-2 flex-shrink-0">
              <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setIsEditing(true)}>
                <Pencil className="h-4 w-4" />
              </Button>
              {link.archived ? (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowArchiveDialog(true)}
                >
                  <ArchiveRestore className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowArchiveDialog(true)}
                >
                  <Archive className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}
      </CardContent>

      {isEditing && (
        <>
          {showPreview && (
            <div className="px-4 pb-4">
              <div className="border rounded-md p-4 mb-4">
                <h3 className="text-sm font-medium mb-2">Live Preview</h3>
                <div
                  className="p-3 rounded-lg"
                  style={
                    useGradient
                      ? {
                          background: `linear-gradient(${gradientDirection}, ${gradientColor1}, ${gradientColor2})`,
                          color: textColor
                        }
                      : {
                          backgroundColor: backgroundColor,
                          color: textColor
                        }
                  }
                >
                  <h4
                    className="font-medium"
                    style={{ textAlign: titleAlignment }}
                  >
                    {title}
                  </h4>
                  <div
                    className="text-xs mt-1 preview-content"
                    style={{ textAlign: textAlignment }}
                    dangerouslySetInnerHTML={{ __html: text || '' }}
                  />

                  {buttonText && url && (
                    <div className="mt-3">
                      <Button
                        size="sm"
                        className="w-full"
                        style={{
                          backgroundColor: buttonBackgroundColor,
                          color: buttonTextColor
                        }}
                      >
                        {buttonText}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          <CardFooter className="flex justify-between gap-2 pt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>Save</Button>
            </div>
          </CardFooter>
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this post. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Archive Confirmation Dialog */}
      <AlertDialog open={showArchiveDialog} onOpenChange={setShowArchiveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {link.archived ? 'Unarchive Post?' : 'Archive Post?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {link.archived
                ? 'This will restore the post to your active list.'
                : 'This will move the post to your archived items.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleArchive}>
              {link.archived ? 'Unarchive' : 'Archive'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

export default AnnouncementCard;
