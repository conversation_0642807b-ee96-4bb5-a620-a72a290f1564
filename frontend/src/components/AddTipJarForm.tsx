import { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Switch } from './ui/switch';
import { Badge } from './ui/badge';
import { X } from 'lucide-react';

interface AddTipJarFormProps {
  onAdd: (
    title: string,
    tipMessage: string,
    thankYouMessage: string,
    tipAmounts: number[],
    customAmountEnabled: boolean,
    minTipAmount: number,
    maxTipAmount: number
  ) => void;
}

const AddTipJarForm = ({ onAdd }: AddTipJarFormProps) => {
  const [title, setTitle] = useState('');
  const [tipMessage, setTipMessage] = useState('Support my work with a tip!');
  const [thankYouMessage, setThankYouMessage] = useState('Thank you for your generous tip!');
  const [tipAmounts, setTipAmounts] = useState<number[]>([5, 10, 25]);
  const [customAmountEnabled, setCustomAmountEnabled] = useState(true);
  const [minTipAmount, setMinTipAmount] = useState('1');
  const [maxTipAmount, setMaxTipAmount] = useState('1000');
  const [newTipAmount, setNewTipAmount] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && tipMessage.trim() && thankYouMessage.trim()) {
      onAdd(
        title,
        tipMessage,
        thankYouMessage,
        tipAmounts,
        customAmountEnabled,
        parseFloat(minTipAmount),
        parseFloat(maxTipAmount)
      );
      
      // Reset form
      setTitle('');
      setTipMessage('Support my work with a tip!');
      setThankYouMessage('Thank you for your generous tip!');
      setTipAmounts([5, 10, 25]);
      setCustomAmountEnabled(true);
      setMinTipAmount('1');
      setMaxTipAmount('1000');
      setNewTipAmount('');
    }
  };

  const addTipAmount = () => {
    const amount = parseFloat(newTipAmount);
    if (amount > 0 && !tipAmounts.includes(amount) && tipAmounts.length < 6) {
      setTipAmounts([...tipAmounts, amount].sort((a, b) => a - b));
      setNewTipAmount('');
    }
  };

  const removeTipAmount = (amount: number) => {
    setTipAmounts(tipAmounts.filter(a => a !== amount));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTipAmount();
    }
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Add Tip Jar</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="tip-title">Tip Jar Title</Label>
          <Input
            id="tip-title"
            placeholder="Buy me a coffee ☕"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1"
            required
          />
        </div>

        <div>
          <Label htmlFor="tip-message">Tip Message</Label>
          <Textarea
            id="tip-message"
            placeholder="Support my work with a tip!"
            value={tipMessage}
            onChange={(e) => setTipMessage(e.target.value)}
            className="mt-1"
            rows={2}
            required
          />
        </div>

        <div>
          <Label htmlFor="thank-you-message">Thank You Message</Label>
          <Textarea
            id="thank-you-message"
            placeholder="Thank you for your generous tip!"
            value={thankYouMessage}
            onChange={(e) => setThankYouMessage(e.target.value)}
            className="mt-1"
            rows={2}
            required
          />
        </div>

        <div>
          <Label>Preset Tip Amounts</Label>
          <div className="mt-2 space-y-2">
            <div className="flex flex-wrap gap-2">
              {tipAmounts.map((amount) => (
                <Badge key={amount} variant="secondary" className="flex items-center gap-1">
                  ${amount}
                  <button
                    type="button"
                    onClick={() => removeTipAmount(amount)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            
            {tipAmounts.length < 6 && (
              <div className="flex gap-2">
                <Input
                  type="number"
                  step="0.01"
                  min="0.01"
                  placeholder="Add amount"
                  value={newTipAmount}
                  onChange={(e) => setNewTipAmount(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="w-32"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTipAmount}
                  disabled={!newTipAmount || tipAmounts.length >= 6}
                >
                  Add
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="custom-amount"
            checked={customAmountEnabled}
            onCheckedChange={setCustomAmountEnabled}
          />
          <Label htmlFor="custom-amount">Allow custom tip amounts</Label>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="min-tip">Minimum Tip Amount</Label>
            <Input
              id="min-tip"
              type="number"
              step="0.01"
              min="0.01"
              placeholder="1.00"
              value={minTipAmount}
              onChange={(e) => setMinTipAmount(e.target.value)}
              className="mt-1"
              required
            />
          </div>

          <div>
            <Label htmlFor="max-tip">Maximum Tip Amount</Label>
            <Input
              id="max-tip"
              type="number"
              step="0.01"
              min="1"
              placeholder="1000.00"
              value={maxTipAmount}
              onChange={(e) => setMaxTipAmount(e.target.value)}
              className="mt-1"
              required
            />
          </div>
        </div>

        <Button type="submit" className="w-full">
          Add Tip Jar
        </Button>
      </form>
    </div>
  );
};

export default AddTipJarForm;
