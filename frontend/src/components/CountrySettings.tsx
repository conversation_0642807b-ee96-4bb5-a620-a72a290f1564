import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Switch } from './ui/switch';
import { countries } from '../data/countries';
import { CountryProfile, updateCountryProfile, deleteCountryProfile } from '../api/user';
import { Search } from 'lucide-react';

const CountrySettings = () => {
  const { user, updateUserData } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [countryProfiles, setCountryProfiles] = useState<CountryProfile[]>([]);

  useEffect(() => {
    if (user && 'countryProfiles' in user && Array.isArray(user.countryProfiles)) {
      setCountryProfiles(user.countryProfiles);
    }
  }, [user]);

  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleCountry = async (countryCode: string, enabled: boolean) => {
    setIsLoading(true);
    setMessage('');

    try {
      // If the country profile doesn't exist yet, create it
      const existingProfile = countryProfiles.find(profile => profile.countryCode === countryCode);

      if (!existingProfile && enabled) {
        // Create new country profile with default values from main profile
        const newProfile: CountryProfile = {
          countryCode,
          bio: user?.bio || '',
          avatarUrl: user?.avatarUrl || '',
          avatarPosition: user?.avatarPosition as any || 'center',
          avatarShape: user?.avatarShape as any || 'circle',
          bannerUrl: user?.bannerUrl || '',
          bannerColor: user?.bannerColor || '#ffffff',
          pageBackgroundColor: user?.pageBackgroundColor || '#f8f9fa',
          cardBackgroundColor: user?.cardBackgroundColor || '#ffffff',
          fontColor: user?.fontColor || '#000000',
          enabled: true
        };

        const updatedUser = await updateCountryProfile(newProfile);
        updateUserData(updatedUser);
        setMessage(`Country profile for ${getCountryName(countryCode)} created successfully!`);
      }
      else if (existingProfile) {
        // Update existing profile
        const updatedProfile = {
          ...existingProfile,
          enabled
        };

        const updatedUser = await updateCountryProfile(updatedProfile);
        updateUserData(updatedUser);

        if (enabled) {
          setMessage(`Country profile for ${getCountryName(countryCode)} enabled!`);
        } else {
          setMessage(`Country profile for ${getCountryName(countryCode)} disabled!`);
        }
      }
    } catch (error: any) {
      setMessage(`Error: ${error.response?.data?.message || 'Failed to update country profile'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCountry = async (countryCode: string) => {
    if (!confirm(`Are you sure you want to delete the profile for ${getCountryName(countryCode)}?`)) {
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const updatedUser = await deleteCountryProfile(countryCode);
      updateUserData(updatedUser);
      setMessage(`Country profile for ${getCountryName(countryCode)} deleted successfully!`);
    } catch (error: any) {
      setMessage(`Error: ${error.response?.data?.message || 'Failed to delete country profile'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getCountryName = (code: string): string => {
    const country = countries.find(c => c.code === code);
    return country ? country.name : code;
  };

  const isCountryEnabled = (code: string): boolean => {
    const profile = countryProfiles.find(p => p.countryCode === code);
    return profile ? profile.enabled : false;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Country Settings</CardTitle>
        <CardDescription>
          Configure different profiles for different countries. Each country can have its own profile settings.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {message && (
          <div className={`p-3 rounded-md ${message.includes('Error') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
            {message}
          </div>
        )}

        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search countries..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="border rounded-md divide-y max-h-96 overflow-y-auto">
          {filteredCountries.map((country) => (
            <div key={country.code} className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <span className="text-lg">{country.code}</span>
                <span>{country.name}</span>
              </div>
              <div className="flex items-center gap-3">
                <Switch
                  checked={isCountryEnabled(country.code)}
                  onCheckedChange={(checked: boolean) => handleToggleCountry(country.code, checked)}
                  disabled={isLoading}
                />
                {isCountryEnabled(country.code) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteCountry(country.code)}
                    disabled={isLoading}
                  >
                    Delete
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default CountrySettings;
