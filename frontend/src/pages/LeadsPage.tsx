import { useState, useEffect } from 'react';
import { getLeads, deleteLead, getLeadStats } from '../api/leads';
import { getLinks } from '../api/links';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
import { Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart2 } from 'lucide-react';
import { format } from 'date-fns';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Loader2 } from 'lucide-react';

const LeadsPage = () => {
  const [leads, setLeads] = useState<any[]>([]);
  const [links, setLinks] = useState<any[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);
  const [deleteLeadId, setDeleteLeadId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('leads');

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError('');

      try {
        // Get all mini lead links
        const linksData = await getLinks();
        const miniLeadLinks = linksData.filter(link => link.type === 'minilead');
        setLinks(miniLeadLinks);

        try {
          // Get leads
          const leadsData = await getLeads();
          setLeads(leadsData);
        } catch (leadsErr: any) {
          console.error('Failed to load leads:', leadsErr);
          setLeads([]);
        }

        try {
          // Get stats
          const statsData = await getLeadStats();
          console.log('Lead stats data:', statsData);
          setStats(statsData);
        } catch (statsErr: any) {
          console.error('Failed to load stats:', statsErr);
          setStats(null);
        }
      } catch (err: any) {
        console.error('Failed to load data:', err);
        setError(err.response?.data?.message || 'Failed to load data. Please try again.');
        setLinks([]);
        setLeads([]);
        setStats(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleLinkFilterChange = async (linkId: string) => {
    console.log('Filter change - received linkId:', linkId);

    // If linkId is an empty string, set it to null
    const newLinkId = linkId === '' ? null : linkId;
    console.log('Setting selectedLinkId to:', newLinkId);

    setSelectedLinkId(newLinkId);
    setIsLoading(true);
    setError('');

    try {
      console.log('Calling API with linkId:', newLinkId || undefined);
      const leadsData = await getLeads(newLinkId || undefined);
      console.log('API response - leads data:', leadsData);
      setLeads(leadsData);
    } catch (err: any) {
      console.error('Failed to filter leads:', err);
      setError(err.response?.data?.message || 'Failed to load leads. Please try again.');
      // Keep the previous leads data
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteLead = async () => {
    if (!deleteLeadId) return;

    try {
      await deleteLead(deleteLeadId);
      setLeads(leads.filter(lead => lead._id !== deleteLeadId));
      setDeleteLeadId(null);

      // Refresh stats after deleting a lead
      try {
        const statsData = await getLeadStats();
        setStats(statsData);
      } catch (statsErr) {
        console.error('Failed to refresh stats after delete:', statsErr);
      }
    } catch (err: any) {
      console.error('Failed to delete lead:', err);
      setError(err.response?.data?.message || 'Failed to delete lead. Please try again.');
      setDeleteLeadId(null);
    }
  };

  // Log the leads and selectedLinkId for debugging
  console.log('Selected Link ID:', selectedLinkId);
  console.log('Leads data structure:', leads.length > 0 ? leads[0] : 'No leads');

  // Handle both string and object ID comparison
  const filteredLeads = selectedLinkId
    ? leads.filter(lead => {
        // The linkId could be in different formats due to population:
        // 1. A string (the ID itself)
        // 2. An object with _id property (populated document)
        // 3. An ObjectId (MongoDB object)

        let leadLinkId;

        if (typeof lead.linkId === 'string') {
          leadLinkId = lead.linkId;
        } else if (lead.linkId && typeof lead.linkId === 'object') {
          // It could be a populated document or an ObjectId
          if (lead.linkId._id) {
            // It's a populated document
            leadLinkId = lead.linkId._id.toString();
          } else {
            // It might be an ObjectId or another object
            leadLinkId = lead.linkId.toString();
          }
        } else {
          // Fallback
          leadLinkId = String(lead.linkId);
        }

        console.log('Comparing lead linkId:', leadLinkId, 'with selectedLinkId:', selectedLinkId);
        return leadLinkId === selectedLinkId;
      })
    : leads;

  if (isLoading && !leads.length) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Leads</h1>
        <div className="text-center p-8 flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Loading lead data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Leads</h1>

      {error && (
        <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="leads">
            <FileText className="h-4 w-4 mr-2" />
            Leads
          </TabsTrigger>
          <TabsTrigger value="stats">
            <BarChart2 className="h-4 w-4 mr-2" />
            Statistics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="leads">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Lead Submissions</CardTitle>
                {links.length > 0 ? (
                  <Select
                    value={selectedLinkId || "all"}
                    onValueChange={(value) => handleLinkFilterChange(value === "all" ? "" : value)}
                  >
                    <SelectTrigger className="w-[250px]">
                      <SelectValue placeholder="Filter by form" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Forms</SelectItem>
                      {links.map(link => (
                        <SelectItem key={link._id} value={link._id}>
                          {link.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    No mini lead forms available
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center p-8 flex flex-col items-center justify-center">
                  <Loader2 className="h-6 w-6 animate-spin text-primary mb-2" />
                  <p className="text-muted-foreground text-sm">Loading leads...</p>
                </div>
              ) : filteredLeads.length === 0 ? (
                <div className="text-center p-8 bg-muted/20 rounded-lg">
                  <p className="text-muted-foreground">
                    No leads found. Create a Mini Lead form to start collecting leads.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Form</TableHead>
                        <TableHead>Data</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLeads.map(lead => (
                        <TableRow key={lead._id}>
                          <TableCell>
                            {format(new Date(lead.createdAt), 'MMM d, yyyy HH:mm')}
                          </TableCell>
                          <TableCell>{lead.title}</TableCell>
                          <TableCell>
                            <div className="max-w-md">
                              {Object.entries(lead.formData).map(([key, value]) => (
                                <div key={key} className="text-sm">
                                  <span className="font-medium">{key}: </span>
                                  <span>{value as string}</span>
                                </div>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setDeleteLeadId(lead._id)}
                              className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-100"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Total Leads</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-primary">
                  {stats?.totalLeads || 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {!stats ? (
            <Card>
              <CardContent className="text-center p-8">
                <p className="text-muted-foreground">Loading statistics...</p>
              </CardContent>
            </Card>
          ) : stats.totalLeads === 0 ? (
            <Card>
              <CardContent className="text-center p-8">
                <p className="text-muted-foreground">No lead data available yet. Start collecting leads to see statistics.</p>
              </CardContent>
            </Card>
          ) : (
            <>
              {stats.leadsPerDay && stats.leadsPerDay.length > 0 && (
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle>Leads Over Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={stats.leadsPerDay}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="count" fill="#3b82f6" name="Leads" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle>Leads by Form</CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.leadsPerForm && stats.leadsPerForm.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Form</TableHead>
                            <TableHead className="text-right">Leads</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {stats.leadsPerForm.map((item: any) => (
                            <TableRow key={item._id}>
                              <TableCell>{item.title || 'Unknown Form'}</TableCell>
                              <TableCell className="text-right">{item.count}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center p-4 bg-muted/20 rounded-lg">
                      <p className="text-muted-foreground">No form data available. This could be because the forms associated with these leads no longer exist.</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteLeadId} onOpenChange={(open) => !open && setDeleteLeadId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this lead. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteLead} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default LeadsPage;
