import { useState, useEffect } from 'react';
import { getStats, LinkWithStats, getDirectRedirectStats, DirectRedirectStatsResponse } from '../api/stats';
import { useAuth } from '../context/AuthContext';
import StatsChart from '../components/StatsChart';
import PieChartStats from '../components/PieChartStats';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import ReportFilters, { ReportFiltersValues } from '../components/ReportFilters';
import { DateRange } from 'react-day-picker';
import { subDays } from 'date-fns';

const StatsPage = () => {
  // We don't need user anymore since we removed PDF export
  const { } = useAuth();
  const [stats, setStats] = useState<{
    totalClicks: number;
    linksWithStats: LinkWithStats[];
    clicksPerDay: { date: string; count: number }[];
  }>({
    totalClicks: 0,
    linksWithStats: [],
    clicksPerDay: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [filteredStats, setFilteredStats] = useState<{
    totalClicks: number;
    linksWithStats: LinkWithStats[];
    clicksPerDay: { date: string; count: number }[];
  }>({
    totalClicks: 0,
    linksWithStats: [],
    clicksPerDay: [],
  });

  // State for direct redirect stats
  const [directRedirectStats, setDirectRedirectStats] = useState<DirectRedirectStatsResponse>({
    totalRedirects: 0,
    redirectsPerDay: [],
  });

  // Default to last 30 days
  const defaultDateRange: DateRange = {
    from: subDays(new Date(), 30),
    to: new Date()
  };

  const [filters, setFilters] = useState<ReportFiltersValues>({
    dateRange: defaultDateRange
  });

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);

      try {
        // Fetch regular stats
        const data = await getStats();
        setStats(data);
        setFilteredStats(data); // Initialize filtered stats with all data

        // Fetch direct redirect stats
        try {
          // Convert date range to the format expected by the API
          const dateRange = filters.dateRange ? {
            startDate: filters.dateRange.from,
            endDate: filters.dateRange.to
          } : undefined;

          const redirectData = await getDirectRedirectStats(dateRange);
          setDirectRedirectStats(redirectData);
        } catch (redirectErr: any) {
          console.error('Failed to load direct redirect stats:', redirectErr);
          // Don't set the main error state, just log it
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load statistics');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [filters.dateRange]);

  // Sample data for OS and device type charts
  const osData = [
    { name: 'Windows', value: 45 },
    { name: 'Mac', value: 25 },
    { name: 'iOS', value: 15 },
    { name: 'Android', value: 10 },
    { name: 'Other', value: 5 }
  ];

  const deviceData = [
    { name: 'Desktop', value: 65 },
    { name: 'Mobile', value: 30 },
    { name: 'Tablet', value: 5 }
  ];

  // Apply filters to the stats data
  useEffect(() => {
    if (!stats) return;

    let filteredClicksPerDay = [...stats.clicksPerDay];
    let filteredLinksWithStats = [...stats.linksWithStats];

    // Apply date range filter
    if (filters.dateRange?.from && filters.dateRange?.to) {
      filteredClicksPerDay = filteredClicksPerDay.filter(day => {
        const date = new Date(day.date);
        return filters.dateRange?.from && filters.dateRange?.to
          ? date >= filters.dateRange.from && date <= filters.dateRange.to
          : true;
      });
    }

    // Calculate total clicks from filtered data
    const totalClicks = filteredClicksPerDay.reduce((sum, day) => sum + day.count, 0);

    setFilteredStats({
      totalClicks,
      linksWithStats: filteredLinksWithStats,
      clicksPerDay: filteredClicksPerDay
    });
  }, [stats, filters]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-50 text-red-500 rounded-md">{error}</div>
      </div>
    );
  }

  // Handle filter changes
  const handleFilterChange = (newFilters: ReportFiltersValues) => {
    setFilters(newFilters);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Statistics</h1>

      <div className="mb-8">
        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Clicks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{filteredStats.totalClicks}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Links
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{filteredStats.linksWithStats.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Average Clicks Per Link
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {filteredStats.linksWithStats.length > 0
                ? Math.round(filteredStats.totalClicks / filteredStats.linksWithStats.length)
                : 0}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mb-8">
        <StatsChart data={filteredStats.clicksPerDay} title="Click Statistics" />
      </div>

      {/* Direct Redirect Stats Section */}
      {directRedirectStats.totalRedirects > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Direct Redirect Statistics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Direct Redirects
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{directRedirectStats.totalRedirects}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Average Daily Redirects
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {directRedirectStats.redirectsPerDay.length > 0
                    ? Math.round(directRedirectStats.totalRedirects / directRedirectStats.redirectsPerDay.length)
                    : 0}
                </div>
              </CardContent>
            </Card>
          </div>
          <StatsChart data={directRedirectStats.redirectsPerDay} title="Direct Redirect Statistics" />
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <PieChartStats data={osData} title="Operating Systems" />
        <PieChartStats data={deviceData} title="Device Types" />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Link Performance</h2>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4">Link</th>
                <th className="text-right py-3 px-4">Clicks</th>
                <th className="text-right py-3 px-4">% of Total</th>
              </tr>
            </thead>
            <tbody>
              {filteredStats.linksWithStats
                .sort((a, b) => b.totalClicks - a.totalClicks)
                .map((link) => (
                  <tr key={link._id} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4">
                      <div className="font-medium">{link.title}</div>
                      <div className="text-sm text-muted-foreground truncate max-w-xs">
                        {link.url}
                      </div>
                    </td>
                    <td className="text-right py-3 px-4 font-medium">
                      {link.totalClicks}
                    </td>
                    <td className="text-right py-3 px-4">
                      {filteredStats.totalClicks > 0
                        ? `${Math.round((link.totalClicks / filteredStats.totalClicks) * 100)}%`
                        : '0%'}
                    </td>
                  </tr>
                ))}
              {filteredStats.linksWithStats.length === 0 && (
                <tr>
                  <td colSpan={3} className="py-8 text-center text-muted-foreground">
                    No link data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>


    </div>
  );
};

export default StatsPage;
