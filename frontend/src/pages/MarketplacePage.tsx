import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { getDigitalProducts, DigitalProduct } from '../api/digitalProducts';
import { getTipAnalytics } from '../api/tips';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Switch } from '../components/ui/switch';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { ShoppingBag, DollarSign, TrendingUp, Package, Heart, BarChart3 } from 'lucide-react';

interface TipJarStats {
  totalTips: number;
  totalAmount: number;
  averageTip: number;
  uniqueTippers: number;
}

const MarketplacePage = () => {
  const { user } = useAuth();
  const [digitalProducts, setDigitalProducts] = useState<DigitalProduct[]>([]);
  const [tipJarStats, setTipJarStats] = useState<TipJarStats>({
    totalTips: 0,
    totalAmount: 0,
    averageTip: 0,
    uniqueTippers: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [marketplaceEnabled, setMarketplaceEnabled] = useState(user?.marketplaceEnabled || false);
  const [tipJarEnabled, setTipJarEnabled] = useState(user?.tipJarEnabled || true);
  const [defaultCurrency, setDefaultCurrency] = useState(user?.defaultCurrency || 'USD');

  useEffect(() => {
    fetchMarketplaceData();
  }, []);

  const fetchMarketplaceData = async () => {
    try {
      setIsLoading(true);

      // Fetch digital products
      const productsResponse = await getDigitalProducts();
      setDigitalProducts(productsResponse.products);

      // Fetch tip jar analytics
      const tipsResponse = await getTipAnalytics();
      setTipJarStats({
        totalTips: tipsResponse.summary.totalTips,
        totalAmount: tipsResponse.summary.totalAmount,
        averageTip: tipsResponse.summary.averageTip,
        uniqueTippers: tipsResponse.uniqueTippersCount
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load marketplace data');
      // Set empty data on error
      setDigitalProducts([]);
      setTipJarStats({
        totalTips: 0,
        totalAmount: 0,
        averageTip: 0,
        uniqueTippers: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateSettings = async () => {
    try {
      // TODO: Implement API call to update marketplace settings
      // const updatedUser = await updateProfile({
      //   marketplaceEnabled,
      //   tipJarEnabled,
      //   defaultCurrency
      // });
      // updateUserData(updatedUser);

      // Mock success for now
      console.log('Settings updated:', { marketplaceEnabled, tipJarEnabled, defaultCurrency });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update settings');
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-3 mb-6">
        <ShoppingBag className="h-8 w-8 text-primary" />
        <h1 className="text-3xl font-bold">Digital Marketplace</h1>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 text-red-500 rounded-md">
          {error}
        </div>
      )}

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Digital Products</p>
                <p className="text-2xl font-bold">{digitalProducts.length}</p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sales</p>
                <p className="text-2xl font-bold">
                  {digitalProducts.reduce((sum, product) => sum + product.purchaseCount, 0)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Tips Received</p>
                <p className="text-2xl font-bold">{tipJarStats.totalTips}</p>
              </div>
              <Heart className="h-8 w-8 text-pink-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">
                  ${(digitalProducts.reduce((sum, product) => sum + (product.price * product.purchaseCount), 0) + tipJarStats.totalAmount).toFixed(2)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="products" className="space-y-6">
        <TabsList>
          <TabsTrigger value="products">Digital Products</TabsTrigger>
          <TabsTrigger value="tips">Tip Jar Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Your Digital Products</CardTitle>
            </CardHeader>
            <CardContent>
              {digitalProducts.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Digital Products Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Create digital product components in your dashboard to start selling.
                  </p>
                  <Button asChild>
                    <a href="/dashboard">Go to Dashboard</a>
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {digitalProducts.map((product) => (
                    <Card key={product._id}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-semibold truncate">{product.title}</h3>
                          <Badge variant={product.isActive ? "default" : "secondary"}>
                            {product.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {product.description}
                        </p>
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium">{product.currency} {product.price}</span>
                          <span className="text-muted-foreground">
                            {product.purchaseCount} sales
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tips">
          <Card>
            <CardHeader>
              <CardTitle>Tip Jar Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div className="text-center p-4 bg-pink-50 rounded-lg">
                  <Heart className="h-8 w-8 text-pink-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-pink-700">{tipJarStats.totalTips}</p>
                  <p className="text-sm text-pink-600">Total Tips</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <DollarSign className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-green-700">${tipJarStats.totalAmount.toFixed(2)}</p>
                  <p className="text-sm text-green-600">Total Amount</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <BarChart3 className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-blue-700">${tipJarStats.averageTip.toFixed(2)}</p>
                  <p className="text-sm text-blue-600">Average Tip</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Package className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-purple-700">{tipJarStats.uniqueTippers}</p>
                  <p className="text-sm text-purple-600">Unique Tippers</p>
                </div>
              </div>

              {tipJarStats.totalTips === 0 && (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Tips Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Add a tip jar component to your profile to start receiving tips.
                  </p>
                  <Button asChild>
                    <a href="/dashboard">Add Tip Jar</a>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Marketplace Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="marketplace-enabled" className="text-base font-medium">
                    Enable Digital Product Marketplace
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow visitors to purchase your digital products directly from your profile
                  </p>
                </div>
                <Switch
                  id="marketplace-enabled"
                  checked={marketplaceEnabled}
                  onCheckedChange={(checked) => setMarketplaceEnabled(checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="tipjar-enabled" className="text-base font-medium">
                    Enable Tip Jar
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow visitors to send you tips and virtual gifts
                  </p>
                </div>
                <Switch
                  id="tipjar-enabled"
                  checked={tipJarEnabled}
                  onCheckedChange={(checked) => setTipJarEnabled(checked)}
                />
              </div>

              <div>
                <Label htmlFor="default-currency" className="text-base font-medium">
                  Default Currency
                </Label>
                <Select value={defaultCurrency} onValueChange={setDefaultCurrency}>
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                    <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={handleUpdateSettings} className="w-full">
                Save Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MarketplacePage;
