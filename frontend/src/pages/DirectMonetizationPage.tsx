import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { updateProfile } from '../api/user';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Loader2, DollarSign, Copy, Check, Link2, CheckCircle2, XCircle } from 'lucide-react';
import { Separator } from '../components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';


// Generate a thank you page URL with UUID
const generateThankYouUrl = (baseUrl: string, slug: string): string => {
  // Generate a UUID v4
  const uuid = crypto.randomUUID();
  return `${baseUrl}/${slug}/thank-you-${uuid}`;
};

const DirectMonetizationPage = () => {
  const { user, updateUserData } = useAuth();
  const [stripePaymentLink, setStripePaymentLink] = useState('');
  const [paypalPaymentLink, setPaypalPaymentLink] = useState('');
  const [molliePaymentLink, setMolliePaymentLink] = useState('');
  const [razorpayPaymentLink, setRazorpayPaymentLink] = useState('');
  const [paymentThankYouUrl, setPaymentThankYouUrl] = useState('');
  const [paymentCancellationUrl, setPaymentCancellationUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [copied, setCopied] = useState(false);
  const [copiedCancellation, setCopiedCancellation] = useState(false);

  // Base URL for thank you pages
  const baseUrl = window.location.origin;

  useEffect(() => {
    if (user) {
      // Set payment links from user data
      if (user.stripePaymentLink) {
        setStripePaymentLink(user.stripePaymentLink);
      }
      if (user.paypalPaymentLink) {
        setPaypalPaymentLink(user.paypalPaymentLink);
      }
      if (user.molliePaymentLink) {
        setMolliePaymentLink(user.molliePaymentLink);
      }
      if (user.razorpayPaymentLink) {
        setRazorpayPaymentLink(user.razorpayPaymentLink);
      }



      // Set thank you URL from user data or generate a new one
      if (user.paymentThankYouUrl) {
        setPaymentThankYouUrl(user.paymentThankYouUrl);
      } else if (user.slug) {
        setPaymentThankYouUrl(generateThankYouUrl(baseUrl, user.slug));
      }

      // Set cancellation URL from user data or generate a new one
      if (user.paymentCancellationUrl) {
        setPaymentCancellationUrl(user.paymentCancellationUrl);
      } else if (user.slug) {
        setPaymentCancellationUrl(`${baseUrl}/${user.slug}/cancel-payment-${crypto.randomUUID()}`);
      }
    }
  }, [user, baseUrl]);

  // Copy thank you URL to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(paymentThankYouUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  // Copy cancellation URL to clipboard
  const copyCancellationToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(paymentCancellationUrl);
      setCopiedCancellation(true);
      setTimeout(() => setCopiedCancellation(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess(false);

    try {
      const updatedUser = await updateProfile({
        stripePaymentLink,
        paypalPaymentLink,
        molliePaymentLink,
        razorpayPaymentLink,
        paymentThankYouUrl,
        paymentCancellationUrl
      });

      updateUserData(updatedUser);
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update payment settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Direct Monetization</h1>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
              <Link2 className="h-8 w-8 text-white" />
            </div>
            <div>
              <CardTitle>Payment Callback URLs</CardTitle>
              <CardDescription>
                Configure redirect URLs for successful payments and cancellations
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-2 flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                  Thank You Page (Successful payment)
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  This page will be shown to the customer after a successful payment
                </p>
                <div className="flex mt-1">
                  <Input
                    id="thankYouUrl"
                    type="url"
                    value={paymentThankYouUrl}
                    readOnly
                    disabled
                    className="flex-1 bg-muted cursor-not-allowed opacity-80"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="ml-2"
                    onClick={copyToClipboard}
                  >
                    {copied ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-2 flex items-center">
                  <span className="inline-block mr-2 text-blue-500">📋</span>
                  Paste this URL into the Success URL field in your payment provider settings
                </p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2 flex items-center">
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  Cancellation Page (Canceled payment)
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  This page will be shown to the customer if they cancel the payment
                </p>
                <div className="flex mt-1">
                  <Input
                    id="cancellationUrl"
                    type="url"
                    value={paymentCancellationUrl}
                    readOnly
                    disabled
                    className="flex-1 bg-muted cursor-not-allowed opacity-80"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="ml-2"
                    onClick={copyCancellationToClipboard}
                  >
                    {copiedCancellation ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-2 flex items-center">
                  <span className="inline-block mr-2 text-blue-500">📋</span>
                  Paste this URL into the Cancellation URL field in your payment provider settings
                </p>
              </div>

              <Separator className="my-6" />

              <Tabs defaultValue="stripe" className="w-full">
                <TabsList className="grid grid-cols-4 mb-4">
                  <TabsTrigger value="stripe" className="flex items-center gap-2">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <svg viewBox="0 0 24 24" className="w-5 h-5">
                        <path d="M13.479 9.883c-1.626-.604-2.512-.931-2.512-1.559 0-.541.445-.931 1.241-.931 1.464 0 2.958.582 3.958 1.097l.582-3.253c-.791-.396-2.374-.931-4.512-.931-1.626 0-2.977.429-3.977 1.24-1.023.813-1.55 1.977-1.55 3.372 0 2.527 1.535 3.604 4.023 4.535 1.604.582 2.152 1 2.152 1.628 0 .627-.532 1.023-1.465 1.023-1.256 0-3.34-.65-4.713-1.488l-.582 3.372c1.186.652 3.34 1.186 5.619 1.186 1.72 0 3.155-.418 4.155-1.209 1.093-.838 1.674-2.064 1.674-3.604-.023-2.575-1.581-3.674-4.093-4.628z" fill="#635BFF"/>
                      </svg>
                    </div>
                    Stripe
                  </TabsTrigger>
                  <TabsTrigger value="paypal" className="flex items-center gap-2">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <svg viewBox="0 0 24 24" className="w-5 h-5">
                        <path d="M20.067 8.478c.492.315.844.825.844 1.469 0 1.125-.933 1.966-2.086 1.966h-1.511c-.149 0-.281.096-.324.239l-.041.12-.866 5.513-.03.106c-.041.14-.175.237-.323.237h-2.246c-.133 0-.214-.096-.184-.227l1.073-6.827.034-.098c.041-.142.175-.239.323-.239h3.287c.184 0 .296-.159.296-.33 0-.172-.112-.33-.296-.33h-3.287c-.471 0-.885.33-.976.79l-1.106 7.033c-.062.267.151.52.422.52h2.245c.471 0 .886-.33.976-.79l.866-5.514c.041-.142.174-.239.323-.239h1.511c1.732 0 3.13-1.364 3.13-3.046 0-.884-.4-1.714-1.073-2.275-.225-.172-.471-.315-.74-.426-.225.583-.64 1.125-1.225 1.538z" fill="#003087"/>
                        <path d="M18.104 7.447c-.04.142-.174.24-.323.24h-1.511c-.149 0-.282.096-.323.238l-.866 5.514-.03.106c-.042.142-.175.239-.324.239h-2.245c-.133 0-.215-.096-.184-.227l.483-3.046.591-3.78c.04-.142.174-.239.323-.239h3.287c.184 0 .296-.159.296-.33 0-.172-.112-.33-.296-.33h-3.287c-.47 0-.885.33-.976.79l-.591 3.78-.483 3.046c-.062.267.151.52.422.52h2.245c.47 0 .885-.33.976-.79l.866-5.513c.041-.142.174-.239.323-.239h1.511c.184 0 .297-.159.297-.33 0-.172-.113-.33-.297-.33h-1.511c-.47 0-.885.33-.976.79" fill="#0070E0"/>
                      </svg>
                    </div>
                    PayPal
                  </TabsTrigger>
                  <TabsTrigger value="mollie" className="flex items-center gap-2">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <svg viewBox="0 0 24 24" className="w-5 h-5">
                        <path d="M9.85 15.29v1.4h2.28v-1.4zm-1.5-5.72v1.4h2.28v-1.4zm3.78 0v1.4h2.28v-1.4zm-3.78 2.86v1.4h2.28v-1.4zm3.78 0v1.4h2.28v-1.4zm3.78-2.86v1.4h2.28v-1.4zm-3.78-2.86v1.4h2.28v-1.4zm3.78 5.72v1.4h2.28v-1.4zm0 2.86v1.4h2.28v-1.4z" fill="#0075FF"/>
                      </svg>
                    </div>
                    Mollie
                  </TabsTrigger>
                  <TabsTrigger value="razorpay" className="flex items-center gap-2">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <svg viewBox="0 0 24 24" className="w-5 h-5">
                        <path d="M8.55 10.5l-2.33 2.33h4.66l2.33-2.33zm0 3.5l-2.33 2.33h4.66l2.33-2.33zm3.5-7l-2.33 2.33h4.66l2.33-2.33z" fill="#072654"/>
                        <path d="M12.05 10.5l-2.33 2.33h4.66l2.33-2.33zm0 3.5l-2.33 2.33h4.66l2.33-2.33z" fill="#3395FF"/>
                      </svg>
                    </div>
                    RazorPay
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="stripe">
                  <div className="mb-4">
                    <Label htmlFor="stripePaymentLink">Stripe Payment Link</Label>
                    <div className="flex mt-1">
                      <Input
                        id="stripePaymentLink"
                        type="url"
                        placeholder="https://buy.stripe.com/your-payment-link"
                        value={stripePaymentLink}
                        onChange={(e) => setStripePaymentLink(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Enter your Stripe Payment Link URL. You can create one in your Stripe Dashboard.
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="paypal">
                  <div className="mb-4">
                    <Label htmlFor="paypalPaymentLink">PayPal Payment Link</Label>
                    <div className="flex mt-1">
                      <Input
                        id="paypalPaymentLink"
                        type="url"
                        placeholder="https://paypal.me/your-payment-link"
                        value={paypalPaymentLink}
                        onChange={(e) => setPaypalPaymentLink(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Enter your PayPal Payment Link URL. You can create one in your PayPal Dashboard.
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="mollie">
                  <div className="mb-4">
                    <Label htmlFor="molliePaymentLink">Mollie Payment Link</Label>
                    <div className="flex mt-1">
                      <Input
                        id="molliePaymentLink"
                        type="url"
                        placeholder="https://mollie.com/your-payment-link"
                        value={molliePaymentLink}
                        onChange={(e) => setMolliePaymentLink(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Enter your Mollie Payment Link URL. You can create one in your Mollie Dashboard.
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="razorpay">
                  <div className="mb-4">
                    <Label htmlFor="razorpayPaymentLink">RazorPay Payment Link</Label>
                    <div className="flex mt-1">
                      <Input
                        id="razorpayPaymentLink"
                        type="url"
                        placeholder="https://rzp.io/your-payment-link"
                        value={razorpayPaymentLink}
                        onChange={(e) => setRazorpayPaymentLink(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Enter your RazorPay Payment Link URL. You can create one in your RazorPay Dashboard.
                    </p>
                  </div>
                </TabsContent>


              </Tabs>

              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                  {error}
                </div>
              )}

              {success && (
                <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md">
                  Payment settings updated successfully!
                </div>
              )}

              <Button type="submit" disabled={isLoading} className="w-full mt-6">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <DollarSign className="mr-2 h-4 w-4" />
                    Save Payment Settings
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>How It Works</CardTitle>
          </CardHeader>
          <CardContent>
            <h3 className="font-semibold mb-3">Payment Providers</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Create an account with your preferred payment provider if you don't have one already.</li>
              <li>Set up a Payment Link in your payment provider's dashboard.</li>
              <li>Copy the Payment Link URL and paste it in the appropriate form above.</li>
              <li>Copy the Thank You Page URL and set it as the success redirect URL in your payment provider settings.</li>
              <li>Copy the Cancellation Page URL and set it as the cancellation redirect URL in your payment provider settings.</li>
              <li>Save your changes.</li>
              <li>A "Payments" tab will appear in your dashboard where you can add payment components to your profile.</li>
            </ol>



            <div className="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md">
              <p>
                <strong>Note:</strong> You'll need to manage all payment processing, refunds, and customer support through your payment provider account.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DirectMonetizationPage;
