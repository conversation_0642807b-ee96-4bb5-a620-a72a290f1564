import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Switch } from '../components/ui/switch';
import { Label } from '../components/ui/label';
import { Input } from '../components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../components/ui/alert-dialog';
import { Copy, Share2, Users, Award } from 'lucide-react';
import { getReferralInfo, enableReferrals, disableReferrals, ReferralInfo, MaskedReferral } from '../api/referrals';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';

const ReferralsPage = () => {
  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDisclaimerDialog, setShowDisclaimerDialog] = useState(false);
  const [copySuccess, setCopySuccess] = useState('');
  const [activeTab, setActiveTab] = useState('standard');

  useEffect(() => {
    fetchReferralInfo();
  }, []);

  const fetchReferralInfo = async () => {
    try {
      setIsLoading(true);
      setError('');
      const data = await getReferralInfo();
      setReferralInfo(data);
    } catch (err: any) {
      console.error('Failed to load referral info:', err);
      setError(err.response?.data?.message || 'Failed to load referral information');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnableReferrals = async () => {
    try {
      setIsLoading(true);
      const data = await enableReferrals();
      setReferralInfo(prevInfo => prevInfo ? { ...prevInfo, ...data } : data);
      setShowDisclaimerDialog(false);
    } catch (err: any) {
      console.error('Failed to enable referrals:', err);
      setError(err.response?.data?.message || 'Failed to enable referrals');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisableReferrals = async () => {
    try {
      setIsLoading(true);
      const data = await disableReferrals();
      setReferralInfo(prevInfo => prevInfo ? { ...prevInfo, referralEnabled: data.referralEnabled } : null);
    } catch (err: any) {
      console.error('Failed to disable referrals:', err);
      setError(err.response?.data?.message || 'Failed to disable referrals');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleReferrals = (checked: boolean) => {
    if (checked) {
      setShowDisclaimerDialog(true);
    } else {
      handleDisableReferrals();
    }
  };

  const copyToClipboard = (text: string, type: 'standard' | 'vip') => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopySuccess(`${type === 'standard' ? 'Standard' : 'VIP'} referral link copied!`);
        setTimeout(() => setCopySuccess(''), 3000);
      },
      () => {
        setError('Failed to copy to clipboard');
      }
    );
  };

  const shareReferralLink = (code: string) => {
    if (navigator.share) {
      navigator.share({
        title: 'Join using my referral link',
        text: 'Sign up using my referral link!',
        url: `${window.location.origin}?ref=${code}`
      }).catch(err => {
        console.error('Error sharing:', err);
      });
    } else {
      copyToClipboard(`${window.location.origin}?ref=${code}`, 'standard');
    }
  };

  if (isLoading && !referralInfo) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Referral Program</h1>

      {error && (
        <div className="bg-red-50 text-red-700 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {copySuccess && (
        <div className="bg-green-50 text-green-700 p-4 rounded-md mb-6">
          {copySuccess}
        </div>
      )}

      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Referral Program Status</CardTitle>
              <CardDescription>Enable or disable your participation in the referral program</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="referral-enabled" className={referralInfo?.referralEnabled ? "text-green-600" : "text-muted-foreground"}>
                {referralInfo?.referralEnabled ? "Enabled" : "Disabled"}
              </Label>
              <Switch
                id="referral-enabled"
                checked={referralInfo?.referralEnabled || false}
                onCheckedChange={handleToggleReferrals}
                disabled={isLoading}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            When enabled, you can share your referral links with others. You'll earn a percentage of revenue from users who sign up using your referral link.
          </p>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Referral Program Disclaimer</h3>
            <p className="text-sm text-blue-700">
              By signing up through this referral link, you acknowledge and agree to participate in the referral program. You understand and accept that the referrer will be entitled to receive a percentage or portion of any future revenues or commissions generated by your account, for as long as your account remains active and generates earnings. This entitlement is perpetual, non-revocable, and forms a condition of joining through the referral link.
            </p>
          </div>
        </CardContent>
      </Card>

      {referralInfo?.referralEnabled && (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Your Referral Links</CardTitle>
              <CardDescription>Share these links to invite others to join</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="standard" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="standard">Standard Referral</TabsTrigger>
                  <TabsTrigger value="vip">VIP Referral</TabsTrigger>
                </TabsList>

                <TabsContent value="standard">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="standard-referral-link">Your Standard Referral Link</Label>
                      <div className="flex mt-1.5">
                        <Input
                          id="standard-referral-link"
                          value={`${window.location.origin}?ref=${referralInfo.referralCode}`}
                          readOnly
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="ml-2"
                          onClick={() => copyToClipboard(`${window.location.origin}?ref=${referralInfo.referralCode}`, 'standard')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="ml-2"
                          onClick={() => shareReferralLink(referralInfo.referralCode)}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Share this link to earn standard commission rates from referred users.
                      </p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="vip">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="vip-referral-link">Your VIP Referral Link</Label>
                      <div className="flex mt-1.5">
                        <Input
                          id="vip-referral-link"
                          value={`${window.location.origin}?ref=${referralInfo.referralVipCode}`}
                          readOnly
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="ml-2"
                          onClick={() => copyToClipboard(`${window.location.origin}?ref=${referralInfo.referralVipCode}`, 'vip')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="ml-2"
                          onClick={() => shareReferralLink(referralInfo.referralVipCode)}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex items-center mt-2">
                        <Award className="h-4 w-4 text-amber-500 mr-2" />
                        <p className="text-sm font-medium">
                          VIP Usage: {referralInfo.referralVipUsageCount} / {referralInfo.referralVipUsageLimit}
                        </p>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        VIP referrals earn higher commission rates but are limited to {referralInfo.referralVipUsageLimit} uses.
                      </p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Your Referrals</CardTitle>
                  <CardDescription>Users who signed up using your referral link</CardDescription>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-muted-foreground mr-2" />
                  <span className="text-lg font-medium">{referralInfo.referrals.length}</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {referralInfo.referrals.length === 0 ? (
                <div className="text-center p-8 bg-muted/20 rounded-lg">
                  <p className="text-muted-foreground">
                    You don't have any referrals yet. Share your referral links to start earning!
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID Prefix</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Username</TableHead>
                        <TableHead>Joined</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {referralInfo.referrals.map((referral: MaskedReferral, index: number) => (
                        <TableRow key={index}>
                          <TableCell className="font-mono">{referral.idPrefix}...</TableCell>
                          <TableCell>{referral.maskedEmail}</TableCell>
                          <TableCell>{referral.username}</TableCell>
                          <TableCell>{format(new Date(referral.createdAt), 'MMM d, yyyy')}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      <AlertDialog open={showDisclaimerDialog} onOpenChange={setShowDisclaimerDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Referral Program Disclaimer</AlertDialogTitle>
            <AlertDialogDescription className="text-left">
              <p className="mb-4">
                By signing up through this referral link, you acknowledge and agree to participate in the referral program. You understand and accept that the referrer will be entitled to receive a percentage or portion of any future revenues or commissions generated by your account, for as long as your account remains active and generates earnings. This entitlement is perpetual, non-revocable, and forms a condition of joining through the referral link.
              </p>
              <p>
                Do you agree to these terms and wish to enable the referral program?
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleEnableReferrals}>I Agree</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ReferralsPage;
