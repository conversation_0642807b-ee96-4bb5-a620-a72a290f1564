import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { acceptInvitation, rejectInvitation } from '../api/invitations';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

const InvitationPage = () => {
  const { token } = useParams<{ token: string }>();
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [error, setError] = useState('');
  const [invitationData, setInvitationData] = useState<any>(null);
  const [requiresAuth, setRequiresAuth] = useState(false);
  
  useEffect(() => {
    const checkInvitation = async () => {
      if (!token) {
        setError('Invalid invitation link');
        setIsLoading(false);
        return;
      }
      
      try {
        // Just check if the invitation is valid
        const response = await acceptInvitation(token);
        
        if (response.requiresAuth) {
          setRequiresAuth(true);
          setInvitationData(response.invitation);
        } else {
          // If already authenticated and email matches, show success
          setInvitationData(response);
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Invalid or expired invitation');
      } finally {
        setIsLoading(false);
      }
    };
    
    checkInvitation();
  }, [token]);
  
  const handleAcceptInvitation = async () => {
    if (!token) return;
    
    setIsAccepting(true);
    setError('');
    
    try {
      const response = await acceptInvitation(token);
      
      if (response.requiresAuth) {
        // Redirect to login with return URL
        const returnUrl = encodeURIComponent(`/invitations/accept/${token}`);
        navigate(`/login?returnUrl=${returnUrl}`);
      } else {
        // Show success and redirect to dashboard after a delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 3000);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to accept invitation');
    } finally {
      setIsAccepting(false);
    }
  };
  
  const handleRejectInvitation = async () => {
    if (!token) return;
    
    setIsRejecting(true);
    setError('');
    
    try {
      await rejectInvitation(token);
      
      // Redirect to login page after rejection
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to reject invitation');
    } finally {
      setIsRejecting(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Checking invitation...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container mx-auto px-4 py-16 max-w-md">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-red-600">Invitation Error</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center text-center">
              <XCircle className="h-16 w-16 text-red-500 mb-4" />
              <p className="mb-4">{error}</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate('/login')}>
              Go to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  if (requiresAuth && !isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-16 max-w-md">
        <Card>
          <CardHeader>
            <CardTitle>Collaboration Invitation</CardTitle>
            <CardDescription>
              You've been invited to collaborate on a profile
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>
                You've been invited to collaborate on <strong>{invitationData?.profileName}</strong>'s profile as a <strong>{invitationData?.role}</strong>.
              </p>
              <p>
                Please log in or create an account with <strong>{invitationData?.email}</strong> to accept this invitation.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => navigate('/login')}>
              Log In
            </Button>
            <Button onClick={() => navigate('/register')}>
              Create Account
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  // If user is authenticated but email doesn't match
  if (isAuthenticated && invitationData?.invitationEmail && user?.email !== invitationData.invitationEmail) {
    return (
      <div className="container mx-auto px-4 py-16 max-w-md">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-red-600">Email Mismatch</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center text-center">
              <XCircle className="h-16 w-16 text-red-500 mb-4" />
              <p className="mb-4">
                This invitation was sent to <strong>{invitationData.invitationEmail}</strong>, but you're logged in as <strong>{user?.email}</strong>.
              </p>
              <p>Please log out and log in with the correct account, or ask for a new invitation to be sent to your current email.</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate('/dashboard')}>
              Go to Dashboard
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-16 max-w-md">
      <Card>
        <CardHeader>
          <CardTitle>Collaboration Invitation</CardTitle>
          <CardDescription>
            Accept or reject this invitation to collaborate
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invitationData?.message ? (
            <div className="flex flex-col items-center text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
              <p className="mb-4">{invitationData.message}</p>
              <p>You'll be redirected to the dashboard shortly...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <p>
                You've been invited to collaborate on a profile as a <strong>{invitationData?.role || 'collaborator'}</strong>.
              </p>
              <p>
                Would you like to accept this invitation?
              </p>
            </div>
          )}
        </CardContent>
        {!invitationData?.message && (
          <CardFooter className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={handleRejectInvitation}
              disabled={isAccepting || isRejecting}
            >
              {isRejecting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rejecting...
                </>
              ) : (
                'Reject'
              )}
            </Button>
            <Button 
              onClick={handleAcceptInvitation}
              disabled={isAccepting || isRejecting}
            >
              {isAccepting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Accepting...
                </>
              ) : (
                'Accept'
              )}
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
};

export default InvitationPage;
