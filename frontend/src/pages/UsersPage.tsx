import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { UserPlus, Trash2, Clock, CheckCircle, XCircle } from 'lucide-react';
import {
  sendInvitation,
  getInvitations,
  cancelInvitation,
  getCollaborators,
  removeCollaborator,
  updateCollaborator<PERSON>ole,
  Invitation,
  Collaborator,
  UserRole,
  InvitationStatus
} from '../api/invitations';
import { formatDistanceToNow } from 'date-fns';

const UsersPage = () => {
  // We'll keep the useAuth hook for future use but not destructure user for now
  useAuth();
  const [activeTab, setActiveTab] = useState('collaborators');
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserRole, setNewUserRole] = useState<UserRole>(UserRole.EDITOR);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);

  // Fetch collaborators and invitations on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [collaboratorsData, invitationsData] = await Promise.all([
          getCollaborators(),
          getInvitations()
        ]);
        setCollaborators(collaboratorsData);
        setInvitations(invitationsData);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInviteUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      await sendInvitation({
        email: newUserEmail,
        role: newUserRole
      });

      // Refresh invitations list
      const invitationsData = await getInvitations();
      setInvitations(invitationsData);

      setNewUserEmail('');
      setSuccess('Invitation sent successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to invite user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      setIsLoading(true);
      await cancelInvitation(invitationId);

      // Remove the invitation from the list
      setInvitations(invitations.filter(inv => inv._id !== invitationId));

      setSuccess('Invitation cancelled successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to cancel invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveCollaborator = async (userId: string) => {
    try {
      setIsLoading(true);
      await removeCollaborator(userId);

      // Remove the collaborator from the list
      setCollaborators(collaborators.filter(collab => collab.userId._id !== userId));

      setSuccess('Collaborator removed successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to remove collaborator');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateCollaboratorRole = async (userId: string, role: UserRole) => {
    try {
      setIsLoading(true);
      const { collaborator } = await updateCollaboratorRole(userId, role);

      // Update the collaborator in the list
      setCollaborators(collaborators.map(collab =>
        collab.userId._id === userId ? collaborator : collab
      ));

      setSuccess('Collaborator role updated successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update collaborator role');
    } finally {
      setIsLoading(false);
    }
  };

  // Clear messages after 5 seconds
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess('');
        setError('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Team Management</h1>

      {(success || error) && (
        <div className={`mb-6 p-4 rounded-md ${success ? 'bg-green-50 text-green-600' : 'bg-red-50 text-red-600'}`}>
          {success || error}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="collaborators">Collaborators</TabsTrigger>
          <TabsTrigger value="invitations">Pending Invitations</TabsTrigger>
        </TabsList>

        <TabsContent value="collaborators">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Team Members</CardTitle>
                  <CardDescription>
                    Manage users who have access to your profile.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading && collaborators.length === 0 ? (
                    <div className="text-center p-8">
                      <p className="text-muted-foreground">Loading collaborators...</p>
                    </div>
                  ) : collaborators.length === 0 ? (
                    <div className="text-center p-8 bg-muted/20 rounded-lg">
                      <p className="text-muted-foreground">
                        No collaborators yet. Invite your first team member!
                      </p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Email</TableHead>
                          <TableHead>Username</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Added</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {collaborators.map((collab) => (
                          <TableRow key={collab.userId._id}>
                            <TableCell>{collab.userId.email}</TableCell>
                            <TableCell>{collab.userId.username}</TableCell>
                            <TableCell>
                              <Select
                                value={collab.role}
                                onValueChange={(value) =>
                                  handleUpdateCollaboratorRole(collab.userId._id, value as UserRole)
                                }
                                disabled={isLoading}
                              >
                                <SelectTrigger className="w-[120px]">
                                  <SelectValue placeholder="Select role" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value={UserRole.OWNER}>Owner</SelectItem>
                                  <SelectItem value={UserRole.EDITOR}>Editor</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>
                            <TableCell>
                              {formatDistanceToNow(new Date(collab.addedAt), { addSuffix: true })}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveCollaborator(collab.userId._id)}
                                disabled={isLoading}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Invite User</CardTitle>
                  <CardDescription>
                    Add a new user to your team.
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleInviteUser}>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={newUserEmail}
                        onChange={(e) => setNewUserEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        required
                        disabled={isLoading}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="role">Role</Label>
                      <Select
                        value={newUserRole}
                        onValueChange={(value) => setNewUserRole(value as UserRole)}
                        disabled={isLoading}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={UserRole.OWNER}>Owner</SelectItem>
                          <SelectItem value={UserRole.EDITOR}>Editor</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground mt-1">
                        <strong>Owners</strong> can manage all aspects of your profile, including collaborators and URL settings. <strong>Editors</strong> can only edit content and appearance.
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" disabled={isLoading} className="w-full">
                      {isLoading ? 'Sending Invitation...' : (
                        <>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Send Invitation
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="invitations">
          <Card>
            <CardHeader>
              <CardTitle>Pending Invitations</CardTitle>
              <CardDescription>
                Manage invitations you've sent to collaborators.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading && invitations.length === 0 ? (
                <div className="text-center p-8">
                  <p className="text-muted-foreground">Loading invitations...</p>
                </div>
              ) : invitations.length === 0 ? (
                <div className="text-center p-8 bg-muted/20 rounded-lg">
                  <p className="text-muted-foreground">
                    No pending invitations. Invite team members from the Collaborators tab.
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invitations.map((invitation) => (
                      <TableRow key={invitation._id}>
                        <TableCell>{invitation.email}</TableCell>
                        <TableCell>
                          <Badge variant={invitation.role === UserRole.OWNER ? 'default' : 'outline'}>
                            {invitation.role === UserRole.OWNER ? 'Owner' : 'Editor'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {invitation.status === InvitationStatus.PENDING ? (
                              <>
                                <Clock className="h-4 w-4 mr-1 text-yellow-500" />
                                <span className="text-yellow-600">Pending</span>
                              </>
                            ) : invitation.status === InvitationStatus.ACCEPTED ? (
                              <>
                                <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                                <span className="text-green-600">Accepted</span>
                              </>
                            ) : (
                              <>
                                <XCircle className="h-4 w-4 mr-1 text-red-500" />
                                <span className="text-red-600">
                                  {invitation.status === InvitationStatus.REJECTED ? 'Rejected' : 'Expired'}
                                </span>
                              </>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {invitation.status === InvitationStatus.PENDING ? (
                            formatDistanceToNow(new Date(invitation.expiresAt), { addSuffix: true })
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          {invitation.status === InvitationStatus.PENDING && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCancelInvitation(invitation._id)}
                              disabled={isLoading}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UsersPage;