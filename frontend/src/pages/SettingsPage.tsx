import VerifiedProfileSettings from '../components/VerifiedProfileSettings';
import AnalyticsSettings from '../components/AnalyticsSettings';

interface SettingsPageProps {
  activeTab?: string;
}

const SettingsPage = ({}: SettingsPageProps = {}) => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Settings</h1>

      <VerifiedProfileSettings />
      <AnalyticsSettings />
    </div>
  );
};

export default SettingsPage;
