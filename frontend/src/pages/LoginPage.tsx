import { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Checkbox } from '../components/ui/checkbox';
import { Label } from '../components/ui/label';

import AuthPreview from '../components/auth/AuthPreview';
import { validateReferralCode } from '../api/referrals';
import { getHashParam } from '../utils/urlUtils';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Check for referral code in URL parameters
  useEffect(() => {
    const checkReferralCode = async () => {
      // Extract referral code from hash part of URL using utility function
      const refCode = getHashParam('ref');

      // Check for existing debug cookie
      const existingDebugCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('referralCodeDebug='));

      if (existingDebugCookie) {
        const debugCookieValue = existingDebugCookie.split('=')[1];
        console.log('Existing referral debug cookie found (login page):', debugCookieValue);
      } else {
        console.log('No existing referral debug cookie found (login page)');
      }

      if (refCode) {
        try {
          // Validate the referral code
          console.log('Referral code found in URL (login page):', refCode);
          const response = await validateReferralCode(refCode);
          console.log('Referral validation response (login page):', response);

          // Check if debug cookie was set
          setTimeout(() => {
            const debugCookie = document.cookie
              .split('; ')
              .find(row => row.startsWith('referralCodeDebug='));

            if (debugCookie) {
              const cookieValue = debugCookie.split('=')[1];
              console.log('Referral debug cookie set (login page):', cookieValue);
            } else {
              console.log('No referral debug cookie set after validation (login page)');
            }
          }, 500); // Check after a short delay
        } catch (err: any) {
          console.error('Invalid referral code (login page):', err);
        }
      }
    };

    checkReferralCode();
  }, [location]);

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, authLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Check for referral cookie before login
    const debugCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('referralCodeDebug='));

    if (debugCookie) {
      const cookieValue = debugCookie.split('=')[1];
      console.log('Referral debug cookie found during login:', cookieValue);
    } else {
      console.log('No referral debug cookie found during login');
    }

    try {
      await login({ email, password, rememberMe });
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col-reverse lg:flex-row">
      {/* Left side - Form */}
      <div className="flex-1 flex flex-col p-8 lg:p-12 z-10 bg-white dark:bg-gray-900">
        <div className="max-w-md mx-auto w-full flex-1 flex flex-col justify-center">
          <div className="mb-8">
            <h1 className="text-4xl font-bold mb-2">Welcome back!</h1>
            <p className="text-gray-600">Log in to your Hi-there account</p>
          </div>

          <form onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 rounded-md bg-red-50 text-red-500 text-sm mb-4">
                {error}
              </div>
            )}

            <div className="mb-6">
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email or username"
                className="h-12 bg-gray-100 border-0 mb-4"
                required
              />

              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                className="h-12 bg-gray-100 border-0"
                required
              />
            </div>

            <div className="flex items-center space-x-2 mb-6">
              <Checkbox
                id="rememberMe"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked === true)}
                className="mt-0.5"
              />
              <Label htmlFor="rememberMe" className="text-sm text-gray-600 font-normal">
                Remember me
              </Label>
            </div>

            <Button
              type="submit"
              className="w-full h-12 bg-purple-600 hover:bg-purple-700 text-white rounded-full"
              disabled={isLoading}
            >
              {isLoading ? 'Logging in...' : 'Continue'}
            </Button>

            <div className="mt-6 text-center">
              <div className="relative flex items-center justify-center">
                <div className="border-t border-gray-200 absolute w-full"></div>
                <span className="bg-white px-4 text-sm text-gray-500 relative">OR</span>
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <Button
                type="button"
                variant="outline"
                className="w-full h-12 border border-gray-300 rounded-full flex items-center justify-center space-x-2"
                onClick={() => alert('Google login not implemented')}
              >
                <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                  <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z" />
                  <path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z" />
                  <path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z" />
                  <path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z" />
                </svg>
                <span>Continue with Google</span>
              </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full h-12 border border-gray-300 rounded-full flex items-center justify-center space-x-2"
                onClick={() => alert('Apple login not implemented')}
              >
                <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                  <path d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z" />
                </svg>
                <span>Continue with Apple</span>
              </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full h-12 border border-gray-300 rounded-full flex items-center justify-center space-x-2"
                onClick={() => alert('One-time code login not implemented')}
              >
                <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg>
                <span>Continue with one-time code</span>
              </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full h-12 border border-gray-300 rounded-full flex items-center justify-center space-x-2"
                onClick={() => alert('Phone number login not implemented')}
              >
                <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
                <span>Continue with phone number</span>
              </Button>
            </div>

            <div className="mt-8 text-center">
              <div className="flex justify-center space-x-4">
                <Link to="/forgot-password" className="text-sm text-purple-600 hover:underline">
                  Forgot password?
                </Link>
                <span className="text-gray-300">•</span>
                <Link to="/forgot-username" className="text-sm text-purple-600 hover:underline">
                  Forgot username?
                </Link>
              </div>

              <p className="text-sm text-gray-600 mt-6">
                Don't have an account?{' '}
                <Link to="/register" className="text-purple-600 hover:underline font-medium">
                  Sign up
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Right side - Preview */}
      <div className="w-full lg:w-1/2 h-auto min-h-screen">
        <AuthPreview type="login" />
      </div>
    </div>
  );
};

export default LoginPage;
