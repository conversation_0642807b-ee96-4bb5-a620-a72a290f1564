import ProfileForm from '../components/ProfileForm';

interface ProfileSettingsPageProps {
  activeTab?: string;
}

const ProfileSettingsPage = ({ activeTab: initialActiveTab }: ProfileSettingsPageProps = {}) => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Profiles</h1>

      <ProfileForm activeTab={initialActiveTab} />
    </div>
  );
};

export default ProfileSettingsPage;
