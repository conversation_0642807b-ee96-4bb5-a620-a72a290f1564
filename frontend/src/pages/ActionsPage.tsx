import { useState, useEffect } from 'react';
import {
  Link as LinkType,
  getLinks,
  createLink,
  updateLink,
  deleteLink,
  archiveLink,
  unarchiveLink,
  toggleLinkEnabled
} from '../api/links';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../components/ui/alert-dialog';
import AddLottieAnimationForm from '../components/AddLottieAnimationForm';
import LottieAnimationCard from '../components/LottieAnimationCard';

const ActionsPage = () => {
  const [activeLinks, setActiveLinks] = useState<LinkType[]>([]);
  const [archivedLinks, setArchivedLinks] = useState<LinkType[]>([]);
  const [regularLinks, setRegularLinks] = useState<LinkType[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('active');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [linkToDelete, setLinkToDelete] = useState<string | null>(null);

  useEffect(() => {
    const fetchLinks = async () => {
      try {
        // Fetch active links
        const activeLinksData = await getLinks(false);

        // Separate lottie animations from regular links
        const lottieLinks = activeLinksData.filter(link => link.type === 'lottie');
        const nonLottieLinks = activeLinksData.filter(link => link.type !== 'lottie');

        console.log('Lottie links:', lottieLinks);
        console.log('Regular links for selection:', nonLottieLinks);

        setActiveLinks(lottieLinks);
        setRegularLinks(nonLottieLinks);

        // Fetch archived links
        const archivedLinksData = await getLinks(true);
        setArchivedLinks(archivedLinksData.filter(link => link.type === 'lottie'));
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load links');
      }
    };

    fetchLinks();
  }, []);

  const handleAddLottieAnimation = async (
    title: string,
    lottieUrl: string,
    triggerEvent: 'pageLoad' | 'inactivity' | 'linkClick',
    triggerDelay: number,
    linkId?: string,
    lottieJsonUrl?: string
  ) => {
    try {
      // Log the data being sent to ensure it's complete
      console.log('Adding new lottie animation with data:', {
        type: 'lottie',
        title,
        lottieUrl,
        lottieJsonUrl,
        triggerEvent,
        triggerDelay,
        linkId
      });

      const newLink = await createLink({
        type: 'lottie',
        title,
        lottieUrl,
        lottieJsonUrl,
        triggerEvent,
        triggerDelay,
        linkId
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add Lottie animation');
    }
  };

  const handleEditLottieAnimation = async (
    id: string,
    title: string,
    lottieUrl: string,
    triggerEvent: 'pageLoad' | 'inactivity' | 'linkClick',
    triggerDelay: number,
    linkId?: string,
    lottieJsonUrl?: string
  ) => {
    try {
      // Log the data being sent to ensure it's complete
      console.log('Editing lottie animation with data:', {
        id,
        title,
        lottieUrl,
        lottieJsonUrl,
        triggerEvent,
        triggerDelay,
        linkId
      });

      const updatedLink = await updateLink(id, {
        title,
        lottieUrl,
        lottieJsonUrl,
        triggerEvent,
        triggerDelay,
        linkId
      });

      // Update the link in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map(link => link._id === id ? updatedLink : link));
      } else {
        setActiveLinks(activeLinks.map(link => link._id === id ? updatedLink : link));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update Lottie animation');
    }
  };

  const handleDeleteLink = async (id: string) => {
    setLinkToDelete(id);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!linkToDelete) return;

    try {
      await deleteLink(linkToDelete);
      setActiveLinks(activeLinks.filter(link => link._id !== linkToDelete));
      setArchivedLinks(archivedLinks.filter(link => link._id !== linkToDelete));
      setLinkToDelete(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete animation');
    } finally {
      setShowDeleteDialog(false);
    }
  };

  const handleArchiveLink = async (id: string) => {
    try {
      const archivedLink = await archiveLink(id);
      setActiveLinks(activeLinks.filter(link => link._id !== id));
      setArchivedLinks([...archivedLinks, archivedLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to archive animation');
    }
  };

  const handleUnarchiveLink = async (id: string) => {
    try {
      const unarchivedLink = await unarchiveLink(id);
      setArchivedLinks(archivedLinks.filter(link => link._id !== id));
      setActiveLinks([...activeLinks, unarchivedLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to unarchive animation');
    }
  };

  const handleToggleEnabled = async (id: string, enabled: boolean) => {
    try {
      const updatedLink = await toggleLinkEnabled(id, enabled);

      // Update the link in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map(link => link._id === id ? updatedLink : link));
      } else {
        setActiveLinks(activeLinks.map(link => link._id === id ? updatedLink : link));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle animation visibility');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Actions</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Add New Lottie Animation</CardTitle>
        </CardHeader>
        <CardContent>
          <AddLottieAnimationForm
            onAdd={handleAddLottieAnimation}
            activeLinks={regularLinks}
          />
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="active">Active Animations</TabsTrigger>
          <TabsTrigger value="archived">Archived Animations</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Your Lottie Animations</h2>

            {activeLinks.length === 0 ? (
              <div className="text-center p-8 bg-muted/20 rounded-lg">
                <p className="text-muted-foreground">
                  You don't have any active Lottie animations yet. Add your first animation above!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {activeLinks.map((link) => (
                  <LottieAnimationCard
                    key={link._id}
                    link={link}
                    onEdit={handleEditLottieAnimation}
                    onDelete={handleDeleteLink}
                    onArchive={handleArchiveLink}
                    onToggleEnabled={handleToggleEnabled}
                    activeLinks={regularLinks}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="archived">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Archived Lottie Animations</h2>

            {archivedLinks.length === 0 ? (
              <div className="text-center p-8 bg-muted/20 rounded-lg">
                <p className="text-muted-foreground">
                  You don't have any archived Lottie animations.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {archivedLinks.map((link) => (
                  <LottieAnimationCard
                    key={link._id}
                    link={link}
                    onEdit={handleEditLottieAnimation}
                    onDelete={handleDeleteLink}
                    onUnarchive={handleUnarchiveLink}
                    onToggleEnabled={handleToggleEnabled}
                    activeLinks={regularLinks}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this Lottie animation. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ActionsPage;
