import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import {
  Link as LinkType,
  ShuffleCard,
  MiniLeadField,
  VisibilityDates,
  getLinks,
  createLink,
  updateLink,
  deleteLink,
  archiveLink,
  unarchiveLink,
  reorderLinks,
  toggleLinkEnabled
} from '../api/links';
import { formatUrl } from '../utils/urlFormatter';
import { getStats } from '../api/stats';
import { updateProfile } from '../api/user';
import ComponentSelector from '../components/ComponentSelector';
import LinkCard from '../components/LinkCard';
import AnnouncementCard from '../components/AnnouncementCard';
import ShuffleCardsCard from '../components/ShuffleCardsCard';
import MiniLeadCard from '../components/MiniLeadCard';
import VideoCard from '../components/VideoCard';
import EmbeddedCard from '../components/EmbeddedCard';
import { ProfilePreview } from '../components/ProfilePreview';
// UI components
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Switch } from '../components/ui/switch';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Button } from '../components/ui/button';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';

interface DashboardPageProps {
  showPreview?: boolean;
}

const DashboardPage = ({ showPreview = false }: DashboardPageProps = {}) => {
  const { user, updateUserData } = useAuth();
  const [activeLinks, setActiveLinks] = useState<LinkType[]>([]);
  const [archivedLinks, setArchivedLinks] = useState<LinkType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('active');
  const [stats, setStats] = useState<{ totalClicks: number; clicksPerDay: { date: string; count: number }[] }>({
    totalClicks: 0,
    clicksPerDay: [],
  });
  // Use type assertion to access custom properties that might be added by the backend
  const [directRedirectEnabled, setDirectRedirectEnabled] = useState((user as any)?.directRedirectEnabled || false);
  const [directRedirectUrl, setDirectRedirectUrl] = useState((user as any)?.directRedirectUrl || '');
  const [isUpdatingRedirect, setIsUpdatingRedirect] = useState(false);
  const [redirectUpdateSuccess, setRedirectUpdateSuccess] = useState(false);

  const fetchLinks = async () => {
    try {
      const [activeLinksData, archivedLinksData] = await Promise.all([
        getLinks(false),
        getLinks(true)
      ]);
      setActiveLinks(activeLinksData);
      setArchivedLinks(archivedLinksData);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load links');
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const statsData = await getStats();
        await fetchLinks();
        setStats({
          totalClicks: statsData.totalClicks,
          clicksPerDay: statsData.clicksPerDay,
        });

        // Initialize direct redirect state from user data
        if (user) {
          const isEnabled = (user as any).directRedirectEnabled || false;
          setDirectRedirectEnabled(isEnabled);
          // Always set the URL from user data, regardless of whether redirect is enabled
          setDirectRedirectUrl((user as any).directRedirectUrl || '');
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user]);



  const handleAddLink = async (title: string, url: string, icon?: string) => {
    try {
      // Format the URL to ensure it has https:// if it starts with www.
      const formattedUrl = formatUrl(url);
      const newLink = await createLink({
        type: 'link',
        title,
        url: formattedUrl,
        icon
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add link');
    }
  };

  const handleAddAnnouncement = async (
    title: string,
    text: string,
    backgroundColor: string,
    textColor: string,
    url?: string,
    backgroundGradient?: {
      color1: string;
      color2: string;
      direction: string;
    },
    useGradient?: boolean,
    buttonText?: string,
    buttonBackgroundColor?: string,
    buttonTextColor?: string,
    titleAlignment?: 'left' | 'center' | 'right',
    textAlignment?: 'left' | 'center' | 'right'
  ) => {
    try {
      // Format the URL to ensure it has https:// if it starts with www.
      const formattedUrl = url ? formatUrl(url) : undefined;
      const newLink = await createLink({
        type: 'announcement',
        title,
        url: formattedUrl,
        text,
        backgroundColor,
        textColor,
        backgroundGradient,
        useGradient,
        buttonText,
        buttonBackgroundColor,
        buttonTextColor,
        titleAlignment,
        textAlignment
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add announcement');
    }
  };

  const handleAddShuffleCards = async (title: string, shuffleCards: ShuffleCard[]) => {
    try {
      const newLink = await createLink({
        type: 'shuffle',
        title,
        shuffleCards
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add shuffle cards');
    }
  };

  const handleAddMiniLead = async (title: string, introText: string, fields: MiniLeadField[]) => {
    try {
      const newLink = await createLink({
        type: 'minilead',
        title,
        introText,
        fields
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add mini lead form');
    }
  };

  const handleAddVideo = async (
    title: string,
    videoUrl: string,
    thumbnailUrl: string,
    orientation: 'portrait' | 'landscape',
    duration: number,
    videoId?: string
  ) => {
    try {
      // Determine if this is a Bunny CDN video by checking the URL
      const isBunnyCdnVideo = videoUrl && (videoUrl.includes('bunnycdn') || videoUrl.includes('.m3u8') || videoUrl.includes('vz-bde34908-dcf.b-cdn.net'));

      const newLink = await createLink({
        type: 'video',
        title,
        videoUrl,
        thumbnailUrl,
        orientation,
        duration,
        videoId, // Include videoId for Bunny CDN videos
        // Set embedType to 'stream' for Bunny CDN videos
        embedType: isBunnyCdnVideo ? 'stream' : 'youtube',
        // For Bunny CDN videos, set triggerEvent to undefined to avoid default 'onLoad'
        triggerEvent: isBunnyCdnVideo ? undefined : 'pageLoad'
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add video');
    }
  };

  const handleAddEmbedded = async (
    title: string,
    embedType: 'youtube' | 'tiktok',
    autoplay: boolean,
    muted: boolean,
    showControls: boolean,
    youtubeCode?: string,
    tikTokUrl?: string,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string,
    visibilityDates?: VisibilityDates
  ) => {
    try {
      const newLink = await createLink({
        type: 'embedded',
        title,
        embedType,
        youtubeCode,
        tikTokUrl,
        autoplay,
        muted,
        showControls,
        password,
        passwordEnabled,
        passwordExpiryDate,
        visibilityDates
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add embedded video');
    }
  };

  const handleAddPayment = async (title: string, description: string) => {
    try {
      const newLink = await createLink({
        type: 'link',
        title,
        url: description,
        icon: 'dollar-sign'
      });
      setActiveLinks([...activeLinks, newLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to add payment link');
    }
  };

  const handleEditLink = async (
    id: string,
    title: string,
    url: string,
    icon?: string,
    badge?: any,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string | null,
    visibilityDates?: any
  ) => {
    try {
      // Format the URL to ensure it has https:// if it starts with www.
      const formattedUrl = formatUrl(url);
      const updatedLink = await updateLink(id, {
        title,
        url: formattedUrl,
        icon,
        badge,
        password,
        passwordEnabled,
        passwordExpiryDate,
        visibilityDates
      });

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update link');
    }
  };

  const handleEditAnnouncement = async (
    id: string,
    title: string,
    text: string,
    backgroundColor: string,
    textColor: string,
    url?: string,
    backgroundGradient?: {
      color1: string;
      color2: string;
      direction: string;
    },
    useGradient?: boolean,
    buttonText?: string,
    buttonBackgroundColor?: string,
    buttonTextColor?: string,
    titleAlignment?: 'left' | 'center' | 'right',
    textAlignment?: 'left' | 'center' | 'right'
  ) => {
    try {
      // Format the URL to ensure it has https:// if it starts with www.
      const formattedUrl = url ? formatUrl(url) : undefined;
      const updatedLink = await updateLink(id, {
        title,
        url: formattedUrl,
        text,
        backgroundColor,
        textColor,
        backgroundGradient,
        useGradient,
        buttonText,
        buttonBackgroundColor,
        buttonTextColor,
        titleAlignment,
        textAlignment
      });

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update announcement');
    }
  };

  const handleEditShuffleCards = async (id: string, title: string, shuffleCards: ShuffleCard[]) => {
    try {
      const updatedLink = await updateLink(id, {
        title,
        shuffleCards
      });

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update shuffle cards');
    }
  };

  const handleEditMiniLead = async (id: string, title: string, introText: string, fields: MiniLeadField[]) => {
    try {
      const updatedLink = await updateLink(id, {
        title,
        introText,
        fields
      });

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update mini lead form');
    }
  };

  const handleEditVideo = async (
    id: string,
    title: string,
    videoUrl?: string,
    thumbnailUrl?: string,
    orientation?: 'portrait' | 'landscape',
    duration?: number,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string | null,
    visibilityDates?: any,
    videoId?: string,
    embedType?: 'youtube' | 'tiktok' | 'stream'
  ) => {
    try {
      // Determine if this is a Bunny CDN video by checking the URL
      const isBunnyCdnVideo = videoUrl && (videoUrl.includes('bunnycdn') || videoUrl.includes('.m3u8') || videoUrl.includes('vz-bde34908-dcf.b-cdn.net'));

      // Determine the final embedType
      const finalEmbedType = embedType || (isBunnyCdnVideo ? 'stream' : 'youtube');

      console.log('handleEditVideo parameters:', {
        id, title, videoUrl, thumbnailUrl, orientation, duration, videoId,
        providedEmbedType: embedType,
        finalEmbedType,
        isBunnyCdnVideo
      });

      const updatedLink = await updateLink(id, {
        title,
        videoUrl,
        thumbnailUrl,
        orientation,
        duration,
        password,
        passwordEnabled,
        passwordExpiryDate,
        visibilityDates,
        videoId,
        // Use the provided embedType if available, otherwise determine based on URL
        embedType: finalEmbedType,
        // For Bunny CDN videos, set triggerEvent to undefined to avoid default 'onLoad'
        triggerEvent: isBunnyCdnVideo ? undefined : 'pageLoad'
      });

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update video');
    }
  };

  const handleEditEmbedded = async (
    id: string,
    title: string,
    embedType: 'youtube' | 'tiktok',
    youtubeCode?: string,
    tikTokUrl?: string,
    autoplay?: boolean,
    muted?: boolean,
    showControls?: boolean,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string | null,
    visibilityDates?: VisibilityDates | null
  ) => {
    try {
      const updatedLink = await updateLink(id, {
        title,
        embedType,
        youtubeCode,
        tikTokUrl,
        autoplay,
        muted,
        showControls,
        password,
        passwordEnabled,
        passwordExpiryDate,
        visibilityDates
      });

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update embedded video');
    }
  };

  // Handle drag end for reordering links
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setActiveLinks((links) => {
        // Find the indices of the dragged and target items
        const oldIndex = links.findIndex((link) => link._id === active.id);
        const newIndex = links.findIndex((link) => link._id === over.id);

        // Create the reordered array
        const reordered = arrayMove(links, oldIndex, newIndex);

        // Update the order property for each link
        const updatedLinks = reordered.map((link, index) => ({
          ...link,
          order: index
        }));

        // Send the reorder request to the server
        const reorderData = {
          links: updatedLinks.map((link) => ({
            _id: link._id,
            order: link.order
          }))
        };

        reorderLinks(reorderData).catch((err) => {
          setError(err.response?.data?.message || 'Failed to reorder links');
        });

        return updatedLinks;
      });
    }
  };

  // Setup sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Sortable Link component
  const SortableLink = ({ link }: { link: LinkType }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
    } = useSortable({ id: link._id });

    const style = {
      transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
      transition,
    };

    // Render the appropriate component based on link type
    const renderComponent = () => {
      switch (link.type) {
        case 'announcement':
          return (
            <AnnouncementCard
              link={link}
              onEdit={handleEditAnnouncement}
              onDelete={handleDeleteLink}
              onArchive={handleArchiveLink}
              onToggleEnabled={handleToggleEnabled}
              isDraggable={true}
              dragHandleProps={{ ...attributes, ...listeners }}
            />
          );
        case 'shuffle':
          return (
            <ShuffleCardsCard
              link={link}
              onEdit={handleEditShuffleCards}
              onDelete={handleDeleteLink}
              onArchive={handleArchiveLink}
              onToggleEnabled={handleToggleEnabled}
              isDraggable={true}
              dragHandleProps={{ ...attributes, ...listeners }}
            />
          );
        case 'minilead':
          return (
            <MiniLeadCard
              link={link}
              onEdit={handleEditMiniLead}
              onDelete={handleDeleteLink}
              onArchive={handleArchiveLink}
              onToggleEnabled={handleToggleEnabled}
              isDraggable={true}
              dragHandleProps={{ ...attributes, ...listeners }}
            />
          );
        case 'video':
          return (
            <VideoCard
              link={link}
              onEdit={handleEditVideo}
              onDelete={handleDeleteLink}
              onArchive={handleArchiveLink}
              onToggleEnabled={handleToggleEnabled}
              isDraggable={true}
              dragHandleProps={{ ...attributes, ...listeners }}
            />
          );
        case 'embedded':
          return (
            <EmbeddedCard
              link={link}
              onEdit={handleEditEmbedded}
              onDelete={handleDeleteLink}
              onArchive={handleArchiveLink}
              onToggleEnabled={handleToggleEnabled}
              isDraggable={true}
              dragHandleProps={{ ...attributes, ...listeners }}
            />
          );
        case 'link':
        default:
          return (
            <LinkCard
              link={link}
              onEdit={handleEditLink}
              onDelete={handleDeleteLink}
              onArchive={handleArchiveLink}
              onToggleEnabled={handleToggleEnabled}
              isDraggable={true}
              dragHandleProps={{ ...attributes, ...listeners }}
            />
          );
      }
    };

    return (
      <div ref={setNodeRef} style={style}>
        {renderComponent()}
      </div>
    );
  };

  const handleDeleteLink = async (id: string) => {
    try {
      await deleteLink(id);
      setActiveLinks(activeLinks.filter((link) => link._id !== id));
      setArchivedLinks(archivedLinks.filter((link) => link._id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete link');
    }
  };

  const handleArchiveLink = async (id: string) => {
    try {
      const updatedLink = await archiveLink(id);
      setActiveLinks(activeLinks.filter((link) => link._id !== id));
      setArchivedLinks([...archivedLinks, updatedLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to archive link');
    }
  };

  const handleUnarchiveLink = async (id: string) => {
    try {
      const updatedLink = await unarchiveLink(id);
      setArchivedLinks(archivedLinks.filter((link) => link._id !== id));
      setActiveLinks([...activeLinks, updatedLink]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to unarchive link');
    }
  };

  const handleToggleEnabled = async (id: string, enabled: boolean) => {
    try {
      const updatedLink = await toggleLinkEnabled(id, enabled);

      // Update in the appropriate list
      if (updatedLink.archived) {
        setArchivedLinks(archivedLinks.map((link) => (link._id === id ? updatedLink : link)));
      } else {
        setActiveLinks(activeLinks.map((link) => (link._id === id ? updatedLink : link)));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle link enabled state');
    }
  };

  const handleUpdateDirectRedirect = async () => {
    try {
      setIsUpdatingRedirect(true);
      setError('');
      setRedirectUpdateSuccess(false);

      // If redirect is enabled, validate that a URL is provided
      if (directRedirectEnabled && (!directRedirectUrl || directRedirectUrl.trim() === '')) {
        setError('Please enter a valid URL for the redirect');
        // Reset the toggle state since we can't enable without a URL
        setDirectRedirectEnabled(false);
        return;
      }

      // Format the URL to ensure it has https:// if it starts with www.
      const formattedUrl = directRedirectUrl ? formatUrl(directRedirectUrl) : '';

      // Prepare data to update
      const dataToUpdate = {
        directRedirectEnabled,
        // When enabled, send the URL; when disabled, we still send the URL to keep it saved
        directRedirectUrl: formattedUrl
      };

      console.log('Sending direct redirect update to server:', dataToUpdate);
      const updatedUser = await updateProfile(dataToUpdate);
      console.log('Received updated user from server:', updatedUser);

      // Verify the server response has the expected values
      if (updatedUser.directRedirectEnabled !== directRedirectEnabled) {
        console.warn('Server returned different directRedirectEnabled value than requested', {
          requested: directRedirectEnabled,
          received: updatedUser.directRedirectEnabled
        });
        // Update our local state to match what the server returned
        setDirectRedirectEnabled(updatedUser.directRedirectEnabled);
      }

      // Update the user data in the context
      updateUserData(updatedUser);

      // Show success message
      setRedirectUpdateSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setRedirectUpdateSuccess(false);
      }, 3000);

      // Log confirmation for debugging
      console.log('Direct redirect settings updated:', dataToUpdate);

    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update redirect settings');
      console.error('Error updating redirect settings:', err);
      // Reset the toggle state to what it was before in case of error
      if (user) {
        setDirectRedirectEnabled((user as any).directRedirectEnabled || false);
      }
    } finally {
      setIsUpdatingRedirect(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  // If showPreview is true, only show the profile preview
  if (showPreview) {
    return (
      <div className="w-full h-full">
        <div className="w-full overflow-hidden"> {/* Full width container for background color */}
          <ProfilePreview
            username={user?.username || ''}
            bio={user?.bio || ''}
            avatarUrl={user?.avatarUrl}
            avatarPosition={user?.avatarPosition as any}
            avatarShape={user?.avatarShape as any}
            bannerUrl={user?.bannerUrl}
            bannerColor={user?.bannerColor}
            pageBackgroundColor={user?.pageBackgroundColor}
            cardBackgroundColor={user?.cardBackgroundColor}
            fontColor={user?.fontColor}
            backgroundImage={user?.backgroundImage}
            backgroundPosition={user?.backgroundPosition as any}
            backgroundRepeat={user?.backgroundRepeat as any}
            backgroundSize={user?.backgroundSize as any}
            backgroundAttachment={user?.backgroundAttachment as any}
            links={activeLinks.filter(link => link.enabled)}
            countryProfiles={user?.countryProfiles}
            showCountrySelector={false}
            showBackgroundButton={true}
            isPreview={true} // Set this to true for the preview
            onBackgroundImageChange={async (url) => {
              if (user) {
                try {
                  console.log('DashboardPage: Updating background image to:', url);

                  // Make sure we're sending a valid URL to the server
                  if (url === null) url = '';

                  // Log the current user data before update
                  console.log('DashboardPage: Current user data before update:', {
                    backgroundImage: user.backgroundImage,
                    backgroundPosition: user.backgroundPosition,
                    backgroundRepeat: user.backgroundRepeat,
                    backgroundSize: user.backgroundSize,
                    backgroundAttachment: user.backgroundAttachment
                  });

                  // Save the background image URL to the database
                  const updatedUser = await updateProfile({
                    backgroundImage: url,
                    backgroundPosition: user.backgroundPosition || 'center',
                    backgroundRepeat: user.backgroundRepeat || 'no-repeat',
                    backgroundSize: user.backgroundSize || 'cover',
                    backgroundAttachment: user.backgroundAttachment || 'scroll'
                  });

                  console.log('DashboardPage: Background image updated in database:', updatedUser.backgroundImage);
                  console.log('DashboardPage: Full updated user data:', updatedUser);

                  // Update the user data in the context to ensure it's available when navigating back
                  updateUserData(updatedUser);

                  // Force a re-render to ensure the background image is updated in the UI
                  setTimeout(() => {
                    console.log('DashboardPage: Forcing re-render after background image update');
                    // This is a trick to force a re-render
                    updateUserData({...updatedUser});
                  }, 100);
                } catch (err) {
                  console.error('DashboardPage: Error updating background image:', err);
                }
              } else {
                console.error('DashboardPage: User data is not available, cannot update background image');
              }
            }}
          onBackgroundPropertiesChange={async (properties) => {
            if (user) {
              try {
                console.log('DashboardPage: Updating background properties to:', properties);

                // Log the current user data before update
                console.log('DashboardPage: Current user data before properties update:', {
                  backgroundImage: user.backgroundImage,
                  backgroundPosition: user.backgroundPosition,
                  backgroundRepeat: user.backgroundRepeat,
                  backgroundSize: user.backgroundSize,
                  backgroundAttachment: user.backgroundAttachment
                });

                // Save the background properties to the database
                // Make sure to include the current background image URL
                const updatedUser = await updateProfile({
                  backgroundImage: user.backgroundImage || '',
                  ...properties
                });

                console.log('DashboardPage: Background properties updated in database');
                console.log('DashboardPage: Full updated user data after properties update:', updatedUser);

                // Update the user data in the context to ensure it's available when navigating back
                updateUserData(updatedUser);

                // Force a re-render to ensure the background properties are updated in the UI
                setTimeout(() => {
                  console.log('DashboardPage: Forcing re-render after background properties update');
                  // This is a trick to force a re-render
                  updateUserData({...updatedUser});
                }, 100);
              } catch (err) {
                console.error('DashboardPage: Error updating background properties:', err);
              }
            } else {
              console.error('DashboardPage: User data is not available, cannot update background properties');
            }
          }}
        />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

      {error && (
        <div className="mb-4 p-4 bg-red-50 text-red-500 rounded-md">
          {error}
        </div>
      )}

      {/* Stats Banner */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div className="mb-4 md:mb-0">
            <h2 className="text-xl font-semibold text-blue-800 mb-2">Your Stats</h2>
            <div className="text-sm text-blue-700">
              Your public link:{' '}
              <a
                href={`/#/u/${user?.slug}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 font-medium hover:underline break-all"
              >
                linktree-clone.com/#/u/{user?.slug}
              </a>
            </div>
          </div>
          <div className="flex gap-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-800">{stats.totalClicks}</div>
              <div className="text-sm text-blue-700">Total Clicks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-800">{activeLinks.length}</div>
              <div className="text-sm text-blue-700">Active Links</div>
            </div>
          </div>
        </div>

        {/* Direct Redirect Option */}
        <div className="border-t border-blue-100 pt-4">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-800">Enable direct redirect</h3>
                <p className="text-sm text-blue-700">When enabled, visitors will be immediately redirected to the URL below</p>
              </div>
              <Switch
                checked={directRedirectEnabled}
                onCheckedChange={async (checked) => {
                  // If enabling redirect, check if URL is valid
                  if (checked && (!directRedirectUrl || directRedirectUrl.trim() === '')) {
                    setError('Please enter a valid URL before enabling redirect');
                    return;
                  }

                  try {
                    // Set loading state
                    setIsUpdatingRedirect(true);

                    // Update local state first for immediate UI feedback
                    setDirectRedirectEnabled(checked);

                    // Format the URL to ensure it has https:// if it starts with www.
                    const formattedUrl = directRedirectUrl ? formatUrl(directRedirectUrl) : '';

                    // Prepare data to update
                    const dataToUpdate = {
                      directRedirectEnabled: checked,
                      directRedirectUrl: formattedUrl
                    };

                    console.log('Toggle switch: Sending update to server:', dataToUpdate);

                    // Call API directly instead of using setTimeout
                    const updatedUser = await updateProfile(dataToUpdate);

                    console.log('Toggle switch: Received response from server:', updatedUser);

                    // Update the user data in the context
                    updateUserData(updatedUser);

                    // Show success message
                    setRedirectUpdateSuccess(true);
                    setTimeout(() => setRedirectUpdateSuccess(false), 3000);

                  } catch (err: any) {
                    // In case of error, revert the toggle to its previous state
                    setDirectRedirectEnabled(!checked);
                    setError(err.response?.data?.message || 'Failed to update redirect settings');
                    console.error('Toggle switch: Error updating redirect settings:', err);
                  } finally {
                    setIsUpdatingRedirect(false);
                  }
                }}
                id="direct-redirect-switch"
                disabled={isUpdatingRedirect}
              />
            </div>

            {/* Success message */}
            {redirectUpdateSuccess && (
              <div className="p-2 bg-green-50 text-green-700 rounded-md text-sm flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Redirect settings saved successfully
              </div>
            )}

            <div className="flex flex-col space-y-2">
              <Label htmlFor="redirect-url" className="text-blue-800">Redirect URL</Label>
              <div className="flex gap-2">
                <Input
                  id="redirect-url"
                  placeholder="https://example.com"
                  value={directRedirectUrl}
                  onChange={(e) => {
                    // Store the raw input value
                    setDirectRedirectUrl(e.target.value);
                  }}
                  className="flex-1"
                  disabled={isUpdatingRedirect}
                />
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  onClick={handleUpdateDirectRedirect}
                  disabled={isUpdatingRedirect || (directRedirectEnabled && (!directRedirectUrl || directRedirectUrl.trim() === ''))}
                >
                  {isUpdatingRedirect ? 'Saving...' : 'Save'}
                </button>
              </div>
            </div>

            {!directRedirectEnabled && directRedirectUrl && (
              <div className="p-2 bg-amber-50 text-amber-700 rounded-md text-sm mt-2">
                <p>Direct redirect is currently disabled. The URL is saved but will not be used until redirect is enabled.</p>
              </div>
            )}

            {directRedirectEnabled && (!directRedirectUrl || directRedirectUrl.trim() === '') && (
              <div className="p-2 bg-red-50 text-red-700 rounded-md text-sm mt-2">
                <p>Please enter a valid URL to enable the redirect.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4"> {/* Changed from gap-6 to gap-4 to reduce spacing */}
        <div className="md:col-span-3"> {/* Increased to col-span-3 for wider edit column */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="active">Active Links</TabsTrigger>
              <TabsTrigger value="archived">Archived Links</TabsTrigger>
            </TabsList>

            <TabsContent value="active">
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Your Components</h2>
                <ComponentSelector
                  onAddLink={handleAddLink}
                  onAddAnnouncement={handleAddAnnouncement}
                  onAddShuffleCards={handleAddShuffleCards}
                  onAddMiniLead={handleAddMiniLead}
                  onAddVideo={handleAddVideo}
                  onAddEmbedded={handleAddEmbedded}
                  onAddPayment={user?.stripePaymentLink || user?.paypalPaymentLink || user?.molliePaymentLink || user?.razorpayPaymentLink ? handleAddPayment : undefined}
                />

                {activeLinks.length === 0 ? (
                  <div className="text-center p-8 bg-muted/20 rounded-lg">
                    <p className="text-muted-foreground">
                      You don't have any active components yet. Add your first component above!
                    </p>
                  </div>
                ) : (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={activeLinks.map(link => link._id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <div>
                        {activeLinks.map((link) => (
                          <SortableLink key={link._id} link={link} />
                        ))}
                      </div>
                    </SortableContext>
                  </DndContext>
                )}
              </div>
            </TabsContent>



            <TabsContent value="archived">
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Archived Links</h2>

                {archivedLinks.length === 0 ? (
                  <div className="text-center p-8 bg-muted/20 rounded-lg">
                    <p className="text-muted-foreground">
                      You don't have any archived components.
                    </p>
                  </div>
                ) : (
                  <div>
                    {archivedLinks.map((link) => {
                      switch (link.type) {
                        case 'announcement':
                          return (
                            <AnnouncementCard
                              key={link._id}
                              link={link}
                              onEdit={handleEditAnnouncement}
                              onDelete={handleDeleteLink}
                              onUnarchive={handleUnarchiveLink}
                              onToggleEnabled={handleToggleEnabled}
                            />
                          );
                        case 'shuffle':
                          return (
                            <ShuffleCardsCard
                              key={link._id}
                              link={link}
                              onEdit={handleEditShuffleCards}
                              onDelete={handleDeleteLink}
                              onUnarchive={handleUnarchiveLink}
                              onToggleEnabled={handleToggleEnabled}
                            />
                          );
                        case 'minilead':
                          return (
                            <MiniLeadCard
                              key={link._id}
                              link={link}
                              onEdit={handleEditMiniLead}
                              onDelete={handleDeleteLink}
                              onUnarchive={handleUnarchiveLink}
                              onToggleEnabled={handleToggleEnabled}
                            />
                          );
                        case 'video':
                          return (
                            <VideoCard
                              key={link._id}
                              link={link}
                              onEdit={handleEditVideo}
                              onDelete={handleDeleteLink}
                              onUnarchive={handleUnarchiveLink}
                              onToggleEnabled={handleToggleEnabled}
                            />
                          );
                        case 'embedded':
                          return (
                            <EmbeddedCard
                              key={link._id}
                              link={link}
                              onEdit={handleEditEmbedded}
                              onDelete={handleDeleteLink}
                              onUnarchive={handleUnarchiveLink}
                              onToggleEnabled={handleToggleEnabled}
                            />
                          );
                        case 'link':
                        default:
                          return (
                            <LinkCard
                              key={link._id}
                              link={link}
                              onEdit={handleEditLink}
                              onDelete={handleDeleteLink}
                              onUnarchive={handleUnarchiveLink}
                              onToggleEnabled={handleToggleEnabled}
                            />
                          );
                      }
                    })}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="md:col-span-2"> {/* Reduced to col-span-2 for narrower preview column */}
          <div className="sticky top-6">
            {/* Profile Preview */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Profile Preview</h2>
                <Button variant="outline" size="sm" className="bg-white hover:bg-white" asChild>
                  <Link to={`/u/${user?.slug}?preview=true`} target="_blank" rel="noopener noreferrer">
                    View Live Profile
                  </Link>
                </Button>
              </div>
              <div className="w-full overflow-hidden rounded-lg"> {/* Full width container for background color */}
                <ProfilePreview
                username={user?.username || ''}
                bio={user?.bio || ''}
                avatarUrl={user?.avatarUrl}
                avatarPosition={user?.avatarPosition as any}
                avatarShape={user?.avatarShape as any}
                bannerUrl={user?.bannerUrl}
                bannerColor={user?.bannerColor}
                pageBackgroundColor={user?.pageBackgroundColor}
                cardBackgroundColor={user?.cardBackgroundColor}
                fontColor={user?.fontColor}
                backgroundImage={user?.backgroundImage}
                backgroundPosition={user?.backgroundPosition as any}
                backgroundRepeat={user?.backgroundRepeat as any}
                backgroundSize={user?.backgroundSize as any}
                backgroundAttachment={user?.backgroundAttachment as any}
                links={activeLinks.filter(link => link.enabled)}
                countryProfiles={user?.countryProfiles}
                showCountrySelector={false}
                verifiedProfile={user?.verifiedProfile}
                verifiedProfileGold={user?.verifiedProfileGold}
                showBackgroundButton={false}
                isPreview={true} // Set this to true for the preview
                onBackgroundImageChange={async (imageUrl) => {
                  if (user) {
                    try {
                      console.log('DashboardPage: Updating background image to:', imageUrl);

                      // Make sure we're sending a valid URL to the server
                      if (imageUrl === null) imageUrl = '';

                      // Update the user profile with the new background image
                      await updateProfile({
                        backgroundImage: imageUrl
                      });

                      // Update the user data in the context
                      if (updateUserData && user) {
                        // Aggiorniamo solo la proprietà backgroundImage senza ricreare l'intero oggetto
                        updateUserData({
                          backgroundImage: imageUrl
                        });
                      }

                      console.log('DashboardPage: Background image updated successfully');
                    } catch (error) {
                      console.error('DashboardPage: Error updating background image:', error);
                    }
                  }
                }}
                onBackgroundPropertiesChange={async (properties) => {
                  if (user) {
                    try {
                      console.log('DashboardPage: Updating background properties to:', properties);

                      // Update the user profile with the new background properties
                      await updateProfile({
                        backgroundPosition: properties.backgroundPosition,
                        backgroundRepeat: properties.backgroundRepeat,
                        backgroundSize: properties.backgroundSize,
                        backgroundAttachment: properties.backgroundAttachment
                      });

                      // Update the user data in the context
                      if (updateUserData && user) {
                        // Aggiorniamo solo le proprietà del background senza ricreare l'intero oggetto
                        updateUserData({
                          backgroundPosition: properties.backgroundPosition,
                          backgroundRepeat: properties.backgroundRepeat,
                          backgroundSize: properties.backgroundSize,
                          backgroundAttachment: properties.backgroundAttachment
                        });
                      }

                      console.log('DashboardPage: Background properties updated successfully');
                    } catch (error) {
                      console.error('DashboardPage: Error updating background properties:', error);
                    }
                  }
                }}
              />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
