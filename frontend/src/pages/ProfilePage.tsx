import { useState, useEffect, useRef } from 'react';
import { useParams, Link, useSearchParams } from 'react-router-dom';
import { getPublicProfile } from '../api/user';
import { trackLinkClick } from '../api/links';
import { submitLead } from '../api/leads';
import { trackDirectRedirect } from '../api/stats';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { useAuth } from '../context/AuthContext';
import { useAmazonAffiliate } from '../hooks/useAmazonAffiliate';
import { ThemeIsolator } from '../components/ThemeIsolator';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { countries } from '../data/countries';
import { OptimizedImage } from '../components/OptimizedImage';
import ShuffleCardStack from '../components/ShuffleCardStack';
import { VideoPlayer } from '../components/VideoPlayer';
import { ThumbnailPlayer } from '../components/ThumbnailPlayer';
import { YouTubeEmbed } from '../components/YouTubeEmbed';
import { TikTokEmbed } from '../components/TikTokEmbed';
import LottiePopup, { preloadLottieLibrary, preloadLottieAnimation } from '../components/LottiePopup';
import ProfileHead from '../components/ProfileHead';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
import { Lock } from 'lucide-react';
import {
  FaTelegram, FaTiktok, FaFacebook, FaInstagram, FaTwitter,
  FaLinkedin, FaYoutube, FaGithub, FaDiscord, FaTwitch,
  FaReddit, FaPinterest, FaSnapchat, FaWhatsapp, FaSpotify,
  FaAmazon, FaApple, FaEtsy, FaGoogle, FaMedium, FaPatreon,
  FaPaypal, FaShopify, FaSoundcloud, FaStackOverflow, FaSteam,
  FaVimeo, FaWordpress, FaYelp, FaLink, FaStar
} from 'react-icons/fa';

// Import badge animations
import '../styles/badge-animations.css';

// Helper function to render the appropriate icon
const renderIcon = (iconName: string) => {
  const iconProps = { size: 20 };
  const getIcon = () => {
    switch (iconName) {
      case 'telegram': return <FaTelegram {...iconProps} />;
      case 'tiktok': return <FaTiktok {...iconProps} />;
      case 'facebook': return <FaFacebook {...iconProps} />;
      case 'instagram': return <FaInstagram {...iconProps} />;
      case 'twitter': return <FaTwitter {...iconProps} />;
      case 'linkedin': return <FaLinkedin {...iconProps} />;
      case 'youtube': return <FaYoutube {...iconProps} />;
      case 'github': return <FaGithub {...iconProps} />;
      case 'discord': return <FaDiscord {...iconProps} />;
      case 'twitch': return <FaTwitch {...iconProps} />;
      case 'reddit': return <FaReddit {...iconProps} />;
      case 'pinterest': return <FaPinterest {...iconProps} />;
      case 'snapchat': return <FaSnapchat {...iconProps} />;
      case 'whatsapp': return <FaWhatsapp {...iconProps} />;
      case 'spotify': return <FaSpotify {...iconProps} />;
      case 'amazon': return <FaAmazon {...iconProps} />;
      case 'apple': return <FaApple {...iconProps} />;
      case 'etsy': return <FaEtsy {...iconProps} />;
      case 'google': return <FaGoogle {...iconProps} />;
      case 'medium': return <FaMedium {...iconProps} />;
      case 'patreon': return <FaPatreon {...iconProps} />;
      case 'paypal': return <FaPaypal {...iconProps} />;
      case 'shopify': return <FaShopify {...iconProps} />;
      case 'soundcloud': return <FaSoundcloud {...iconProps} />;
      case 'stackoverflow': return <FaStackOverflow {...iconProps} />;
      case 'steam': return <FaSteam {...iconProps} />;
      case 'vimeo': return <FaVimeo {...iconProps} />;
      case 'wordpress': return <FaWordpress {...iconProps} />;
      case 'yelp': return <FaYelp {...iconProps} />;
      default: return <FaLink {...iconProps} />;
    }
  };

  // Return just the icon - the container is handled in the parent component
  return getIcon();
};

const ProfilePage = () => {
  const { slug } = useParams<{ slug: string }>();
  const { user: currentUser } = useAuth();
  const { processAmazonLink } = useAmazonAffiliate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [profile, setProfile] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<string | null>(
    searchParams.get('country') || null
  );
  const [formValues, setFormValues] = useState<Record<string, Record<string, string>>>({});
  const [submittingLeadId, setSubmittingLeadId] = useState<string | null>(null);
  const [showThankYouDialog, setShowThankYouDialog] = useState(false);
  const [submittedLeadId, setSubmittedLeadId] = useState<string | null>(null);

  // Get the base URL for the API - defined early to avoid initialization issues
  const baseUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || '';

  // Password protection state
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [passwordInput, setPasswordInput] = useState('');
  const [passwordError, setPasswordError] = useState(false);
  const [currentPasswordProtectedLinkId, setCurrentPasswordProtectedLinkId] = useState<string | null>(null);
  const [currentPasswordProtectedLinkUrl, setCurrentPasswordProtectedLinkUrl] = useState<string | null>(null);
  const passwordDialogRef = useRef<HTMLDivElement>(null);

  // Lottie animation state
  const [showLottiePopup, setShowLottiePopup] = useState(false);
  const [activeLottieAnimation, setActiveLottieAnimation] = useState<any>(null);
  const inactivityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pageLoadTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const preloadedAnimationsRef = useRef<Record<string, any>>({});

  // Check if the current user is the owner of this profile
  const isOwner = currentUser && profile?.user?.slug === currentUser.slug;

  useEffect(() => {
    const fetchProfile = async () => {
      if (!slug) return;

      setIsLoading(true);
      try {
        console.log('Fetching profile for slug:', slug);
        // Only pass selectedCountry if it's not null
        const data = await getPublicProfile(slug, selectedCountry || undefined);
        console.log('Profile data received:', data);
        setProfile(data);

        // Check if direct redirect is enabled and URL is provided
        console.log('Direct redirect settings:', {
          enabled: data.user.directRedirectEnabled,
          url: data.user.directRedirectUrl
        });

        // Check if direct redirect is enabled and URL is provided
        const shouldRedirect = data.user.directRedirectEnabled &&
                              data.user.directRedirectUrl &&
                              data.user.directRedirectUrl.trim() !== '';

        console.log('Direct redirect check:', {
          enabled: data.user.directRedirectEnabled,
          url: data.user.directRedirectUrl,
          shouldRedirect
        });

        if (shouldRedirect) {
          // Check if we're in a preview context by looking for the preview URL parameter or isPreview prop
          const isPreviewMode = window.location.href.includes('/preview') ||
                               window.location.search.includes('preview=true') ||
                               window.location.hash.includes('preview=true');

          console.log('Preview mode check:', {
            isPreviewMode,
            currentUrl: window.location.href,
            hash: window.location.hash,
            search: window.location.search
          });

          // Only redirect if we're not in preview mode
          if (!isPreviewMode) {
            // Track the redirect before navigating
            try {
              console.log('Tracking direct redirect for user:', data.user._id);

              // Use the selected country code if available
              const trackingPromise = trackDirectRedirect(data.user._id, selectedCountry || undefined);

              // Set a timeout to ensure we don't wait too long for tracking
              const timeoutPromise = new Promise<{ success: boolean }>((resolve) => {
                setTimeout(() => resolve({ success: false }), 500); // 500ms timeout
              });

              // Race the tracking promise against the timeout
              const result = await Promise.race([trackingPromise, timeoutPromise]);

              if (result.success) {
                console.log('Direct redirect tracked successfully');
              } else {
                console.log('Direct redirect tracking timed out or failed, proceeding with redirect');
              }
            } catch (error) {
              console.error('Error tracking direct redirect:', error);
              // Continue with redirect even if tracking fails
            } finally {
              // Always redirect, even if tracking fails
              // Make sure we have a valid URL before redirecting
              if (data.user.directRedirectUrl) {
                console.log('Redirecting to:', data.user.directRedirectUrl);
                window.location.href = data.user.directRedirectUrl;
              } else {
                console.error('Cannot redirect: directRedirectUrl is undefined or empty');
              }
            }
          } else {
            console.log('In preview mode - skipping redirect');
          }
        }

        // If we reach this point, it means no redirect is happening, so we should continue loading the profile

        // Preload Lottie library as soon as we have the profile data
        await preloadLottieLibrary();

        // Preload all Lottie animations in the background
        if (data.links) {
          const lottieAnimations = data.links.filter((link: any) =>
            link.type === 'lottie' && link.enabled && link.lottieUrl
          );

          if (lottieAnimations.length > 0) {
            console.log(`Preloading ${lottieAnimations.length} Lottie animations in background`);

            // Start preloading all animations in parallel
            const preloadPromises = lottieAnimations.map(async (animation: any) => {
              try {
                const preloaded = await preloadLottieAnimation(animation.lottieUrl);
                preloadedAnimationsRef.current[animation._id] = preloaded;
                console.log(`Preloaded animation: ${animation.title}`);
                return { id: animation._id, success: true };
              } catch (error) {
                console.error(`Failed to preload animation: ${animation.title}`, error);
                return { id: animation._id, success: false };
              }
            });

            // We don't await this - let it happen in the background
            Promise.all(preloadPromises).then(results => {
              console.log(`Finished preloading ${results.filter(r => r.success).length}/${lottieAnimations.length} animations`);
            });
          }
        }
      } catch (err: any) {
        console.error('Error loading profile:', err);
        setError(err.response?.data?.message || 'Failed to load profile');
      } finally {
        console.log('Setting isLoading to false');
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [slug, selectedCountry]);

  // Handle Lottie animations on page load
  useEffect(() => {
    if (!profile || !profile.links) return;

    // Clear any existing timeouts
    if (pageLoadTimeoutRef.current) {
      clearTimeout(pageLoadTimeoutRef.current);
    }

    // Find Lottie animations triggered by page load
    const pageLoadAnimations = profile.links.filter(
      (link: any) => link.type === 'lottie' && link.triggerEvent === 'pageLoad' && link.enabled
    );

    if (pageLoadAnimations.length > 0) {
      // Sort by delay to show the earliest one first
      const sortedAnimations = [...pageLoadAnimations].sort((a, b) => (a.triggerDelay || 0) - (b.triggerDelay || 0));
      const animation = sortedAnimations[0];

      // Set timeout to show the animation after the specified delay
      pageLoadTimeoutRef.current = setTimeout(() => {
        setActiveLottieAnimation(animation);
        setShowLottiePopup(true);
      }, (animation.triggerDelay || 0) * 1000);
    }

    return () => {
      if (pageLoadTimeoutRef.current) {
        clearTimeout(pageLoadTimeoutRef.current);
      }
    };
  }, [profile]);

  // Handle inactivity detection
  useEffect(() => {
    if (!profile || !profile.links) return;

    // Find Lottie animations triggered by inactivity
    const inactivityAnimations = profile.links.filter(
      (link: any) => link.type === 'lottie' && link.triggerEvent === 'inactivity' && link.enabled
    );

    if (inactivityAnimations.length === 0) return;

    // Sort by delay to show the earliest one first
    const sortedAnimations = [...inactivityAnimations].sort((a, b) => (a.triggerDelay || 0) - (b.triggerDelay || 0));
    const animation = sortedAnimations[0];
    const inactivityDelay = (animation.triggerDelay || 0) * 1000;

    // Reset inactivity timer on user activity
    const resetInactivityTimer = () => {
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
      }

      inactivityTimeoutRef.current = setTimeout(() => {
        setActiveLottieAnimation(animation);
        setShowLottiePopup(true);
      }, inactivityDelay);
    };

    // Set up event listeners for user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    activityEvents.forEach(event => {
      document.addEventListener(event, resetInactivityTimer);
    });

    // Initial setup
    resetInactivityTimer();

    // Cleanup
    return () => {
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
      }

      activityEvents.forEach(event => {
        document.removeEventListener(event, resetInactivityTimer);
      });
    };
  }, [profile]);

  // Apply background image directly to the DOM when the profile is loaded
  useEffect(() => {
    if (profile && profile.user && profile.user.backgroundImage) {
      const bgImage = profile.user.backgroundImage;
      const fullBgUrl = bgImage && !bgImage.startsWith('http') && !bgImage.startsWith('data:')
        ? `${baseUrl}${bgImage}`
        : bgImage;

      console.log('ProfilePage: Applying background image on mount:', fullBgUrl);

      // Apply the background image directly to the DOM
      setTimeout(() => {
        const container = document.getElementById('profile-page-container');
        if (container) {
          container.style.backgroundImage = fullBgUrl ? `url(${fullBgUrl})` : 'none';
          container.style.backgroundPosition = profile.user.backgroundPosition || 'center';
          container.style.backgroundRepeat = profile.user.backgroundRepeat || 'no-repeat';
          container.style.backgroundSize = profile.user.backgroundSize || 'cover';
          container.style.backgroundAttachment = profile.user.backgroundAttachment || 'scroll';
          console.log('ProfilePage: Background image applied to container on mount');
        }
      }, 100);
    }

    // No cleanup needed as the component will be unmounted
  }, [profile, baseUrl]);

  // Handle country profile changes
  useEffect(() => {
    if (profile && profile.user && profile.user.countryProfiles && selectedCountry) {
      // Find the selected country profile
      const countryProfile = profile.user.countryProfiles.find(
        (p: any) => p.countryCode === selectedCountry && p.enabled
      );

      if (countryProfile && countryProfile.backgroundImage) {
        // Apply the country-specific background image
        const bgImage = countryProfile.backgroundImage;
        const fullBgUrl = bgImage && !bgImage.startsWith('http') && !bgImage.startsWith('data:')
          ? `${baseUrl}${bgImage}`
          : bgImage;

        console.log('ProfilePage: Applying country-specific background image:', fullBgUrl);

        setTimeout(() => {
          const container = document.getElementById('profile-page-container');
          if (container) {
            container.style.backgroundImage = fullBgUrl ? `url(${fullBgUrl})` : 'none';
            container.style.backgroundPosition = countryProfile.backgroundPosition || 'center';
            container.style.backgroundRepeat = countryProfile.backgroundRepeat || 'no-repeat';
            container.style.backgroundSize = countryProfile.backgroundSize || 'cover';
            container.style.backgroundAttachment = countryProfile.backgroundAttachment || 'scroll';
            console.log('ProfilePage: Country-specific background image applied');
          }
        }, 100);
      }
    }
  }, [selectedCountry, profile, baseUrl]);

  // Update URL when country changes
  const handleCountryChange = (value: string) => {
    // If value is 'default', treat it as null (default profile)
    const countryValue = value === 'default' ? null : value;
    setSelectedCountry(countryValue);
    if (countryValue) {
      setSearchParams({ country: countryValue });
    } else {
      setSearchParams({});
    }
  };

  // Track video plays separately to avoid interfering with click tracking
  const handleVideoPlay = async (id: string) => {
    try {
      // Just track the click without any additional action
      await trackLinkClick(id);
    } catch (error) {
      console.error('Failed to track video play:', error);
    }
  };

  const handleLinkClick = async (id: string, customUrl?: string) => {
    try {
      const link = links.find((l: any) => l._id === id);

      // Special handling for video links
      if (link && link.type === 'video') {
        // Just track the click without opening a new window
        await trackLinkClick(id);
        return;
      }

      const response = await trackLinkClick(id);

      // Check if password is required
      if (response.passwordRequired) {
        setCurrentPasswordProtectedLinkId(id);
        setCurrentPasswordProtectedLinkUrl(customUrl || response.url);
        setPasswordInput('');
        setPasswordError(false);
        setShowPasswordDialog(true);
        return;
      }

      // Check if there are any Lottie animations triggered by this link click
      const linkClickAnimations = links.filter(
        (l: any) => l.type === 'lottie' &&
                   l.triggerEvent === 'linkClick' &&
                   l.linkId === id &&
                   l.enabled
      );

      if (linkClickAnimations.length > 0) {
        // Sort by delay to show the earliest one first
        const sortedAnimations = [...linkClickAnimations].sort((a, b) => (a.triggerDelay || 0) - (b.triggerDelay || 0));
        const animation = sortedAnimations[0];

        // Show the animation after the specified delay
        setTimeout(() => {
          setActiveLottieAnimation(animation);
          setShowLottiePopup(true);
        }, (animation.triggerDelay || 0) * 1000);
      }

      // Process the URL for Amazon Affiliate if needed
      const processedUrl = processAmazonLink(customUrl || response.url);

      // If no password required, proceed normally
      window.open(processedUrl, '_blank');
    } catch (error) {
      console.error('Failed to track link click:', error);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentPasswordProtectedLinkId) return;

    try {
      const response = await trackLinkClick(currentPasswordProtectedLinkId, passwordInput);

      if (response.passwordRequired && response.passwordCorrect === false) {
        // Wrong password
        setPasswordError(true);

        // Add shaking animation
        if (passwordDialogRef.current) {
          passwordDialogRef.current.classList.add('shake-animation');
          setTimeout(() => {
            if (passwordDialogRef.current) {
              passwordDialogRef.current.classList.remove('shake-animation');
            }
          }, 2000);
        }

        return;
      }

      // Password correct or not required anymore
      setShowPasswordDialog(false);
      setPasswordInput('');
      setPasswordError(false);

      // Process the URL for Amazon Affiliate if needed
      const processedUrl = processAmazonLink(currentPasswordProtectedLinkUrl || response.url);

      // Open the link
      window.open(processedUrl, '_blank');
    } catch (error) {
      console.error('Failed to verify password:', error);
      setPasswordError(true);
    }
  };

  const handleInputChange = (linkId: string, fieldName: string, value: string) => {
    setFormValues(prev => ({
      ...prev,
      [linkId]: {
        ...(prev[linkId] || {}),
        [fieldName]: value
      }
    }));
  };

  const handleLeadSubmit = async (linkId: string, fields: any[]) => {
    try {
      setSubmittingLeadId(linkId);

      // Get the form values for this link
      const formData = formValues[linkId] || {};

      // Validate required fields
      const missingRequiredFields = fields
        .filter(field => field.required)
        .filter(field => !formData[field.label || field.type]);

      if (missingRequiredFields.length > 0) {
        alert('Please fill in all required fields');
        setSubmittingLeadId(null);
        return;
      }

      // Submit the lead
      await submitLead(linkId, formData);

      // Show thank you dialog
      setSubmittedLeadId(linkId);
      setShowThankYouDialog(true);

      // Track the click
      await trackLinkClick(linkId);
    } catch (error) {
      console.error('Failed to submit lead:', error);
      alert('Failed to submit form. Please try again.');
    } finally {
      setSubmittingLeadId(null);
    }
  };

  if (isLoading) {
    console.log('Profile page is in loading state');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  if (error || !profile) {
    console.log('Profile page is in error state:', { error, profile });
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Profile Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The profile you're looking for doesn't exist or has been removed.
          </p>
          {error && (
            <p className="text-red-500 mb-4">Error: {error}</p>
          )}
          <Button asChild>
            <Link to="/">Go to Login</Link>
          </Button>
        </div>
      </div>
    );
  }

  const { user, links } = profile;

  // Ensure we have valid values for all customization options
  const effectiveBannerColor = user?.bannerColor || '#e0f2fe';
  const effectivePageBgColor = user?.pageBackgroundColor || '#f8f9fa';
  const effectiveCardBgColor = user?.cardBackgroundColor || '#ffffff';
  const effectiveFontColor = user?.fontColor || '#000000';
  const effectiveFontFamily = user?.fontFamily || '';
  const effectiveAvatarPosition = user?.avatarPosition || 'center';
  const effectiveAvatarShape = user?.avatarShape || 'circle';
  const effectiveBackgroundImage = user?.backgroundImage || '';
  const effectiveBackgroundPosition = user?.backgroundPosition || 'center';
  const effectiveBackgroundRepeat = user?.backgroundRepeat || 'no-repeat';
  const effectiveBackgroundSize = user?.backgroundSize || 'cover';
  const effectiveBackgroundAttachment = user?.backgroundAttachment || 'scroll';

  // Prepare the background image URL
  const backgroundImageUrl = effectiveBackgroundImage && !effectiveBackgroundImage.startsWith('http') && !effectiveBackgroundImage.startsWith('data:')
    ? `${baseUrl}${effectiveBackgroundImage}`
    : effectiveBackgroundImage;

  console.log('ProfilePage: backgroundImageUrl:', backgroundImageUrl);

  // Create a unique key for the container to force re-render when background changes
  const containerKey = `profile-page-${backgroundImageUrl || 'none'}-${effectiveBackgroundPosition}-${effectiveBackgroundRepeat}-${effectiveBackgroundSize}-${effectiveBackgroundAttachment}`;

  return (
    <ThemeIsolator>
      <div
        key={containerKey}
        id="profile-page-container"
        className="min-h-screen flex flex-col items-center py-8"
        style={{
          backgroundColor: effectivePageBgColor,
          backgroundImage: backgroundImageUrl ? `url(${backgroundImageUrl})` : 'none',
          backgroundPosition: effectiveBackgroundPosition,
          backgroundRepeat: effectiveBackgroundRepeat,
          backgroundSize: effectiveBackgroundSize,
          backgroundAttachment: effectiveBackgroundAttachment
        }}
    >
      {/* Add the ProfileHead component for SEO and analytics */}
      <ProfileHead user={user} baseUrl={baseUrl} />
      <div className="w-full max-w-xl px-4"> {/* Increased from max-w-md to max-w-xl (20% wider) */}
        {/* Verified Profile Badge - positioned above the card */}
        {user.verifiedProfile && (
          <div className="flex items-center gap-1 px-2 py-1 mb-2 w-fit rounded-full bg-blue-500/70 badge-popup text-white text-xs font-medium">
            <FaStar size={10} />
            <span>Verified profile</span>
          </div>
        )}

        {/* Verified Profile Gold Badge */}
        {user.verifiedProfileGold && (
          <div className="flex items-center gap-1 px-2 py-1 mb-2 w-fit rounded-full bg-amber-500/70 badge-popup text-white text-xs font-medium">
            <FaStar size={10} />
            <span>Verified profile</span>
          </div>
        )}

        <Card className="overflow-hidden" style={{ backgroundColor: effectiveCardBgColor }}>
          <div className="relative">
            {/* Banner */}
            <div
              className="h-[138px] relative"
              style={{ backgroundColor: effectiveBannerColor }}
            >
              {user.bannerUrl ? (
                <OptimizedImage
                  src={user.bannerUrl}
                  alt="Profile banner"
                  className="w-full h-full object-cover"
                  priority={true}
                  placeholder="blur"
                  blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjE5MiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZTBmMmZlIi8+PC9zdmc+"
                />
              ) : (
                <div className="w-full h-full" style={{ backgroundColor: effectiveBannerColor }}></div>
              )}

              {/* View Live Profile Button - only show for owner */}
              {isOwner && (
                <div className="absolute top-2 right-2">
                  <Button variant="outline" size="sm" className="bg-white/80 hover:bg-white" asChild>
                    <Link to="/dashboard">
                      Edit Profile
                    </Link>
                  </Button>
                </div>
              )}

              {/* Country selector */}
              {user.countryProfiles && user.countryProfiles.length > 0 && (
                <div className="absolute top-2 left-2 z-10">
                  <Select
                    value={selectedCountry || 'default'}
                    onValueChange={handleCountryChange}
                  >
                    <SelectTrigger className="w-[140px] bg-white/80 hover:bg-white text-xs h-8">
                      <SelectValue placeholder="Default Profile" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default Profile</SelectItem>
                      {user.countryProfiles
                        .filter((profile: any) => profile.enabled)
                        .map((profile: any) => {
                          const country = countries.find(c => c.code === profile.countryCode);
                          return (
                            <SelectItem key={profile.countryCode} value={profile.countryCode}>
                              {country ? country.name : profile.countryCode}
                            </SelectItem>
                          );
                        })
                      }
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* Avatar */}
            <div
              className={`absolute -bottom-10 ${
                effectiveAvatarPosition === 'left'
                  ? 'left-6'
                  : effectiveAvatarPosition === 'right'
                    ? 'right-6'
                    : 'left-0 right-0 mx-auto'
              } flex ${effectiveAvatarPosition === 'center' ? 'justify-center' : ''}`}
            >
              {user.avatarUrl ? (
                <OptimizedImage
                  src={user.avatarUrl}
                  alt={user.username}
                  className={`w-20 h-20 ${
                    effectiveAvatarShape === 'square' ? 'rounded-lg' : 'rounded-full'
                  } border-4 border-background shadow-md object-cover`}
                  priority={false}
                  placeholder="blur"
                  blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjE2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+"
                />
              ) : (
                <div
                  className={`w-20 h-20 ${
                    effectiveAvatarShape === 'square' ? 'rounded-lg' : 'rounded-full'
                  } border-4 border-background shadow-md bg-[#f0f0f0] flex items-center justify-center text-2xl font-bold text-gray-400`}
                >
                  {user.username.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
          </div>

          <CardContent
            className="pt-4 pb-4 text-center"
            style={{
              color: effectiveFontColor,
              fontFamily: effectiveFontFamily && effectiveFontFamily !== 'default' ? `'${effectiveFontFamily}', sans-serif` : undefined
            }}
          >
            <h3 className="font-bold text-lg">{user.username}</h3>
            {user.bio && (
              <div
                className="text-sm mt-1 mb-4 bio-content"
                style={{ color: effectiveFontColor }}
                dangerouslySetInnerHTML={{ __html: user.bio }}
              />
            )}

            {/* Display components */}
            {links && links.length > 0 && (
              <div className="space-y-2 mt-4">
                {links.map((link: any) => {
                  // Render different components based on type
                  switch (link.type) {
                    case 'announcement':
                      return (
                        <div
                          key={link._id}
                          className="w-full p-3 rounded-lg text-sm relative cursor-pointer hover:opacity-90 transition-colors"
                          style={
                            link.useGradient && link.backgroundGradient
                              ? {
                                  background: `linear-gradient(${link.backgroundGradient.direction}, ${link.backgroundGradient.color1}, ${link.backgroundGradient.color2})`,
                                  color: link.textColor || '#000000'
                                }
                              : {
                                  backgroundColor: link.backgroundColor || '#f3f4f6',
                                  color: link.textColor || '#000000'
                                }
                          }
                          onClick={link.url && !link.buttonText ? () => handleLinkClick(link._id) : undefined}
                        >
                          <h4
                            className="font-medium"
                            style={{ textAlign: link.titleAlignment || 'left' }}
                          >
                            {link.title}
                          </h4>
                          <div
                            className="text-xs mt-1"
                            style={{ textAlign: link.textAlignment || 'left' }}
                            dangerouslySetInnerHTML={{ __html: link.text || '' }}
                          />

                          {link.buttonText && link.url && (
                            <div className="mt-3">
                              <Button
                                size="sm"
                                className="w-full"
                                style={{
                                  backgroundColor: link.buttonBackgroundColor || '#3b82f6',
                                  color: link.buttonTextColor || '#ffffff'
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleLinkClick(link._id);
                                }}
                              >
                                {link.buttonText}
                              </Button>
                            </div>
                          )}

                          {link.url && !link.buttonText && (
                            <div className="text-xs mt-1 underline">
                              Learn more
                            </div>
                          )}
                        </div>
                      );

                    case 'shuffle':
                      return (
                        <div
                          key={link._id}
                          className="w-full rounded-lg text-sm relative bg-white/50 mb-8"
                        >
                          <h4 className="font-medium mb-2 px-2 pt-2">{link.title}</h4>
                          <ShuffleCardStack
                            cards={link.shuffleCards || []}
                            onCardClick={(url) => handleLinkClick(link._id, url)}
                          />
                        </div>
                      );

                    case 'minilead':
                      // If this lead has been submitted and thank you dialog is shown, don't render it
                      if (submittedLeadId === link._id) {
                        return null;
                      }
                      return (
                        <div
                          key={link._id}
                          className="w-full p-3 rounded-lg border border-border text-sm relative bg-white/50"
                        >
                          <h4 className="font-medium">{link.title}</h4>
                          <p className="text-xs mt-1 mb-3">{link.introText}</p>

                          <form className="space-y-2" onSubmit={(e) => e.preventDefault()}>
                            {link.fields && link.fields.map((field: any, index: number) => {
                              const fieldName = field.label || field.type;
                              return (
                                <div key={index} className="flex flex-col">
                                  <label className="text-xs font-medium mb-1">
                                    {fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}
                                    {field.required && <span className="text-red-500">*</span>}
                                  </label>
                                  <input
                                    type={field.type === 'email' ? 'email' : field.type === 'phone' ? 'tel' : 'text'}
                                    className="w-full p-1.5 text-xs border rounded-md bg-background"
                                    placeholder={`Enter your ${field.type}`}
                                    required={field.required}
                                    value={(formValues[link._id] && formValues[link._id][fieldName]) || ''}
                                    onChange={(e) => handleInputChange(link._id, fieldName, e.target.value)}
                                    disabled={submittingLeadId === link._id}
                                  />
                                </div>
                              );
                            })}
                            <button
                              className="w-full mt-2 p-1.5 bg-primary text-primary-foreground rounded-md text-xs font-medium"
                              onClick={() => handleLeadSubmit(link._id, link.fields)}
                              disabled={submittingLeadId === link._id}
                            >
                              {submittingLeadId === link._id ? 'Submitting...' : 'Submit'}
                            </button>
                          </form>
                        </div>
                      );

                    case 'video':
                      return (
                        <div
                          key={link._id}
                          className="w-full rounded-lg text-sm relative bg-white/50 mb-4"
                        >
                          <h4 className="font-medium mb-2 px-2 pt-2">{link.title}</h4>
                          {link.thumbnailUrl && (
                            <div className="relative cursor-pointer">
                              {/* Use VideoPlayer only if we have both video and thumbnail */}
                              {link.videoUrl ? (
                                <VideoPlayer
                                  videoUrl={link.videoUrl}
                                  thumbnailUrl={link.thumbnailUrl}
                                  orientation={link.orientation || 'landscape'}
                                  autoPlay={false}
                                  controls={true}
                                  // When video is played, track it
                                  onClick={() => handleVideoPlay(link._id)}
                                />
                              ) : (
                                <ThumbnailPlayer
                                  thumbnailUrl={link.thumbnailUrl}
                                  videoUrl={link.videoUrl}
                                  onClick={() => console.log('Thumbnail clicked, but no video URL available')}
                                />
                              )}
                            </div>
                          )}
                          {link.badge && (
                            <div
                              className={`absolute -top-2 -right-1 px-2 py-0.5 rounded-full text-xs font-medium ${link.badge.animation ? `badge-${link.badge.animation}` : ''}`}
                              style={{
                                backgroundColor: link.badge.backgroundColor || 'rgba(59, 130, 246, 0.2)',
                                color: link.badge.textColor || '#3b82f6'
                              }}
                            >
                              {link.badge.text}
                            </div>
                          )}
                          {link.passwordEnabled && (
                            <div className="absolute -top-2 left-2 px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 flex items-center gap-1">
                              <Lock size={10} />
                              <span className="text-[10px]">Protected</span>
                            </div>
                          )}
                        </div>
                      );

                    case 'embedded':
                      return (
                        <div
                          key={link._id}
                          className="w-full rounded-lg text-sm relative bg-white/50 mb-4"
                        >
                          <h4 className="font-medium mb-2 px-2 pt-2">{link.title}</h4>

                          {/* YouTube embed */}
                          {(link.embedType === 'youtube' || !link.embedType) && link.youtubeCode && (
                            <div className="relative">
                              <YouTubeEmbed
                                youtubeCode={link.youtubeCode}
                                title={link.title}
                                autoplay={link.autoplay || false}
                                muted={link.muted !== false} // Default to true if undefined
                                showControls={link.showControls !== false} // Default to true if undefined
                                onClick={() => handleVideoPlay(link._id)}
                              />
                            </div>
                          )}

                          {/* TikTok embed */}
                          {link.embedType === 'tiktok' && link.tikTokUrl && (
                            <div className="relative">
                              <TikTokEmbed
                                tikTokUrl={link.tikTokUrl}
                                title={link.title}
                                onClick={() => handleVideoPlay(link._id)}
                              />
                            </div>
                          )}

                          {link.badge && (
                            <div
                              className={`absolute -top-2 -right-1 px-2 py-0.5 rounded-full text-xs font-medium ${link.badge.animation ? `badge-${link.badge.animation}` : ''}`}
                              style={{
                                backgroundColor: link.badge.backgroundColor || 'rgba(59, 130, 246, 0.2)',
                                color: link.badge.textColor || '#3b82f6'
                              }}
                            >
                              {link.badge.text}
                            </div>
                          )}
                          {link.passwordEnabled && (
                            <div className="absolute -top-2 left-2 px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 flex items-center gap-1">
                              <Lock size={10} />
                              <span className="text-[10px]">Protected</span>
                            </div>
                          )}
                        </div>
                      );

                    case 'link':
                    default:
                      return (
                        <button
                          key={link._id}
                          onClick={() => handleLinkClick(link._id)}
                          className="w-full p-2 rounded-lg border border-border flex items-center gap-2 text-sm relative hover:opacity-90 transition-colors"
                          style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}
                        >
                          {link.badge && (
                            <div
                              className={`absolute -top-2 -right-1 px-2 py-0.5 rounded-full text-xs font-medium ${link.badge.animation ? `badge-${link.badge.animation}` : ''}`}
                              style={{
                                backgroundColor: link.badge.backgroundColor || 'rgba(59, 130, 246, 0.2)',
                                color: link.badge.textColor || '#3b82f6'
                              }}
                            >
                              {link.badge.text}
                            </div>
                          )}
                          {link.passwordEnabled && (
                            <div className="absolute -top-2 left-2 px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 flex items-center gap-1">
                              <Lock size={10} />
                              <span className="text-[10px]">Protected</span>
                            </div>
                          )}

                          {/* Amazon affiliate indicator removed from live page */}
                          <div className="text-primary flex items-center justify-center w-5 h-5">
                            {renderIcon(link.icon || 'link')}
                          </div>
                          <span className="flex-1 text-left truncate" style={{ color: effectiveFontColor }}>
                            {link.title}
                          </span>
                        </button>
                      );
                  }
                })}
              </div>
            )}
            {links.length === 0 && (
              <div className="text-center p-4 mt-4">
                <p className="text-muted-foreground">No links available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="mt-6 text-center text-sm text-muted-foreground">
          <p>
            Powered by{' '}
            <span className="text-primary">
              Hi-there
            </span>
          </p>
        </div>
      </div>

      {/* Thank You Dialog */}
      <AlertDialog open={showThankYouDialog} onOpenChange={setShowThankYouDialog}>
        <AlertDialogContent className="thankyou-dialog-popup">
          <AlertDialogHeader>
            <AlertDialogTitle>Thank You!</AlertDialogTitle>
            <AlertDialogDescription>
              Your information has been submitted successfully.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => {
              setShowThankYouDialog(false);
              // Keep the submitted lead ID to keep the form hidden
            }}>
              Close
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Password Dialog */}
      <AlertDialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <AlertDialogContent
          ref={passwordDialogRef}
          className={`${passwordError ? 'password-error' : ''} fixed-center-dialog`}
        >
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Lock size={16} className="text-amber-600" />
              Password Protected Link
            </AlertDialogTitle>
            <AlertDialogDescription>
              This link is password protected. Please enter the password to continue.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <form onSubmit={handlePasswordSubmit} className="py-4">
            <Input
              type="password"
              placeholder="Enter password"
              value={passwordInput}
              onChange={(e) => setPasswordInput(e.target.value)}
              className={`w-full ${passwordError ? 'border-red-500' : ''}`}
              autoFocus
            />
            {passwordError && (
              <p className="text-red-500 text-sm mt-2">Incorrect password. Please try again.</p>
            )}
          </form>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setShowPasswordDialog(false);
              setPasswordInput('');
              setPasswordError(false);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handlePasswordSubmit}>
              Submit
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Lottie Animation Popup */}
      {showLottiePopup && activeLottieAnimation && (
        <LottiePopup
          link={activeLottieAnimation}
          preloadedAnimation={preloadedAnimationsRef.current[activeLottieAnimation._id]}
          onClose={() => {
            setShowLottiePopup(false);
            setActiveLottieAnimation(null);
          }}
        />
      )}
    </div>
    </ThemeIsolator>
  );
};

export default ProfilePage;
