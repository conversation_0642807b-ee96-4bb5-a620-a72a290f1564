import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { XCircle } from 'lucide-react';
import { getPublicProfile } from '../api/user';

const CancellationPage = () => {
  const { slug } = useParams<{ slug: string; token: string }>();
  const [username, setUsername] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!slug) return;

      try {
        setIsLoading(true);
        const profileData = await getPublicProfile(slug);
        setUsername(profileData.user.username || '');
      } catch (err) {
        setError('Could not load user profile');
        console.error('Error fetching profile:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [slug]);

  if (isLoading) {
    return (
      <div className="container mx-auto py-16 px-4 flex justify-center">
        <div className="w-full max-w-md">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center p-8">
                <div className="h-12 w-12 rounded-full border-4 border-t-transparent border-red-500 animate-spin"></div>
                <p className="mt-4 text-muted-foreground">Loading...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-16 px-4 flex justify-center">
        <div className="w-full max-w-md">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <p className="text-red-500 mb-4">Something went wrong</p>
                <Button asChild>
                  <Link to="/">Return to Login</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-16 px-4 flex justify-center">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
            <CardDescription>
              Your payment process has been cancelled.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center space-y-6 py-4">
              <p className="text-center">
                You've cancelled your payment to {username}. No charges have been made to your account.
              </p>

              <Button asChild className="w-full">
                <Link to={`/u/${slug}`}>Return to Profile</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CancellationPage;
