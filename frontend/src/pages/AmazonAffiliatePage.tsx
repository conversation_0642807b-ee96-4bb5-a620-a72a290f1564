import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { updateProfile } from '../api/user';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Loader2 } from 'lucide-react';
import { Switch } from '../components/ui/switch';
import { FaAmazon } from 'react-icons/fa';

const AmazonAffiliatePage = () => {
  const { user, updateUserData } = useAuth();
  const [amazonAffiliateId, setAmazonAffiliateId] = useState('');
  const [amazonAffiliateAutoApply, setAmazonAffiliateAutoApply] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (user) {
      // Set Amazon Affiliate settings
      if (user.amazonAffiliateId) {
        setAmazonAffiliateId(user.amazonAffiliateId);
      }
      if (user.amazonAffiliateAutoApply !== undefined) {
        setAmazonAffiliateAutoApply(user.amazonAffiliateAutoApply);
      }
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess(false);

    try {
      const updatedUser = await updateProfile({
        amazonAffiliateId,
        amazonAffiliateAutoApply
      });

      updateUserData(updatedUser);
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update Amazon Affiliate settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Amazon Affiliate</h1>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-yellow-500 flex items-center justify-center flex-shrink-0">
              <FaAmazon className="h-8 w-8 text-white" />
            </div>
            <div>
              <CardTitle>Amazon Affiliate Settings</CardTitle>
              <CardDescription>
                Configure your Amazon Affiliate tracking ID and settings
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="amazonAffiliateId">Amazon Affiliate Tracking ID</Label>
                  <div className="flex mt-1">
                    <Input
                      id="amazonAffiliateId"
                      type="text"
                      placeholder="yourtag-20"
                      value={amazonAffiliateId}
                      onChange={(e) => setAmazonAffiliateId(e.target.value)}
                      className="flex-1"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Enter your Amazon Affiliate tracking ID (e.g., yourtag-20). You can find this in your Amazon Associates account.
                  </p>
                </div>

                <div className="flex items-center space-x-2 mt-4">
                  <Switch
                    id="amazonAffiliateAutoApply"
                    checked={amazonAffiliateAutoApply}
                    onCheckedChange={setAmazonAffiliateAutoApply}
                  />
                  <Label htmlFor="amazonAffiliateAutoApply" className="cursor-pointer">
                    Automatically apply tracking ID to Amazon links
                  </Label>
                </div>

                <div className="bg-amber-50 p-4 rounded-md mt-4">
                  <h4 className="font-medium text-amber-800 mb-2">How it works</h4>
                  <p className="text-sm text-amber-700">
                    When enabled, your Amazon Affiliate tracking ID will be automatically added to any Amazon links in your components.
                    This allows you to earn commissions from qualifying purchases made through these links.
                  </p>
                  <p className="text-sm text-amber-700 mt-2">
                    A small Amazon badge will be displayed on components containing Amazon affiliate links.
                  </p>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md">
                    Amazon Affiliate settings updated successfully!
                  </div>
                )}

                <Button type="submit" disabled={isLoading} className="w-full mt-6">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <FaAmazon className="mr-2 h-4 w-4" />
                      Save Amazon Affiliate Settings
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>How It Works</CardTitle>
          </CardHeader>
          <CardContent>
            <h3 className="font-semibold mb-3">Amazon Affiliate Program</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Sign up for the <a href="https://affiliate-program.amazon.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Amazon Associates Program</a> if you haven't already.</li>
              <li>Enter your Amazon Affiliate tracking ID in the form above.</li>
              <li>Enable the automatic tracking option to have your ID added to all Amazon links in your components.</li>
              <li>Components with Amazon affiliate links will display a small Amazon badge.</li>
            </ol>

            <div className="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md">
              <p>
                <strong>Note:</strong> Make sure to follow Amazon's terms of service regarding affiliate links. You are responsible for ensuring your use of affiliate links complies with Amazon's policies.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AmazonAffiliatePage;
