import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Button } from '../components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { <PERSON><PERSON>ircle2, BarChart2, FileBarChart, Users, BadgeCheck, LineChart, BrainCircuit } from 'lucide-react';

// Import first image eagerly for immediate display
import beautyInfluencers from '../assets/beauty-influencers.webp';

// Placeholder for images that will be lazy loaded
const imagePlaceholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+';

const HomePage = () => {
  // Lazy load the other images
  const [communityManager, setCommunityManager] = useState(imagePlaceholder);
  const [twitchStreamer, setTwitchStreamer] = useState(imagePlaceholder);

  useEffect(() => {
    // Load second image after a delay
    const timer1 = setTimeout(() => {
      import('../assets/community-manager.webp').then(module => {
        setCommunityManager(module.default);
      });
    }, 1000);

    // Load third image after a longer delay
    const timer2 = setTimeout(() => {
      import('../assets/twitch-streamer.webp').then(module => {
        setTwitchStreamer(module.default);
      });
    }, 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-accent/20 to-secondary/30">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-12 md:py-20">
        <div className="grid md:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
          <div className="text-left">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 font-heading bg-clip-text text-transparent bg-gradient-to-r from-primary via-accent to-primary/80">
              One Link for Your Entire Digital World
            </h1>
            <p className="text-xl mb-8 text-muted-foreground font-light">
              Share all your content, social profiles, and links with a single, customizable page. Perfect for creators, influencers, and businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="rounded-full text-lg px-8 py-6 font-medium shadow-lg hover:shadow-xl transition-all bg-primary hover:bg-primary/90" asChild>
                <Link to="/register">Get Started Free</Link>
              </Button>
              <Button size="lg" variant="outline" className="rounded-full text-lg px-8 py-6 font-medium border-2 hover:bg-secondary/50 transition-all" asChild>
                <Link to="/login">Log In</Link>
              </Button>
            </div>
            <div className="mt-8 flex items-center text-sm text-muted-foreground">
              <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
              <span>No credit card required • Free forever plan available</span>
            </div>
          </div>
          <div className="relative">
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-accent/30 rounded-full filter blur-xl opacity-70"></div>
            <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-primary/30 rounded-full filter blur-xl opacity-70"></div>
            <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-secondary-foreground/20 rounded-full filter blur-lg opacity-60"></div>

            {/* Image Carousel with Animation Effects */}
            <div className="relative h-[400px] rounded-2xl shadow-2xl overflow-hidden">
              {/* First Image - Beauty Influencers */}
              <img
                src={beautyInfluencers}
                alt="Beauty Influencers using LinkTree Clone"
                className="absolute inset-0 w-full h-full object-cover animate-fade-in"
                loading="eager" // Load first image eagerly as it's visible immediately
                fetchPriority="high"
              />

              {/* Second Image - Community Manager */}
              <img
                src={communityManager}
                alt="Community Manager using LinkTree Clone"
                className="absolute inset-0 w-full h-full object-cover animate-slide-in-right"
                style={{ animationDelay: '4s', opacity: 0 }}
                loading="lazy"
                decoding="async"
              />

              {/* Third Image - Twitch Streamer */}
              <img
                src={twitchStreamer}
                alt="Twitch Streamer using LinkTree Clone"
                className="absolute inset-0 w-full h-full object-cover animate-slide-in-left"
                style={{ animationDelay: '8s', opacity: 0 }}
                loading="lazy"
                decoding="async"
              />
            </div>
          </div>
        </div>

        {/* Premium Features Section */}
        <div className="max-w-6xl mx-auto mt-24">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold font-heading">Premium Features for Everyone</h2>
            <p className="text-xl text-muted-foreground mt-4 max-w-2xl mx-auto">
              Everything you need to grow your audience and track your success, all in one place.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]"></TableHead>
                    <TableHead>Feature</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Availability</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                        <BarChart2 className="w-6 h-6 text-primary" />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">Free Analytics</TableCell>
                    <TableCell>Track clicks, views, and engagement metrics with our powerful analytics dashboard.</TableCell>
                    <TableCell className="text-right">All Plans</TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell>
                      <div className="w-12 h-12 bg-accent/10 rounded-xl flex items-center justify-center">
                        <FileBarChart className="w-6 h-6 text-accent" />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">Free Reports</TableCell>
                    <TableCell>Generate detailed performance reports to analyze your link engagement and track growth over time.</TableCell>
                    <TableCell className="text-right">All Plans</TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell>
                      <div className="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center">
                        <Users className="w-6 h-6 text-green-600" />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">Unlimited Users</TableCell>
                    <TableCell>Enterprise plans include unlimited user accounts with customizable permissions and roles.</TableCell>
                    <TableCell className="text-right">Enterprise</TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell>
                      <div className="w-12 h-12 bg-amber-50 rounded-xl flex items-center justify-center">
                        <BadgeCheck className="w-6 h-6 text-amber-600" />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">Verified Icon</TableCell>
                    <TableCell>Stand out with a verified badge on your profile to build trust and credibility with your audience.</TableCell>
                    <TableCell className="text-right">Pro & Enterprise</TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell>
                      <div className="w-12 h-12 bg-red-50 rounded-xl flex items-center justify-center">
                        <LineChart className="w-6 h-6 text-red-600" />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">Google Analytics</TableCell>
                    <TableCell>Integrate your Google Analytics account for even deeper insights into your audience behavior.</TableCell>
                    <TableCell className="text-right">Pro & Enterprise</TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell>
                      <div className="w-12 h-12 bg-indigo-50 rounded-xl flex items-center justify-center">
                        <BrainCircuit className="w-6 h-6 text-indigo-600" />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">AI Performance</TableCell>
                    <TableCell>Our AI-powered recommendations help optimize your link placement and content for maximum engagement.</TableCell>
                    <TableCell className="text-right">Enterprise</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </div>



        {/* Creator Showcase Section */}
        <div className="mt-24 max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 font-heading">Perfect for Every Creator</h2>

          <div className="grid md:grid-cols-3 gap-12">
            {/* Card 1 - Musicians */}
            <div className="bg-gradient-to-br from-purple-100 to-indigo-50 p-6 rounded-2xl overflow-hidden shadow-md">
              <div className="h-48 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl mb-6 flex items-center justify-center text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-center">Musicians & Artists</h3>
              <p className="text-muted-foreground text-center">
                Share your latest releases, tour dates, merchandise, and streaming platforms all in one place.
              </p>
            </div>

            {/* Card 2 - Content Creators */}
            <div className="bg-gradient-to-br from-pink-100 to-rose-50 p-6 rounded-2xl overflow-hidden shadow-md">
              <div className="h-48 bg-gradient-to-r from-pink-500 to-rose-500 rounded-xl mb-6 flex items-center justify-center text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-center">Content Creators</h3>
              <p className="text-muted-foreground text-center">
                Connect your YouTube, TikTok, Instagram, and other platforms to grow your audience across all channels.
              </p>
            </div>

            {/* Card 3 - Businesses */}
            <div className="bg-gradient-to-br from-cyan-100 to-sky-50 p-6 rounded-2xl overflow-hidden shadow-md">
              <div className="h-48 bg-gradient-to-r from-cyan-500 to-sky-500 rounded-xl mb-6 flex items-center justify-center text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-center">Small Businesses</h3>
              <p className="text-muted-foreground text-center">
                Showcase your products, services, contact information, and social media profiles in a professional layout.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-24 mb-12 max-w-4xl mx-auto text-center bg-gradient-to-r from-primary/20 via-accent/20 to-primary/10 p-12 rounded-3xl border border-primary/10 shadow-lg">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 font-heading bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">Ready to Boost Your Online Presence?</h2>
          <p className="text-xl mb-8 text-muted-foreground max-w-2xl mx-auto">
            Join thousands of creators, influencers, and businesses who have increased their engagement with our platform.
          </p>
          <Button size="lg" className="rounded-full text-lg px-10 py-6 font-medium shadow-lg hover:shadow-xl transition-all bg-primary hover:bg-primary/90" asChild>
            <Link to="/register">Create Your Free Account</Link>
          </Button>
          <p className="mt-6 text-sm text-muted-foreground">
            No credit card required. Upgrade anytime.
          </p>
        </div>

        {/* Footer Section */}
        <footer className="mt-24 py-12 border-t border-gray-200">
          <div className="max-w-6xl mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-lg font-bold mb-4">Hi-there</h3>
                <p className="text-muted-foreground text-sm">
                  Share all your content with a single, customizable page. Perfect for creators, influencers, and businesses.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-bold mb-4">Company</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/about" className="text-muted-foreground hover:text-primary text-sm">
                      About Us
                    </Link>
                  </li>
                  <li>
                    <Link to="/careers" className="text-muted-foreground hover:text-primary text-sm">
                      Careers
                    </Link>
                  </li>
                  <li>
                    <Link to="/contact" className="text-muted-foreground hover:text-primary text-sm">
                      Contact
                    </Link>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-bold mb-4">Resources</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/blog" className="text-muted-foreground hover:text-primary text-sm">
                      Blog
                    </Link>
                  </li>
                  <li>
                    <Link to="/help" className="text-muted-foreground hover:text-primary text-sm">
                      Help Center
                    </Link>
                  </li>
                  <li>
                    <a href="https://status.hi-there.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary text-sm">
                      Server Status
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-bold mb-4">Legal</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/terms" className="text-muted-foreground hover:text-primary text-sm">
                      Terms & Conditions
                    </Link>
                  </li>
                  <li>
                    <Link to="/privacy" className="text-muted-foreground hover:text-primary text-sm">
                      Privacy Policy
                    </Link>
                  </li>
                  <li>
                    <Link to="/cookies" className="text-muted-foreground hover:text-primary text-sm">
                      Cookie Policy
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-12 pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm text-muted-foreground">
                © {new Date().getFullYear()} Hi-there. All rights reserved.
              </p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                  </svg>
                </a>
                <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                  </svg>
                </a>
                <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default HomePage;
