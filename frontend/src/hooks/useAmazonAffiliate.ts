import { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { addAmazonAffiliateId, isAmazonProductLink } from '../utils/amazonAffiliateUtils';

/**
 * Hook to handle Amazon Affiliate link processing
 * @returns Object with processAmazonLink function
 */
export const useAmazonAffiliate = () => {
  const { user } = useAuth();
  const [amazonAffiliateId, setAmazonAffiliateId] = useState<string>('');
  const [autoApplyEnabled, setAutoApplyEnabled] = useState<boolean>(false);

  useEffect(() => {
    if (user) {
      setAmazonAffiliateId(user.amazonAffiliateId || '');
      setAutoApplyEnabled(user.amazonAffiliateAutoApply || false);
    }
  }, [user]);

  /**
   * Process a URL to add Amazon Affiliate ID if needed
   * @param url The URL to process
   * @returns The processed URL with Amazon Affiliate ID if applicable
   */
  const processAmazonLink = (url: string): string => {
    if (!url || !amazonAffiliateId || !autoApplyEnabled) {
      return url;
    }

    if (isAmazonProductLink(url)) {
      return addAmazonAffiliateId(url, amazonAffiliateId);
    }

    return url;
  };

  return {
    processAmazonLink,
    amazonAffiliateId,
    autoApplyEnabled
  };
};
