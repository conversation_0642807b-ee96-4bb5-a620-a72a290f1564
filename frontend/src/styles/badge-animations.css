/* Badge Animations */

@keyframes popup {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  70% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes vibrate {
  0%, 100% {
    transform: translateY(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateY(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateY(2px);
  }
}

/* Thank you popup animation */
@keyframes thankyou-popup {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  70% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.badge-popup {
  animation: popup 0.5s ease-out forwards;
}

.badge-shake {
  animation: shake 0.5s ease-in-out infinite;
}

.badge-vibrate {
  animation: vibrate 0.5s ease-in-out infinite;
}

/* For preview */
.preview-popup {
  animation: popup 1s ease-out forwards;
}

.preview-shake {
  animation: shake 1s ease-in-out infinite;
}

.preview-vibrate {
  animation: vibrate 1s ease-in-out infinite;
}

/* Thank you dialog animation */
.thankyou-dialog-popup {
  animation: thankyou-popup 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards !important;
}

/* Password error animation */
@keyframes shake-animation {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

.shake-animation {
  animation: shake-animation 0.5s ease-in-out;
}

.password-error {
  border-color: rgb(239, 68, 68) !important;
}
