/**
 * Utility functions for formatting URLs consistently across the application
 */

/**
 * Formats a URL to ensure it's properly handled:
 * - If it's an absolute URL (starts with http/https), returns it as is
 * - If it's a data URL (starts with data:), returns it as is
 * - Otherwise, prepends the API base URL
 *
 * @param url The URL to format
 * @returns The formatted URL
 */
export const formatAssetUrl = (url: string): string => {
  if (!url) return '';

  // If it's already an absolute URL or a data URL, return it as is
  if (url.startsWith('http') || url.startsWith('data:')) {
    return url;
  }

  // Otherwise, prepend the API base URL
  // Make sure we have a valid API URL, fallback to localhost if not available
  const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';
  const baseUrl = apiUrl.replace('/api', '');

  // Log for debugging
  console.log('formatAssetUrl: baseUrl =', baseUrl, 'for url =', url);

  return `${baseUrl}${url}`;
};

/**
 * Formats a URL to ensure it has a proper protocol:
 * - If it already starts with http:// or https://, returns it as is
 * - If it starts with www., prepends https://
 * - Otherwise, returns the URL as is (assuming it's a relative URL or other format)
 *
 * @param url The URL to format
 * @returns The formatted URL with proper protocol
 */
export const formatUrl = (url: string): string => {
  if (!url) return '';

  // If it already has a protocol, return it as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // If it starts with www., prepend https://
  if (url.startsWith('www.')) {
    return `https://${url}`;
  }

  // Return the URL as is for other cases
  return url;
};
