/**
 * Utility functions for generating simple banner backgrounds
 */

/**
 * Generate a random pastel color
 * @returns A hex color code for a pastel color
 */
export const generateRandomPastelColor = (): string => {
  // Generate random RGB values in the pastel range (180-240)
  const r = Math.floor(Math.random() * 60) + 180;
  const g = Math.floor(Math.random() * 60) + 180;
  const b = Math.floor(Math.random() * 60) + 180;
  
  // Convert to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

/**
 * Generate a simple gradient SVG with the specified color
 * @param color The main color for the gradient
 * @returns An SVG string with a simple gradient
 */
export const generateSimpleGradient = (color: string): string => {
  // Create a slightly lighter version of the color for the gradient
  const r = parseInt(color.slice(1, 3), 16);
  const g = parseInt(color.slice(3, 5), 16);
  const b = parseInt(color.slice(5, 7), 16);
  
  // Make it lighter (add 20 to each component, max 255)
  const lighterR = Math.min(r + 20, 255);
  const lighterG = Math.min(g + 20, 255);
  const lighterB = Math.min(b + 20, 255);
  
  // Convert back to hex
  const lighterColor = `#${lighterR.toString(16).padStart(2, '0')}${lighterG.toString(16).padStart(2, '0')}${lighterB.toString(16).padStart(2, '0')}`;
  
  return `
<svg width="800" height="200" viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="${color}" />
      <stop offset="100%" stop-color="${lighterColor}" />
    </linearGradient>
  </defs>
  <rect width="800" height="200" fill="url(#gradient)" />
</svg>
  `.trim();
};

/**
 * Generate a data URL for a simple banner with the specified color
 * @param color The main color for the banner
 * @returns A data URL for the SVG
 */
export const generateSimpleBannerDataUrl = (color: string): string => {
  const svg = generateSimpleGradient(color);
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Generate a random simple banner data URL
 * @returns A data URL for a random simple banner
 */
export const generateRandomBannerDataUrl = (): string => {
  const randomColor = generateRandomPastelColor();
  return generateSimpleBannerDataUrl(randomColor);
};
