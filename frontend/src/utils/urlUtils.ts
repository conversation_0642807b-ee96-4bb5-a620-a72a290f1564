/**
 * Utility functions for handling URL parameters in a HashRouter environment
 */

/**
 * Extracts URL parameters from the hash part of the URL
 * Works with both formats:
 * - localhost:5173/#/path?param=value
 * - localhost:5173/#/path/param=value
 * 
 * @param paramName The name of the parameter to extract
 * @returns The parameter value or null if not found
 */
export const getHashParam = (paramName: string): string | null => {
  // First check if there's a search part in the hash (after ?)
  const hashParts = window.location.hash.split('?');
  if (hashParts.length > 1) {
    const params = new URLSearchParams(hashParts[1]);
    const value = params.get(paramName);
    if (value) return value;
  }
  
  // If not found, check if it's directly in the hash path
  const hashPath = window.location.hash;
  const paramMatch = hashPath.match(new RegExp(`${paramName}=([^&#]+)`));
  if (paramMatch && paramMatch[1]) {
    return paramMatch[1];
  }
  
  return null;
};
