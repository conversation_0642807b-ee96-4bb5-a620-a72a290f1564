/**
 * Utility functions for generating avatar URLs using DiceBear API
 */

/**
 * Available DiceBear avatar styles
 * See https://www.dicebear.com/styles/ for all available styles
 */
export type DiceBearStyle = 
  | 'adventurer'
  | 'adventurer-neutral'
  | 'avataaars'
  | 'avataaars-neutral'
  | 'big-ears'
  | 'big-ears-neutral'
  | 'big-smile'
  | 'bottts'
  | 'bottts-neutral'
  | 'croodles'
  | 'croodles-neutral'
  | 'fun-emoji'
  | 'icons'
  | 'identicon'
  | 'initials'
  | 'lorelei'
  | 'lorelei-neutral'
  | 'micah'
  | 'miniavs'
  | 'notionists'
  | 'open-peeps'
  | 'personas'
  | 'pixel-art'
  | 'pixel-art-neutral'
  | 'shapes'
  | 'thumbs';

/**
 * Default avatar style to use
 */
export const DEFAULT_AVATAR_STYLE: DiceBearStyle = 'avataaars';

/**
 * Generates a DiceBear avatar URL based on a username or seed
 * @param seed The seed to use for generating the avatar (usually username)
 * @param style The DiceBear style to use
 * @param options Additional options to pass to the DiceBear API
 * @returns The URL for the avatar
 */
export const generateAvatarUrl = (
  seed: string,
  style: DiceBearStyle = DEFAULT_AVATAR_STYLE,
  options: Record<string, string | number | boolean | string[]> = {}
): string => {
  // Base URL for DiceBear API
  const baseUrl = `https://api.dicebear.com/9.x/${style}/svg`;
  
  // Convert options to query parameters
  const queryParams = new URLSearchParams();
  
  // Add seed
  queryParams.append('seed', seed);
  
  // Add other options
  Object.entries(options).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      queryParams.append(key, value.join(','));
    } else {
      queryParams.append(key, String(value));
    }
  });
  
  // Return the full URL
  return `${baseUrl}?${queryParams.toString()}`;
};

/**
 * Generates a default avatar URL for a user
 * @param username The username to use as seed
 * @returns The URL for the default avatar
 */
export const getDefaultAvatarUrl = (username: string): string => {
  // Default options for avatars
  const options = {
    backgroundColor: 'b6e3f4',
  };
  
  return generateAvatarUrl(username, DEFAULT_AVATAR_STYLE, options);
};
