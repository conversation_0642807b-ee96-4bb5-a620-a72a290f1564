/**
 * Utility functions for handling Amazon Affiliate links
 */

/**
 * Checks if a URL is an Amazon product link
 * @param url The URL to check
 * @returns True if the URL is an Amazon product link
 */
export const isAmazonProductLink = (url: string): boolean => {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    // Check if the domain is amazon (any TLD)
    return urlObj.hostname.includes('amazon.');
  } catch (error) {
    // If URL parsing fails, it's not a valid URL
    return false;
  }
};

/**
 * Adds an Amazon Affiliate tracking ID to an Amazon product URL if it doesn't already have one
 * @param url The Amazon product URL
 * @param affiliateId The Amazon Affiliate tracking ID
 * @returns The URL with the affiliate tracking ID added
 */
export const addAmazonAffiliateId = (url: string, affiliateId: string): string => {
  if (!url || !affiliateId || !isAmazonProductLink(url)) {
    return url;
  }
  
  try {
    const urlObj = new URL(url);
    
    // Check if the URL already has a tag parameter
    if (urlObj.searchParams.has('tag')) {
      return url; // Don't modify if it already has a tag
    }
    
    // Add the affiliate ID as the 'tag' parameter
    urlObj.searchParams.set('tag', affiliateId);
    
    return urlObj.toString();
  } catch (error) {
    // If URL parsing fails, return the original URL
    return url;
  }
};

/**
 * Checks if a URL has an Amazon Affiliate tracking ID
 * @param url The URL to check
 * @returns True if the URL has an Amazon Affiliate tracking ID
 */
export const hasAmazonAffiliateId = (url: string): boolean => {
  if (!url || !isAmazonProductLink(url)) {
    return false;
  }
  
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.has('tag');
  } catch (error) {
    return false;
  }
};
