/**
 * Utility functions for working with Google Fonts
 */

// Google Fonts API key - this should ideally be stored in an environment variable
// For this implementation, we're using a public API key with limited usage
const GOOGLE_FONTS_API_KEY = 'AIzaSyAOES8EmKhuJEnsn9kS1XKBpxxp-TgN8Jc';

// Google Fonts API URL
const GOOGLE_FONTS_API_URL = 'https://www.googleapis.com/webfonts/v1/webfonts';

// Interface for Google Font item
export interface GoogleFont {
  family: string;
  variants: string[];
  subsets: string[];
  category: string;
}

// Cache for Google Fonts to avoid repeated API calls
let googleFontsCache: GoogleFont[] = [];

/**
 * Fetches the list of available Google Fonts
 * @returns Promise with the list of Google Fonts
 */
export const fetchGoogleFonts = async (): Promise<GoogleFont[]> => {
  // Return cached fonts if available
  if (googleFontsCache.length > 0) {
    return googleFontsCache;
  }

  try {
    // Fetch fonts from Google Fonts API
    const response = await fetch(
      `${GOOGLE_FONTS_API_URL}?key=${GOOGLE_FONTS_API_KEY}&sort=popularity`
    );

    if (!response.ok) {
      throw new Error('Failed to fetch Google Fonts');
    }

    const data = await response.json();
    googleFontsCache = data.items || [];
    return googleFontsCache;
  } catch (error) {
    console.error('Error fetching Google Fonts:', error);
    return [];
  }
};

/**
 * Searches for Google Fonts by name
 * @param query Search query
 * @param fonts List of Google Fonts to search in
 * @returns Filtered list of Google Fonts
 */
export const searchGoogleFonts = (query: string, fonts: GoogleFont[]): GoogleFont[] => {
  if (!query) return fonts;

  const lowerCaseQuery = query.toLowerCase();
  return fonts.filter(font =>
    font.family.toLowerCase().includes(lowerCaseQuery)
  );
};

/**
 * Loads a Google Font by adding a link element to the document head
 * @param fontFamily Font family name
 * @returns Cleanup function to remove the font
 */
export const loadGoogleFont = (fontFamily: string): (() => void) => {
  if (!fontFamily || fontFamily === 'default') {
    return () => {}; // No-op cleanup function
  }

  // Create a link element for the Google Font
  const fontLink = document.createElement('link');
  fontLink.rel = 'stylesheet';
  fontLink.href = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(/\s+/g, '+')}:wght@300;400;500;600;700&display=swap`;
  document.head.appendChild(fontLink);

  // Return cleanup function
  return () => {
    if (fontLink.parentNode) {
      document.head.removeChild(fontLink);
    }
  };
};

// List of popular Google Fonts (same as in the current implementation)
export const POPULAR_FONTS = [
  'Roboto',
  'Open Sans',
  'Lato',
  'Montserrat',
  'Poppins',
  'Raleway',
  'Nunito',
  'Ubuntu',
  'Playfair Display',
  'Merriweather',
  'Oswald',
  'Quicksand',
  'Rubik',
  'Work Sans',
  'Mulish',
  'Inter',
  'Source Sans Pro',
  'Noto Sans',
  'Outfit',
  'Dancing Script',
  'Pacifico',
  'Caveat',
  'Satisfy',
  'Lobster',
  'Comfortaa',
];
