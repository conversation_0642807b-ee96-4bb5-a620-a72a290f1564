/**
 * Utility functions for generating default banner images
 */

/**
 * Available banner pattern types
 */
export type BannerPatternType = 
  | 'waves'
  | 'blobs'
  | 'triangles'
  | 'circles'
  | 'gradient';

/**
 * Default banner pattern type
 */
export const DEFAULT_BANNER_PATTERN: BannerPatternType = 'waves';

/**
 * Generate a color based on a seed string
 * @param seed The seed string to generate the color from
 * @returns A hex color code
 */
export const generateColorFromSeed = (seed: string): string => {
  // Simple hash function to generate a number from a string
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    hash = seed.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Convert the hash to a hex color
  let color = '#';
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xFF;
    color += ('00' + value.toString(16)).substr(-2);
  }
  
  return color;
};

/**
 * Generate a pastel color based on a seed string
 * @param seed The seed string to generate the color from
 * @returns A hex color code for a pastel color
 */
export const generatePastelColorFromSeed = (seed: string): string => {
  // Generate a base color
  const baseColor = generateColorFromSeed(seed);
  
  // Convert hex to RGB
  const r = parseInt(baseColor.slice(1, 3), 16);
  const g = parseInt(baseColor.slice(3, 5), 16);
  const b = parseInt(baseColor.slice(5, 7), 16);
  
  // Make it pastel by mixing with white
  const pastelR = Math.floor((r + 255) / 2);
  const pastelG = Math.floor((g + 255) / 2);
  const pastelB = Math.floor((b + 255) / 2);
  
  // Convert back to hex
  return `#${pastelR.toString(16).padStart(2, '0')}${pastelG.toString(16).padStart(2, '0')}${pastelB.toString(16).padStart(2, '0')}`;
};

/**
 * Generate a waves pattern SVG
 * @param seed The seed to use for generating the pattern
 * @returns An SVG string
 */
export const generateWavesPattern = (seed: string): string => {
  const color1 = generatePastelColorFromSeed(seed);
  const color2 = generatePastelColorFromSeed(seed + 'alt');
  
  return `
<svg width="800" height="200" viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="${color1}" />
      <stop offset="100%" stop-color="${color2}" />
    </linearGradient>
  </defs>
  <rect width="800" height="200" fill="url(#gradient)" />
  <path d="M0 80 C 200 120, 400 40, 600 80 S 800 120, 1000 80 V 200 H 0 Z" fill="${color1}" opacity="0.3" />
  <path d="M0 100 C 200 140, 400 60, 600 100 S 800 140, 1000 100 V 200 H 0 Z" fill="${color2}" opacity="0.3" />
</svg>
  `.trim();
};

/**
 * Generate a blobs pattern SVG
 * @param seed The seed to use for generating the pattern
 * @returns An SVG string
 */
export const generateBlobsPattern = (seed: string): string => {
  const color1 = generatePastelColorFromSeed(seed);
  const color2 = generatePastelColorFromSeed(seed + 'alt');
  
  return `
<svg width="800" height="200" viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="${color1}" />
      <stop offset="100%" stop-color="${color2}" />
    </linearGradient>
  </defs>
  <rect width="800" height="200" fill="url(#gradient)" />
  <circle cx="100" cy="100" r="80" fill="${color1}" opacity="0.3" />
  <circle cx="700" cy="50" r="60" fill="${color2}" opacity="0.3" />
  <ellipse cx="400" cy="150" rx="200" ry="50" fill="${color1}" opacity="0.3" />
</svg>
  `.trim();
};

/**
 * Generate a triangles pattern SVG
 * @param seed The seed to use for generating the pattern
 * @returns An SVG string
 */
export const generateTrianglesPattern = (seed: string): string => {
  const color1 = generatePastelColorFromSeed(seed);
  const color2 = generatePastelColorFromSeed(seed + 'alt');
  
  return `
<svg width="800" height="200" viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="${color1}" />
      <stop offset="100%" stop-color="${color2}" />
    </linearGradient>
  </defs>
  <rect width="800" height="200" fill="url(#gradient)" />
  <polygon points="0,0 200,0 100,100" fill="${color1}" opacity="0.3" />
  <polygon points="600,0 800,0 700,100" fill="${color2}" opacity="0.3" />
  <polygon points="300,50 500,50 400,150" fill="${color1}" opacity="0.3" />
</svg>
  `.trim();
};

/**
 * Generate a circles pattern SVG
 * @param seed The seed to use for generating the pattern
 * @returns An SVG string
 */
export const generateCirclesPattern = (seed: string): string => {
  const color1 = generatePastelColorFromSeed(seed);
  const color2 = generatePastelColorFromSeed(seed + 'alt');
  
  return `
<svg width="800" height="200" viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="${color1}" />
      <stop offset="100%" stop-color="${color2}" />
    </linearGradient>
  </defs>
  <rect width="800" height="200" fill="url(#gradient)" />
  <circle cx="100" cy="50" r="30" fill="${color1}" opacity="0.3" />
  <circle cx="200" cy="100" r="20" fill="${color2}" opacity="0.3" />
  <circle cx="300" cy="50" r="25" fill="${color1}" opacity="0.3" />
  <circle cx="400" cy="100" r="35" fill="${color2}" opacity="0.3" />
  <circle cx="500" cy="50" r="15" fill="${color1}" opacity="0.3" />
  <circle cx="600" cy="100" r="30" fill="${color2}" opacity="0.3" />
  <circle cx="700" cy="50" r="25" fill="${color1}" opacity="0.3" />
</svg>
  `.trim();
};

/**
 * Generate a gradient SVG
 * @param seed The seed to use for generating the gradient
 * @returns An SVG string
 */
export const generateGradientSVG = (seed: string): string => {
  const color1 = generatePastelColorFromSeed(seed);
  const color2 = generatePastelColorFromSeed(seed + 'alt');
  
  return `
<svg width="800" height="200" viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="${color1}" />
      <stop offset="100%" stop-color="${color2}" />
    </linearGradient>
  </defs>
  <rect width="800" height="200" fill="url(#gradient)" />
</svg>
  `.trim();
};

/**
 * Generate a banner SVG based on a pattern type and seed
 * @param seed The seed to use for generating the pattern
 * @param patternType The type of pattern to generate
 * @returns An SVG string
 */
export const generateBannerSVG = (
  seed: string,
  patternType: BannerPatternType = DEFAULT_BANNER_PATTERN
): string => {
  switch (patternType) {
    case 'waves':
      return generateWavesPattern(seed);
    case 'blobs':
      return generateBlobsPattern(seed);
    case 'triangles':
      return generateTrianglesPattern(seed);
    case 'circles':
      return generateCirclesPattern(seed);
    case 'gradient':
      return generateGradientSVG(seed);
    default:
      return generateWavesPattern(seed);
  }
};

/**
 * Generate a data URL for a banner SVG
 * @param seed The seed to use for generating the pattern
 * @param patternType The type of pattern to generate
 * @returns A data URL for the SVG
 */
export const generateBannerDataUrl = (
  seed: string,
  patternType: BannerPatternType = DEFAULT_BANNER_PATTERN
): string => {
  const svg = generateBannerSVG(seed, patternType);
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Get a default banner URL for a user
 * @param username The username to use as seed
 * @returns A data URL for the default banner
 */
export const getDefaultBannerUrl = (username: string): string => {
  return generateBannerDataUrl(username);
};
