/**
 * Utility functions for processing images
 */

/**
 * Converts an image to WebP format with specified quality
 * @param file Original image file
 * @param quality WebP quality (0-1)
 * @returns Promise with the WebP blob
 */
export const convertToWebP = async (
  file: File,
  quality = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        ctx.drawImage(img, 0, 0);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Could not convert image to WebP'));
            }
          },
          'image/webp',
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = event.target?.result as string;
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};

/**
 * Centers an image based on focal point and optimizes it for display
 * @param file Image file
 * @param focalPoint Focal point coordinates (0-1 for both x and y)
 * @param targetWidth Target width
 * @param targetHeight Target height
 * @returns Promise with the centered, cropped, and optimized image blob
 */
export const centerImageOnFocalPoint = async (
  file: File,
  focalPoint: { x: number; y: number },
  targetWidth: number,
  targetHeight: number
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        // Apply image smoothing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // Calculate source and destination rectangles
        const sourceAspect = img.width / img.height;
        const targetAspect = targetWidth / targetHeight;

        let sourceX = 0;
        let sourceY = 0;
        let sourceWidth = img.width;
        let sourceHeight = img.height;

        // Determine crop dimensions based on aspect ratios
        if (sourceAspect > targetAspect) {
          // Source is wider than target
          sourceWidth = img.height * targetAspect;
          // Center on focal point X
          sourceX = Math.max(0, Math.min(img.width - sourceWidth, img.width * focalPoint.x - sourceWidth / 2));
        } else {
          // Source is taller than target
          sourceHeight = img.width / targetAspect;
          // Center on focal point Y
          sourceY = Math.max(0, Math.min(img.height - sourceHeight, img.height * focalPoint.y - sourceHeight / 2));
        }

        // Draw the cropped image
        ctx.drawImage(
          img,
          sourceX, sourceY, sourceWidth, sourceHeight,
          0, 0, targetWidth, targetHeight
        );

        // Determine appropriate quality based on image type
        // Use higher quality for avatar (which is smaller) and lower quality for banner (which is larger)
        const isAvatar = targetWidth === targetHeight;
        // Reduce banner quality to improve LCP
        const quality = isAvatar ? 0.85 : 0.65;

        canvas.toBlob(
          (blob) => {
            if (blob) {
              // Check if the blob size is reasonable (under 1MB)
              if (blob.size <= 1024 * 1024) {
                resolve(blob);
              } else {
                // If the image is still too large, create a more compressed version
                const secondCanvas = document.createElement('canvas');
                secondCanvas.width = targetWidth;
                secondCanvas.height = targetHeight;
                const secondCtx = secondCanvas.getContext('2d');

                if (!secondCtx) {
                  // If we can't create a second canvas, just return the original blob
                  resolve(blob);
                  return;
                }

                // Create a temporary image from the first blob
                const tempImg = new Image();
                tempImg.onload = () => {
                  // Draw the image on the second canvas
                  secondCtx.drawImage(tempImg, 0, 0, targetWidth, targetHeight);

                  // Create a more compressed blob
                  secondCanvas.toBlob(
                    (compressedBlob) => {
                      if (compressedBlob) {
                        resolve(compressedBlob);
                      } else {
                        // If compression fails, return the original blob
                        resolve(blob);
                      }
                    },
                    'image/webp',
                    isAvatar ? 0.75 : 0.5 // Even more compression for the second attempt
                  );
                };

                tempImg.src = URL.createObjectURL(blob);
              }
            } else {
              reject(new Error('Could not create cropped image'));
            }
          },
          'image/webp',
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = event.target?.result as string;
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};

/**
 * Generates a random code for user media directory
 * @returns Random alphanumeric string
 */
export const generateRandomCode = (): string => {
  return Math.random().toString(36).substring(2, 10);
};

/**
 * Creates a sanitized directory name for user media
 * @param username User's username
 * @returns Sanitized directory name with random code
 */
export const createUserMediaDir = (username: string): string => {
  // Sanitize username (remove special chars, lowercase)
  const sanitized = username.toLowerCase().replace(/[^a-z0-9]/g, '');
  const randomCode = generateRandomCode();
  return `${sanitized}_${randomCode}`;
};
