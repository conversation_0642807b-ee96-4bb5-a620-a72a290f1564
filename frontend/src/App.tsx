import { HashRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ThemeProvider } from './context/ThemeContext';
import Navbar from './components/Navbar';
import DashboardLayout from './components/DashboardLayout';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import SettingsPage from './pages/SettingsPage';
import StatsPage from './pages/StatsPage';
import ProfilePage from './pages/ProfilePage';
import ProfileSettingsPage from './pages/ProfileSettingsPage';
import UsersPage from './pages/UsersPage';
import LeadsPage from './pages/LeadsPage';
import ReferralsPage from './pages/ReferralsPage';
import DirectMonetizationPage from './pages/DirectMonetizationPage';
import AmazonAffiliatePage from './pages/AmazonAffiliatePage';
import ActionsPage from './pages/ActionsPage';
import MarketplacePage from './pages/MarketplacePage';
import ThankYouPage from './pages/ThankYouPage';
import CancellationPage from './pages/CancellationPage';
import InvitationPage from './pages/InvitationPage';
import NotFoundPage from './pages/NotFoundPage';

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return <>{children}</>;
};

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <DashboardPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/actions"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <ActionsPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <SettingsPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/stats"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <StatsPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <ProfileSettingsPage activeTab="profile" />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/preview"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <DashboardPage showPreview={true} />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/direct-monetization"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <DirectMonetizationPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/marketplace"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <MarketplacePage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/amazon-affiliate"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <AmazonAffiliatePage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/users"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <UsersPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/leads"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <LeadsPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/referrals"
        element={
          <ProtectedRoute>
            <DashboardLayout>
              <ReferralsPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route path="/u/:slug" element={<ProfilePage />} />
      <Route path="/:slug/thank-you-:token" element={<ThankYouPage />} />
      <Route path="/:slug/cancel-payment-:token" element={<CancellationPage />} />
      <Route path="/invitations/accept/:token" element={<InvitationPage />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

// Component to conditionally render the Navbar
const AppLayout = () => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  // Check if we're on a profile page (/u/:slug), thank you page, cancellation page, or invitation page
  const isProfilePage = location.pathname.startsWith('/u/');
  const isThankYouPage = /^\/[^/]+\/thank-you-[0-9a-f-]+$/.test(location.pathname);
  const isCancellationPage = /^\/[^/]+\/cancel-payment-[0-9a-f-]+$/.test(location.pathname);
  const isInvitationPage = location.pathname.startsWith('/invitations/accept/');

  // Only hide navbar for non-authenticated users on profile pages, thank you pages, cancellation pages, and invitation pages
  const showNavbar = (!isProfilePage && !isThankYouPage && !isCancellationPage && !isInvitationPage) || isAuthenticated;

  return (
    <div className="min-h-screen bg-background text-foreground">
      {showNavbar && <Navbar />}
      <main>
        <AppRoutes />
      </main>
    </div>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <AppLayout />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App
