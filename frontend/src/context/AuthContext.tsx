import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthResponse, LoginData, RegisterData, login as apiLogin, register as apiRegister, getProfile } from '../api/auth';
import { User } from '../api/user';
import { jwtDecode } from 'jwt-decode';
import api from '../api/axios';

// Define JWT token interface
interface JwtPayload {
  id: string;
  exp: number;
  iat: number;
}

interface AuthContextType {
  user: AuthResponse | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateUserData: (userData: Partial<User>) => void;
  checkTokenValidity: (token: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Function to check if a token is valid and not expired
  const checkTokenValidity = (token: string): boolean => {
    try {
      const decodedToken = jwtDecode<JwtPayload>(token);
      const currentTime = Date.now() / 1000;

      // Check if token is expired
      if (decodedToken.exp < currentTime) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  };

  useEffect(() => {
    const checkAuth = async () => {
      // Check both localStorage and sessionStorage for a token
      const localToken = localStorage.getItem('token');
      const sessionToken = sessionStorage.getItem('token');
      const token = localToken || sessionToken;

      if (token) {
        // Validate token before making API call
        if (checkTokenValidity(token)) {
          try {
            const userData = await getProfile();

            // Ensure default values are set for profile customization
            const userWithDefaults = {
              ...userData,
              token,
              bannerColor: userData.bannerColor || '#e0f2fe',
              pageBackgroundColor: userData.pageBackgroundColor || '#f8f9fa',
              cardBackgroundColor: userData.cardBackgroundColor || '#ffffff',
              fontColor: userData.fontColor || '#000000',
              fontFamily: userData.fontFamily || '',
              avatarPosition: userData.avatarPosition || 'center',
              avatarShape: userData.avatarShape || 'circle',
              backgroundImage: userData.backgroundImage || '',
              backgroundPosition: userData.backgroundPosition || 'center',
              backgroundRepeat: userData.backgroundRepeat || 'no-repeat',
              backgroundSize: userData.backgroundSize || 'cover',
              backgroundAttachment: userData.backgroundAttachment || 'scroll'
            };

            // Log the background image for debugging
            console.log('AuthContext: User loaded with backgroundImage:', userData.backgroundImage);

            setUser(userWithDefaults);
          } catch (error) {
            console.error('Authentication error:', error);
            // Clear token from both storages
            localStorage.removeItem('token');
            sessionStorage.removeItem('token');
          }
        } else {
          // Token is invalid or expired, clear it
          console.log('Token expired or invalid, logging out');
          localStorage.removeItem('token');
          sessionStorage.removeItem('token');
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (data: LoginData) => {
    try {
      const response = await apiLogin(data);

      // Validate token before storing
      if (checkTokenValidity(response.token)) {
        // If rememberMe is true, store in localStorage (persists after browser close)
        // Otherwise, use sessionStorage (cleared when browser is closed)
        const storage = data.rememberMe ? localStorage : sessionStorage;
        storage.setItem('token', response.token);

        // Ensure default values are set for profile customization
        const userWithDefaults = {
          ...response,
          bannerColor: response.bannerColor || '#e0f2fe',
          pageBackgroundColor: response.pageBackgroundColor || '#f8f9fa',
          cardBackgroundColor: response.cardBackgroundColor || '#ffffff',
          fontColor: response.fontColor || '#000000',
          fontFamily: response.fontFamily || '',
          avatarPosition: response.avatarPosition || 'center',
          avatarShape: response.avatarShape || 'circle',
          backgroundImage: response.backgroundImage || '',
          backgroundPosition: response.backgroundPosition || 'center',
          backgroundRepeat: response.backgroundRepeat || 'no-repeat',
          backgroundSize: response.backgroundSize || 'cover',
          backgroundAttachment: response.backgroundAttachment || 'scroll'
        };

        // Log the background image for debugging
        console.log('AuthContext: Login with backgroundImage:', response.backgroundImage);

        setUser(userWithDefaults);
      } else {
        throw new Error('Invalid authentication token received');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await apiRegister(data);

      // Validate token before storing
      if (checkTokenValidity(response.token)) {
        // For new registrations, always store in localStorage for persistence
        localStorage.setItem('token', response.token);

        // Ensure default values are set for profile customization
        const userWithDefaults = {
          ...response,
          bannerColor: response.bannerColor || '#e0f2fe',
          pageBackgroundColor: response.pageBackgroundColor || '#f8f9fa',
          cardBackgroundColor: response.cardBackgroundColor || '#ffffff',
          fontColor: response.fontColor || '#000000',
          fontFamily: response.fontFamily || '',
          avatarPosition: response.avatarPosition || 'center',
          avatarShape: response.avatarShape || 'circle',
          backgroundImage: response.backgroundImage || '',
          backgroundPosition: response.backgroundPosition || 'center',
          backgroundRepeat: response.backgroundRepeat || 'no-repeat',
          backgroundSize: response.backgroundSize || 'cover',
          backgroundAttachment: response.backgroundAttachment || 'scroll'
        };

        // Log the background image for debugging
        console.log('AuthContext: Register with backgroundImage:', response.backgroundImage);

        setUser(userWithDefaults);
      } else {
        throw new Error('Invalid authentication token received');
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Call the logout API endpoint to clear the HTTP-only cookie
      await api.post('/auth/logout');

      // Clear token from both storages (for backward compatibility)
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');

      // Clear user state
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);

      // Even if the API call fails, clear local storage and user state
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      setUser(null);
    }
  };

  const updateUserData = (userData: Partial<User>) => {
    if (user) {
      console.log('AuthContext: Updating user data with:', userData);
      console.log('AuthContext: Current user data:', user);

      const updatedUser = {
        ...user,
        ...userData
      };

      console.log('AuthContext: Updated user data:', updatedUser);
      setUser(updatedUser);
    } else {
      console.error('AuthContext: Cannot update user data, user is null');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        register,
        logout,
        updateUserData,
        checkTokenValidity,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
