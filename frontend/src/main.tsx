import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { validateReferralCode } from './api/referrals'
import { getHashParam } from './utils/urlUtils'

// Check for referral code in URL immediately on page load
const checkReferralCodeOnLoad = async () => {
  try {
    // Extract referral code from hash part of URL using utility function
    const refCode = getHashParam('ref');

    if (refCode) {
      console.log('Referral code found in URL on initial load:', refCode);

      // Validate the referral code
      const response = await validateReferralCode(refCode);
      console.log('Referral validation response on initial load:', response);
    }
  } catch (err) {
    console.error('Error checking referral code on initial load:', err);
  }
};

// Run the check immediately
checkReferralCodeOnLoad();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
