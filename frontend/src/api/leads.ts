import api from './axios';

export interface LeadFormData {
  [key: string]: string;
}

export interface Lead {
  _id: string;
  linkId: string;
  userId: string;
  title: string;
  formData: LeadFormData;
  createdAt: string;
  updatedAt: string;
}

export interface LeadStats {
  totalLeads: number;
  leadsPerForm: {
    _id: string;
    title: string;
    count: number;
  }[];
  leadsPerDay: {
    date: string;
    count: number;
  }[];
}

export const submitLead = async (linkId: string, formData: LeadFormData): Promise<{ success: boolean }> => {
  const response = await api.post('/leads', { linkId, formData });
  return response.data;
};

export const getLeads = async (linkId?: string): Promise<Lead[]> => {
  console.log('API getLeads called with linkId:', linkId);
  const params = linkId ? { linkId } : {};
  console.log('API params:', params);
  const response = await api.get('/leads', { params });
  console.log('API response status:', response.status);
  return response.data;
};

export const getLeadStats = async (): Promise<LeadStats> => {
  const response = await api.get('/leads/stats');
  return response.data;
};

export const deleteLead = async (id: string): Promise<void> => {
  await api.delete(`/leads/${id}`);
};
