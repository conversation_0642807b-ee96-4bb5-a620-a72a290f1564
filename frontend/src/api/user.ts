import api from './axios';

export interface CountryProfile {
  countryCode: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: 'left' | 'center' | 'right';
  avatarShape?: 'circle' | 'square';
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
  backgroundSize?: 'auto' | 'cover';
  backgroundAttachment?: 'fixed' | 'scroll';
  enabled: boolean;
}

export interface User {
  _id: string;
  email: string;
  username: string;
  slug: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: 'left' | 'center' | 'right';
  avatarShape?: 'circle' | 'square';
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
  backgroundSize?: 'auto' | 'cover';
  backgroundAttachment?: 'fixed' | 'scroll';
  theme?: 'light' | 'dark' | 'custom';
  directRedirectEnabled?: boolean;
  directRedirectUrl?: string;
  verifiedProfile?: boolean;
  verifiedProfileGold?: boolean;
  countryProfiles?: CountryProfile[];
  // Analytics settings
  googleAnalyticsId?: string;
  microsoftClarityId?: string;
  // SEO settings
  seoTitle?: string;
  seoDescription?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  ogType?: string;
  // Direct Monetization settings
  stripePaymentLink?: string;
  paypalPaymentLink?: string;
  molliePaymentLink?: string;
  razorpayPaymentLink?: string;
  // Amazon Affiliate settings
  amazonAffiliateId?: string;
  amazonAffiliateAutoApply?: boolean;
  // Payment callback URLs
  paymentThankYouUrl?: string;
  paymentCancellationUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileData {
  username?: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: 'left' | 'center' | 'right';
  avatarShape?: 'circle' | 'square';
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
  backgroundSize?: 'auto' | 'cover';
  backgroundAttachment?: 'fixed' | 'scroll';
  theme?: 'light' | 'dark' | 'custom';
  directRedirectEnabled?: boolean;
  directRedirectUrl?: string;
  verifiedProfile?: boolean;
  verifiedProfileGold?: boolean;
  // Analytics settings
  googleAnalyticsId?: string;
  microsoftClarityId?: string;
  // SEO settings
  seoTitle?: string;
  seoDescription?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  ogType?: string;
  // Direct Monetization settings
  stripePaymentLink?: string;
  paypalPaymentLink?: string;
  molliePaymentLink?: string;
  razorpayPaymentLink?: string;
  // Amazon Affiliate settings
  amazonAffiliateId?: string;
  amazonAffiliateAutoApply?: boolean;
  // Payment callback URLs
  paymentThankYouUrl?: string;
  paymentCancellationUrl?: string;
}

export interface UpdateCountryProfileData {
  countryCode: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: 'left' | 'center' | 'right';
  avatarShape?: 'circle' | 'square';
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
  backgroundSize?: 'auto' | 'cover';
  backgroundAttachment?: 'fixed' | 'scroll';
  enabled?: boolean;
}

export interface QRCodeResponse {
  qrCode: string;
  profileUrl: string;
}

export interface UpdateSlugData {
  slug: string;
}

export interface PublicProfile {
  user: Omit<User, 'email'>;
  links: Array<{
    _id: string;
    title: string;
    url: string;
    order: number;
    clickCount: number;
  }>;
}

export const updateProfile = async (data: UpdateProfileData): Promise<User> => {
  const response = await api.put('/users/profile', data);
  return response.data;
};

export const updateSlug = async (data: UpdateSlugData): Promise<{ _id: string; slug: string }> => {
  const response = await api.put('/users/slug', data);
  return response.data;
};

export const getPublicProfile = async (slug: string, countryCode?: string): Promise<PublicProfile> => {
  const url = countryCode
    ? `/users/${slug}?countryCode=${countryCode}`
    : `/users/${slug}`;
  const response = await api.get(url);
  return response.data;
};

export const updateCountryProfile = async (data: UpdateCountryProfileData): Promise<User> => {
  const response = await api.put('/users/country-profile', data);
  return response.data;
};

export const deleteCountryProfile = async (countryCode: string): Promise<User> => {
  const response = await api.delete(`/users/country-profile/${countryCode}`);
  return response.data;
};

export const getQRCode = async (): Promise<QRCodeResponse> => {
  const response = await api.get('/users/qrcode');
  return response.data;
};
