import api from './axios';

export interface RegisterData {
  email: string;
  password: string;
  username: string;
}

export interface LoginData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  _id: string;
  email: string;
  username: string;
  slug: string;
  token: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: 'left' | 'center' | 'right';
  avatarShape?: 'circle' | 'square';
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
  backgroundSize?: 'auto' | 'cover';
  backgroundAttachment?: 'fixed' | 'scroll';
  theme?: 'light' | 'dark' | 'custom';
  directRedirectEnabled?: boolean;
  directRedirectUrl?: string;
  verifiedProfile?: boolean;
  verifiedProfileGold?: boolean;
  countryProfiles?: Array<{
    countryCode: string;
    bio?: string;
    avatarUrl?: string;
    avatarPosition?: 'left' | 'center' | 'right';
    avatarShape?: 'circle' | 'square';
    bannerUrl?: string;
    bannerColor?: string;
    pageBackgroundColor?: string;
    cardBackgroundColor?: string;
    fontColor?: string;
    fontFamily?: string;
    backgroundImage?: string;
    backgroundPosition?: 'left' | 'center' | 'right' | 'top' | 'bottom';
    backgroundRepeat?: 'no-repeat' | 'repeat' | 'space';
    backgroundSize?: 'auto' | 'cover';
    backgroundAttachment?: 'fixed' | 'scroll';
    enabled: boolean;
  }>;
  // Analytics settings
  googleAnalyticsId?: string;
  microsoftClarityId?: string;
  // SEO settings
  seoTitle?: string;
  seoDescription?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  ogType?: string;
  // Direct Monetization settings
  stripePaymentLink?: string;
  paypalPaymentLink?: string;
  molliePaymentLink?: string;
  razorpayPaymentLink?: string;
  // Amazon Affiliate settings
  amazonAffiliateId?: string;
  amazonAffiliateAutoApply?: boolean;
  // Payment callback URLs
  paymentThankYouUrl?: string;
  paymentCancellationUrl?: string;
  // Referral settings
  referralCode?: string;
  referralEnabled?: boolean;
  referralVipCode?: string;
  referralVipUsageLimit?: number;
  referralVipUsageCount?: number;
  referredBy?: string;
  referrals?: string[];
  // Marketplace settings
  marketplaceEnabled?: boolean;
  marketplaceCommission?: number;
  tipJarEnabled?: boolean;
  defaultCurrency?: string;
}

export const register = async (data: RegisterData): Promise<AuthResponse> => {
  const response = await api.post('/auth/register', data);
  return response.data;
};

export const login = async (data: LoginData): Promise<AuthResponse> => {
  const response = await api.post('/auth/login', data);
  return response.data;
};

export const getProfile = async () => {
  const response = await api.get('/auth/profile');
  return response.data;
};
