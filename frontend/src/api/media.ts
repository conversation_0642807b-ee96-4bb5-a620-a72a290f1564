import api from './axios';

/**
 * Upload a profile image (avatar, banner, or shuffle card)
 * @param file The image file to upload
 * @param type The type of image (avatar, banner, or shuffle)
 * @returns The URL of the uploaded image
 */
export const uploadProfileImage = async (
  file: File,
  type: 'avatar' | 'banner' | 'shuffle' | 'video-thumbnail' | 'background'
): Promise<string> => {
  console.log(`uploadProfileImage called with type: ${type}, file:`, {
    name: file.name,
    type: file.type,
    size: file.size
  });

  // Create a FormData object to send the file
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);

  try {
    // Send the request
    console.log('Sending upload request to /media/upload');
    const response = await api.post('/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('Upload response:', response.data);
    return response.data.imageUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

/**
 * Delete a profile image
 * @param imageUrl The URL of the image to delete
 * @returns Success status
 */
export const deleteProfileImage = async (imageUrl: string): Promise<boolean> => {
  const response = await api.delete('/media/delete', {
    data: { imageUrl },
  });

  return response.data.success;
};

/**
 * Upload a video file to Bunny CDN
 * @param file The video file to upload
 * @param title Optional title for the video
 * @param onProgress Optional callback for upload progress
 * @returns The URL of the uploaded video, its ID, and thumbnail URL
 */
export const uploadBunnyVideo = async (
  file: File,
  title?: string,
  onProgress?: (progress: number) => void
): Promise<{ videoUrl: string; videoId: string; thumbnailUrl: string; duration: number }> => {
  console.log(`uploadBunnyVideo called with file:`, {
    name: file.name,
    type: file.type,
    size: file.size
  });

  // Create a FormData object to send the file
  const formData = new FormData();
  formData.append('file', file);
  if (title) {
    formData.append('title', title);
  }

  try {
    // Send the request with progress tracking
    console.log('Sending upload request to /media/upload-bunny-video');
    const response = await api.post('/media/upload-bunny-video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`Upload progress: ${progress}%`);
          onProgress(progress);
        }
      },
    });

    console.log('Bunny CDN video upload response:', response.data);
    return {
      videoUrl: response.data.videoUrl,
      videoId: response.data.videoId,
      thumbnailUrl: response.data.thumbnailUrl,
      duration: response.data.duration || 0
    };
  } catch (error) {
    console.error('Error uploading video to Bunny CDN:', error);
    throw error;
  }
};

/**
 * Delete a video from Bunny CDN
 * @param videoId The ID of the video to delete
 * @returns Success status
 */
export const deleteBunnyVideo = async (videoId: string): Promise<boolean> => {
  const response = await api.delete('/media/delete-bunny-video', {
    data: { videoId },
  });

  return response.data.success;
};

/**
 * Upload a Lottie JSON file
 * @param file The JSON file to upload
 * @returns The URL of the uploaded JSON file
 */
export const uploadLottieJson = async (file: File): Promise<string> => {
  console.log(`uploadLottieJson called with file:`, {
    name: file.name,
    type: file.type,
    size: file.size
  });

  // Create a FormData object to send the file
  const formData = new FormData();
  formData.append('file', file);

  try {
    // Send the request
    console.log('Sending upload request to /media/upload-lottie-json');
    const response = await api.post('/media/upload-lottie-json', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('Lottie JSON upload response:', response.data);
    return response.data.jsonUrl;
  } catch (error) {
    console.error('Error uploading Lottie JSON:', error);
    throw error;
  }
};

/**
 * Delete a Lottie JSON file
 * @param jsonUrl The URL of the JSON file to delete
 * @returns Success status
 */
export const deleteLottieJson = async (jsonUrl: string): Promise<boolean> => {
  const response = await api.delete('/media/delete-lottie-json', {
    data: { jsonUrl },
  });

  return response.data.success;
};
