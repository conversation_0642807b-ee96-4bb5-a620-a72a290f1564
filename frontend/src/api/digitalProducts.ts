import api from './api';

export interface DigitalProduct {
  _id: string;
  userId: string;
  title: string;
  description: string;
  productType: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other';
  price: number;
  currency: string;
  downloadUrl: string;
  previewImages: string[];
  fileSize: string;
  fileFormat: string;
  tags: string[];
  isActive: boolean;
  downloadCount: number;
  purchaseCount: number;
  slug: string;
  metaDescription?: string;
  originalFileName?: string;
  fileHash?: string;
  discountPrice?: number;
  discountEndDate?: Date;
  requiresAccount?: boolean;
  maxDownloads?: number;
  downloadExpiryDays?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDigitalProductData {
  title: string;
  description: string;
  productType: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other';
  price: number;
  currency: string;
  downloadUrl: string;
  previewImages?: string[];
  fileSize?: string;
  fileFormat?: string;
  tags?: string[];
  metaDescription?: string;
  originalFileName?: string;
  requiresAccount?: boolean;
  maxDownloads?: number;
  downloadExpiryDays?: number;
}

export interface UpdateDigitalProductData extends Partial<CreateDigitalProductData> {
  isActive?: boolean;
}

export interface DigitalProductsResponse {
  products: DigitalProduct[];
  totalPages: number;
  currentPage: number;
  total: number;
}

export interface ProductAnalytics {
  totalPurchases: number;
  totalDownloads: number;
  totalRevenue: number;
  averageOrderValue: number;
  recentPurchases: any[];
}

// Create a new digital product
export const createDigitalProduct = async (data: CreateDigitalProductData): Promise<DigitalProduct> => {
  const response = await api.post('/digital-products', data);
  return response.data;
};

// Get all digital products for the authenticated user
export const getDigitalProducts = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  productType?: string;
  isActive?: boolean;
}): Promise<DigitalProductsResponse> => {
  const response = await api.get('/digital-products', { params });
  return response.data;
};

// Get public digital products for marketplace
export const getMarketplaceProducts = async (
  userId: string,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    productType?: string;
  }
): Promise<DigitalProductsResponse> => {
  const response = await api.get(`/digital-products/marketplace/${userId}`, { params });
  return response.data;
};

// Get a single digital product
export const getDigitalProduct = async (id: string): Promise<DigitalProduct> => {
  const response = await api.get(`/digital-products/${id}`);
  return response.data;
};

// Update a digital product
export const updateDigitalProduct = async (
  id: string,
  data: UpdateDigitalProductData
): Promise<DigitalProduct> => {
  const response = await api.put(`/digital-products/${id}`, data);
  return response.data;
};

// Delete a digital product
export const deleteDigitalProduct = async (id: string): Promise<void> => {
  await api.delete(`/digital-products/${id}`);
};

// Get product analytics
export const getProductAnalytics = async (id: string): Promise<ProductAnalytics> => {
  const response = await api.get(`/digital-products/${id}/analytics`);
  return response.data;
};
