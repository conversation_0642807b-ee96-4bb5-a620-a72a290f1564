import api from './axios';

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export enum UserRole {
  OWNER = 'owner',
  EDITOR = 'editor'
}

export interface Invitation {
  _id: string;
  email: string;
  profileId: string;
  invitedBy: string;
  role: UserRole;
  status: InvitationStatus;
  token: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface Collaborator {
  userId: {
    _id: string;
    username: string;
    email: string;
  };
  role: UserRole;
  addedAt: string;
}

export interface SendInvitationData {
  email: string;
  role: UserRole;
}

export const sendInvitation = async (data: SendInvitationData): Promise<Invitation> => {
  const response = await api.post('/invitations', data);
  return response.data;
};

export const getInvitations = async (): Promise<Invitation[]> => {
  const response = await api.get('/invitations');
  return response.data;
};

export const cancelInvitation = async (id: string): Promise<{ message: string }> => {
  const response = await api.delete(`/invitations/${id}`);
  return response.data;
};

export const acceptInvitation = async (token: string): Promise<any> => {
  const response = await api.post(`/invitations/accept/${token}`);
  return response.data;
};

export const rejectInvitation = async (token: string): Promise<{ message: string }> => {
  const response = await api.post(`/invitations/reject/${token}`);
  return response.data;
};

export const getCollaborators = async (): Promise<Collaborator[]> => {
  const response = await api.get('/invitations/collaborators');
  return response.data;
};

export const removeCollaborator = async (userId: string): Promise<{ message: string }> => {
  const response = await api.delete(`/invitations/collaborators/${userId}`);
  return response.data;
};

export const updateCollaboratorRole = async (
  userId: string,
  role: UserRole
): Promise<{ message: string; collaborator: Collaborator }> => {
  const response = await api.put(`/invitations/collaborators/${userId}`, { role });
  return response.data;
};
