import api from './axios';

export interface Badge {
  text: string;
  backgroundColor: string;
  textColor: string;
  animation?: 'popup' | 'shake' | 'vibrate';
  animationDelay?: number;
}

export interface ShuffleCard {
  imageUrl: string;
  title: string;
  tag?: string;
  url: string;
}

export interface MiniLeadField {
  type: 'email' | 'phone' | 'activity';
  required: boolean;
  label?: string;
}

export interface VisibilityDates {
  startDate?: string;
  endDate?: string;
  enabled?: boolean;
}

export interface Link {
  _id: string;
  type: 'link' | 'announcement' | 'shuffle' | 'minilead' | 'video' | 'embedded' | 'lottie' | 'digitalproduct' | 'tipjar';
  title: string;
  url?: string;
  icon?: string;
  order: number;
  clickCount: number;
  archived: boolean;
  enabled: boolean;
  badge?: Badge;
  // Advanced options
  password?: string;
  passwordEnabled?: boolean;
  passwordExpiryDate?: string;
  visibilityDates?: VisibilityDates;
  // Announcement specific fields
  backgroundColor?: string;
  backgroundGradient?: {
    color1: string;
    color2: string;
    direction: string;
  };
  useGradient?: boolean;
  textColor?: string;
  text?: string;
  titleAlignment?: 'left' | 'center' | 'right';
  textAlignment?: 'left' | 'center' | 'right';
  buttonText?: string;
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  // Shuffle cards specific fields
  shuffleCards?: ShuffleCard[];
  // MiniLead specific fields
  introText?: string;
  fields?: MiniLeadField[];
  // Video specific fields
  videoUrl?: string;
  thumbnailUrl?: string;
  orientation?: 'portrait' | 'landscape';
  duration?: number;
  videoId?: string; // Bunny CDN video ID
  // Embedded video specific fields
  youtubeCode?: string;
  tikTokUrl?: string;
  embedType?: 'youtube' | 'tiktok' | 'stream';
  autoplay?: boolean;
  muted?: boolean;
  showControls?: boolean;
  // Lottie animation specific fields
  lottieUrl?: string;
  lottieJsonUrl?: string; // For uploaded JSON files
  triggerEvent?: 'pageLoad' | 'inactivity' | 'linkClick';
  triggerDelay?: number;
  linkId?: string;
  // Digital Product specific fields
  productType?: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other';
  price?: number;
  currency?: string;
  description?: string;
  downloadUrl?: string;
  previewImages?: string[];
  fileSize?: string;
  fileFormat?: string;
  digitalProductId?: string;
  // Tip Jar specific fields
  tipAmounts?: number[];
  customAmountEnabled?: boolean;
  tipMessage?: string;
  thankYouMessage?: string;
  minTipAmount?: number;
  maxTipAmount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateLinkData {
  type: 'link' | 'announcement' | 'shuffle' | 'minilead' | 'video' | 'embedded' | 'lottie' | 'digitalproduct' | 'tipjar';
  title: string;
  url?: string;
  icon?: string;
  badge?: Badge;
  // Advanced options
  password?: string;
  passwordEnabled?: boolean;
  passwordExpiryDate?: string;
  visibilityDates?: VisibilityDates;
  // Announcement specific fields
  backgroundColor?: string;
  backgroundGradient?: {
    color1: string;
    color2: string;
    direction: string;
  };
  useGradient?: boolean;
  textColor?: string;
  text?: string;
  titleAlignment?: 'left' | 'center' | 'right';
  textAlignment?: 'left' | 'center' | 'right';
  buttonText?: string;
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  // Shuffle cards specific fields
  shuffleCards?: ShuffleCard[];
  // MiniLead specific fields
  introText?: string;
  fields?: MiniLeadField[];
  // Video specific fields
  videoUrl?: string;
  thumbnailUrl?: string;
  orientation?: 'portrait' | 'landscape';
  duration?: number;
  videoId?: string; // Bunny CDN video ID
  // Embedded video specific fields
  youtubeCode?: string;
  tikTokUrl?: string;
  embedType?: 'youtube' | 'tiktok' | 'stream';
  autoplay?: boolean;
  muted?: boolean;
  showControls?: boolean;
  // Lottie animation specific fields
  lottieUrl?: string;
  lottieJsonUrl?: string; // For uploaded JSON files
  triggerEvent?: 'pageLoad' | 'inactivity' | 'linkClick';
  triggerDelay?: number;
  linkId?: string;
  // Digital Product specific fields
  productType?: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other';
  price?: number;
  currency?: string;
  description?: string;
  downloadUrl?: string;
  previewImages?: string[];
  fileSize?: string;
  fileFormat?: string;
  digitalProductId?: string;
  // Tip Jar specific fields
  tipAmounts?: number[];
  customAmountEnabled?: boolean;
  tipMessage?: string;
  thankYouMessage?: string;
  minTipAmount?: number;
  maxTipAmount?: number;
}

export interface UpdateLinkData {
  title?: string;
  url?: string;
  icon?: string;
  badge?: Badge | null;
  enabled?: boolean;
  // Advanced options
  password?: string;
  passwordEnabled?: boolean;
  passwordExpiryDate?: string | null;
  visibilityDates?: VisibilityDates | null;
  // Announcement specific fields
  backgroundColor?: string;
  backgroundGradient?: {
    color1: string;
    color2: string;
    direction: string;
  };
  useGradient?: boolean;
  textColor?: string;
  text?: string;
  titleAlignment?: 'left' | 'center' | 'right';
  textAlignment?: 'left' | 'center' | 'right';
  buttonText?: string;
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  // Shuffle cards specific fields
  shuffleCards?: ShuffleCard[];
  // MiniLead specific fields
  introText?: string;
  fields?: MiniLeadField[];
  // Video specific fields
  videoUrl?: string;
  thumbnailUrl?: string;
  orientation?: 'portrait' | 'landscape';
  duration?: number;
  videoId?: string; // Bunny CDN video ID
  // Embedded video specific fields
  youtubeCode?: string;
  tikTokUrl?: string;
  embedType?: 'youtube' | 'tiktok' | 'stream';
  autoplay?: boolean;
  muted?: boolean;
  showControls?: boolean;
  // Lottie animation specific fields
  lottieUrl?: string;
  lottieJsonUrl?: string; // For uploaded JSON files
  triggerEvent?: 'pageLoad' | 'inactivity' | 'linkClick';
  triggerDelay?: number;
  linkId?: string;
  // Digital Product specific fields
  productType?: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other';
  price?: number;
  currency?: string;
  description?: string;
  downloadUrl?: string;
  previewImages?: string[];
  fileSize?: string;
  fileFormat?: string;
  digitalProductId?: string;
  // Tip Jar specific fields
  tipAmounts?: number[];
  customAmountEnabled?: boolean;
  tipMessage?: string;
  thankYouMessage?: string;
  minTipAmount?: number;
  maxTipAmount?: number;
}

export interface ReorderLinksData {
  links: { _id: string; order: number }[];
}

export const getLinks = async (archived?: boolean): Promise<Link[]> => {
  const params = archived !== undefined ? { archived: String(archived) } : {};
  const response = await api.get('/links', { params });
  return response.data;
};

export const createLink = async (data: CreateLinkData): Promise<Link> => {
  const response = await api.post('/links', data);
  return response.data;
};

export const updateLink = async (id: string, data: UpdateLinkData): Promise<Link> => {
  console.log('updateLink called with data:', data);

  // Add debugging for video components
  if (data.videoUrl || data.embedType) {
    console.log('Updating video component with:', {
      videoUrl: data.videoUrl,
      embedType: data.embedType,
      videoId: data.videoId
    });
  }

  const response = await api.put(`/links/${id}`, data);
  console.log('updateLink response:', response.data);
  return response.data;
};

export const deleteLink = async (id: string): Promise<void> => {
  await api.delete(`/links/${id}`);
};

export const reorderLinks = async (data: ReorderLinksData): Promise<Link[]> => {
  const response = await api.put('/links/reorder', data);
  return response.data;
};

export const trackLinkClick = async (id: string, password?: string): Promise<{ success: boolean; url: string; passwordRequired?: boolean; passwordCorrect?: boolean }> => {
  const response = await api.post(`/links/${id}/click`, password ? { password } : {});
  return response.data;
};

export const archiveLink = async (id: string): Promise<Link> => {
  const response = await api.put(`/links/${id}/archive`);
  return response.data;
};

export const unarchiveLink = async (id: string): Promise<Link> => {
  const response = await api.put(`/links/${id}/unarchive`);
  return response.data;
};

export const toggleLinkEnabled = async (id: string, enabled: boolean): Promise<Link> => {
  const response = await api.put(`/links/${id}`, { enabled });
  return response.data;
};
