import api from './axios';

export interface TipTransaction {
  _id: string;
  recipientId: string;
  tipJarId: string;
  transactionId: string;
  paymentProvider: 'stripe' | 'paypal' | 'mollie' | 'razorpay';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  tipperEmail?: string;
  tipperName?: string;
  tipperMessage?: string;
  isAnonymous: boolean;
  paymentIntentId?: string;
  paymentMethodId?: string;
  refundId?: string;
  refundAmount?: number;
  refundReason?: string;
  ipAddress?: string;
  userAgent?: string;
  giftType?: 'coffee' | 'beer' | 'pizza' | 'heart' | 'star' | 'custom';
  giftEmoji?: string;
  giftMessage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTipData {
  tipJarId: string;
  amount: number;
  currency: string;
  paymentProvider: 'stripe' | 'paypal' | 'mollie' | 'razorpay';
  tipperEmail?: string;
  tipperName?: string;
  tipperMessage?: string;
  isAnonymous?: boolean;
  giftType?: 'coffee' | 'beer' | 'pizza' | 'heart' | 'star' | 'custom';
  giftEmoji?: string;
  giftMessage?: string;
}

export interface TipsResponse {
  tips: TipTransaction[];
  totalPages: number;
  currentPage: number;
  total: number;
  totalEarnings: number;
}

export interface TipAnalytics {
  summary: {
    totalTips: number;
    totalAmount: number;
    averageTip: number;
    uniqueTippers: string[];
  };
  tipsByGiftType: Array<{
    _id: string;
    count: number;
    totalAmount: number;
  }>;
  dailyTips: Array<{
    _id: string;
    count: number;
    amount: number;
  }>;
  uniqueTippersCount: number;
}

export interface TipJarSettings {
  title: string;
  tipMessage: string;
  thankYouMessage: string;
  tipAmounts: number[];
  customAmountEnabled: boolean;
  minTipAmount: number;
  maxTipAmount: number;
  currency: string;
  recipientName: string;
}

// Create a new tip transaction
export const createTip = async (data: CreateTipData): Promise<{
  transactionId: string;
  amount: number;
  currency: string;
  message: string;
}> => {
  const response = await api.post('/tips', data);
  return response.data;
};

// Get tips received by the authenticated user
export const getReceivedTips = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TipsResponse> => {
  const response = await api.get('/tips/received', { params });
  return response.data;
};

// Get tip analytics for the authenticated user
export const getTipAnalytics = async (params?: {
  period?: '7d' | '30d' | '90d' | '1y';
}): Promise<TipAnalytics> => {
  const response = await api.get('/tips/analytics', { params });
  return response.data;
};

// Update tip transaction status (webhook)
export const updateTipStatus = async (
  transactionId: string,
  data: {
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    paymentIntentId?: string;
    paymentMethodId?: string;
  }
): Promise<{ message: string }> => {
  const response = await api.put(`/tips/${transactionId}/status`, data);
  return response.data;
};

// Get tip jar settings (public)
export const getTipJarSettings = async (tipJarId: string): Promise<TipJarSettings> => {
  const response = await api.get(`/tips/jar/${tipJarId}`);
  return response.data;
};

// Get recent tips for tip jar (public display)
export const getRecentTips = async (
  tipJarId: string,
  params?: { limit?: number }
): Promise<TipTransaction[]> => {
  const response = await api.get(`/tips/jar/${tipJarId}/recent`, { params });
  return response.data;
};
