import api from './axios';
import { Link } from './links';

export interface DailyClickStats {
  date: string;
  count: number;
}

export interface LinkWithStats extends Link {
  totalClicks: number;
}

export interface StatsResponse {
  totalClicks: number;
  linksWithStats: LinkWithStats[];
  clicksPerDay: DailyClickStats[];
}

export interface LinkStatsResponse {
  link: Link;
  totalClicks: number;
  clicksPerDay: DailyClickStats[];
}

export interface DirectRedirectStatsResponse {
  totalRedirects: number;
  redirectsPerDay: DailyClickStats[];
}

export interface DirectRedirectCheckResponse {
  enabled: boolean;
  url: string;
}

export const getStats = async (): Promise<StatsResponse> => {
  const response = await api.get('/stats');
  return response.data;
};

export const getLinkStats = async (linkId: string): Promise<LinkStatsResponse> => {
  const response = await api.get(`/stats/${linkId}`);
  return response.data;
};

/**
 * Track a direct redirect
 * @param userId User ID
 * @param countryCode Optional country code
 * @returns Success status
 */
export const trackDirectRedirect = async (userId: string, countryCode?: string): Promise<{ success: boolean }> => {
  try {
    // Log the tracking attempt
    console.log(`Attempting to track direct redirect for user ${userId} with country code ${countryCode || 'none'}`);
    console.log(`API base URL: ${api.defaults.baseURL}`);

    // Use sendBeacon if available for more reliable tracking during page navigation
    if (navigator.sendBeacon) {
      const blob = new Blob([JSON.stringify({ countryCode })], { type: 'application/json' });
      // Make sure we're using the correct endpoint
      const endpoint = `${api.defaults.baseURL}/stats/direct-redirect/${userId}`;
      console.log(`Sending beacon to endpoint: ${endpoint}`);
      const success = navigator.sendBeacon(endpoint, blob);
      console.log(`Sent beacon to ${endpoint}`, success ? 'successfully' : 'failed');
      return { success };
    } else {
      // Fallback to regular API call
      console.log(`Sending POST to /stats/direct-redirect/${userId}`);
      const response = await api.post(`/stats/direct-redirect/${userId}`, { countryCode });
      console.log('Direct redirect tracking response:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('Error tracking direct redirect:', error);
    return { success: false };
  }
};

/**
 * Check if a user has direct redirect enabled
 * @param userId User ID
 * @returns Redirect status and URL
 */
export const checkDirectRedirect = async (userId: string): Promise<DirectRedirectCheckResponse> => {
  try {
    // Check localStorage cache first
    const cacheKey = `redirect-check-${userId}`;
    const cachedData = localStorage.getItem(cacheKey);

    console.log(`Checking direct redirect for user ${userId}`);
    console.log(`Cache key: ${cacheKey}`);
    console.log(`Cached data found: ${cachedData ? 'yes' : 'no'}`);

    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        const { data, timestamp } = parsed;
        const cacheAge = Date.now() - timestamp;

        console.log(`Cache age: ${cacheAge}ms (${cacheAge / 1000} seconds)`);

        // Use cache if it's less than 1 minute old
        if (cacheAge < 60000) {
          console.log(`Using cached data:`, data);
          return data;
        } else {
          console.log(`Cache expired, fetching fresh data`);
        }
      } catch (parseError) {
        console.error(`Error parsing cached data:`, parseError);
        // Clear invalid cache entry
        localStorage.removeItem(cacheKey);
      }
    }

    // If no valid cache, make API call
    console.log(`Making API call to /redirect/check/${userId}`);
    const response = await api.get(`/redirect/check/${userId}`);
    console.log(`API response:`, response.data);

    // Cache the result
    try {
      localStorage.setItem(cacheKey, JSON.stringify({
        data: response.data,
        timestamp: Date.now()
      }));
      console.log(`Cached new data`);
    } catch (storageError) {
      console.error(`Error caching data:`, storageError);
      // Continue without caching
    }

    return response.data;
  } catch (error) {
    console.error('Error checking direct redirect:', error);
    return { enabled: false, url: '' };
  }
};

/**
 * Get direct redirect statistics
 * @param dateRange Optional date range
 * @returns Redirect statistics
 */
export const getDirectRedirectStats = async (dateRange?: { startDate?: Date, endDate?: Date }): Promise<DirectRedirectStatsResponse> => {
  try {
    let url = '/direct-redirect';

    // Add date range parameters if provided
    if (dateRange) {
      const params = new URLSearchParams();
      if (dateRange.startDate) {
        params.append('startDate', dateRange.startDate.toISOString());
      }
      if (dateRange.endDate) {
        params.append('endDate', dateRange.endDate.toISOString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }
    }

    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error getting direct redirect stats:', error);
    return { totalRedirects: 0, redirectsPerDay: [] };
  }
};
