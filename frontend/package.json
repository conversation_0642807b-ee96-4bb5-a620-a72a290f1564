{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "node simple-server.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@tiptap/core": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.9.2", "hls.js": "^1.6.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "compression": "^1.8.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "express": "^5.1.0", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "terser": "^5.39.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-compression": "^0.5.1"}}