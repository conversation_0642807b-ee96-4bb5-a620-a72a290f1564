#!/usr/bin/env node

import express from 'express';
import compression from 'compression';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Ottieni il percorso della directory corrente
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 5173;

// Abilita la compressione gzip
app.use(compression());

// Servi i file statici dalla directory dist
app.use(express.static(path.join(__dirname, 'dist')));

// Gestisci le route per SPA (Single Page Application)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Avvia il server
app.listen(PORT, () => {
  console.log(`Preview server with compression running at http://localhost:${PORT}`);
  console.log('Press Ctrl+C to stop');
});
