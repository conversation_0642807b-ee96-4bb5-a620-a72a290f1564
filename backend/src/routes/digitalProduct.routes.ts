import express, { RequestHand<PERSON> } from 'express';
import {
  createDigitalProduct,
  getDigitalProducts,
  getMarketplaceProducts,
  getDigitalProduct,
  updateDigitalProduct,
  deleteDigitalProduct,
  getProductAnalytics,
} from '../controllers/digitalProduct.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

// Protected routes
router.post('/', protect, createDigitalProduct as RequestHandler);
router.get('/', protect, getDigitalProducts as RequestHandler);
router.get('/:id', protect, getDigitalProduct as RequestHandler);
router.put('/:id', protect, updateDigitalProduct as RequestHandler);
router.delete('/:id', protect, deleteDigitalProduct as RequestHandler);
router.get('/:id/analytics', protect, getProductAnalytics as RequestHandler);

// Public routes
router.get('/marketplace/:userId', getMarketplaceProducts as RequestHandler);

export default router;
