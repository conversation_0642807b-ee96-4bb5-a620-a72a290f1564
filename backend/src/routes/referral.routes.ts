import express, { Re<PERSON><PERSON><PERSON><PERSON> } from 'express';
import {
  enableReferrals,
  disableReferrals,
  getReferralInfo,
  validateReferralCode
} from '../controllers/referral.controller';

// Add a new controller function to check cookie status
const checkCookieStatus = (req: express.Request, res: express.Response) => {
  console.log('Checking cookie status');
  console.log('All cookies:', req.cookies);

  // Return all cookies for debugging
  res.json({
    cookies: req.cookies,
    message: 'Cookie status check'
  });
};

// Add a function to set a test cookie
const setTestCookie = (req: express.Request, res: express.Response) => {
  const code = req.params.code || 'TEST-CODE';
  console.log('Setting test cookie with code:', code);

  // Set HTTP-only cookie for security
  res.cookie('referralCode', code, {
    maxAge: 90 * 24 * 60 * 60 * 1000, // 90 days
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // Only use secure in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' as 'none' : 'lax' as 'lax', // Use 'none' for production, 'lax' for development
    path: '/'
  });

  // Set a non-HTTP-only cookie for debugging purposes
  res.cookie('referralCodeDebug', code, {
    maxAge: 90 * 24 * 60 * 60 * 1000, // 90 days
    httpOnly: false, // Must be false to be accessible from JavaScript
    secure: process.env.NODE_ENV === 'production', // Only use secure in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' as 'none' : 'lax' as 'lax', // Use 'none' for production, 'lax' for development
    path: '/'
  });

  res.json({
    message: 'Test cookie set',
    code: code
  });
};
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

router.put('/enable', protect, enableReferrals as RequestHandler);
router.put('/disable', protect, disableReferrals as RequestHandler);
router.get('/', protect, getReferralInfo as RequestHandler);
router.post('/validate', validateReferralCode as RequestHandler);
router.get('/check-cookies', checkCookieStatus as RequestHandler);
router.get('/set-test-cookie/:code', setTestCookie as RequestHandler);

export default router;
