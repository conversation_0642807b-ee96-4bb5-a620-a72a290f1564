import express, { RequestHand<PERSON> } from 'express';
import {
  createTip,
  getReceivedTips,
  getTipAnalytics,
  updateTipStatus,
  getTipJarSettings,
  getRecentTips,
} from '../controllers/tip.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

// Public routes
router.post('/', createTip as RequestHandler);
router.put('/:transactionId/status', updateTipStatus as RequestHandler);
router.get('/jar/:tipJarId', getTipJarSettings as RequestHandler);
router.get('/jar/:tipJarId/recent', getRecentTips as RequestHandler);

// Protected routes
router.get('/received', protect, getReceivedTips as RequestHandler);
router.get('/analytics', protect, getTipAnalytics as RequestHandler);

export default router;
