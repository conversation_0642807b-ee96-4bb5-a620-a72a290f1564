import express, { RequestHand<PERSON> } from 'express';
import {
  sendInvitation,
  acceptInvitation,
  rejectInvitation,
  getInvitations,
  cancelInvitation,
  getCollaborators,
  removeCollaborator,
  updateCollaboratorRole
} from '../controllers/invitation.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

// Protected routes (require authentication)
router.post('/', protect, sendInvitation as RequestHandler);
router.get('/', protect, getInvitations as RequestHandler);
router.delete('/:id', protect, cancelInvitation as RequestHandler);
router.get('/collaborators', protect, getCollaborators as RequestHandler);
router.delete('/collaborators/:userId', protect, removeCollaborator as RequestHandler);
router.put('/collaborators/:userId', protect, updateCollaboratorRole as RequestHandler);

// Public routes (for accepting/rejecting invitations)
router.post('/accept/:token', acceptInvitation as RequestHandler);
router.post('/reject/:token', rejectInvitation as RequestHandler);

export default router;
