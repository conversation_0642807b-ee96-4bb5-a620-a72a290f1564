import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import multer from 'multer';
import {
  uploadProfileImage,
  deleteProfileImage,
  uploadBunnyVideo,
  deleteBunnyVideo,
  upload<PERSON>ottie<PERSON>son,
  delete<PERSON><PERSON><PERSON><PERSON><PERSON>
} from '../controllers/media.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

// Configure multer for memory storage
const storage = multer.memoryStorage();

// Image upload configuration
const imageUpload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Video upload configuration for Bunny CDN
const videoUpload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit for Bunny CDN uploads
  },
  fileFilter: (req, file, cb) => {
    // Accept only video files
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'));
    }
  },
});

// JSON upload configuration for Lottie animations
const jsonUpload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit for JSON files
  },
  fileFilter: (req, file, cb) => {
    // Accept only JSON files
    if (file.mimetype === 'application/json' || file.originalname.endsWith('.json')) {
      cb(null, true);
    } else {
      cb(new Error('Only JSON files are allowed'));
    }
  },
});

router.post('/upload', protect, imageUpload.single('file'), uploadProfileImage as RequestHandler);
router.delete('/delete', protect, deleteProfileImage as RequestHandler);

// Bunny CDN routes
router.post('/upload-bunny-video', protect, videoUpload.single('file'), uploadBunnyVideo as RequestHandler);
router.delete('/delete-bunny-video', protect, deleteBunnyVideo as RequestHandler);

// Lottie JSON routes
router.post('/upload-lottie-json', protect, jsonUpload.single('file'), uploadLottieJson as RequestHandler);
router.delete('/delete-lottie-json', protect, deleteLottieJson as RequestHandler);

export default router;
