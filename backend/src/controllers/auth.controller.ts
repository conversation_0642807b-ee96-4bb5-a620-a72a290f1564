import { Request, Response } from 'express';
import User from '../models/User.model';
import generateToken from '../utils/generateToken';
import { generateDefaultAvatarUrl } from '../utils/defaultAssets';

// Cookie options for JWT token
const TOKEN_COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production', // Only use secure in production
  sameSite: process.env.NODE_ENV === 'production' ? 'none' as 'none' : 'lax' as 'lax', // Use 'none' for production, 'lax' for development
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  path: '/'
};

// Log cookie options
console.log('Token cookie options:', TOKEN_COOKIE_OPTIONS);

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
export const register = async (req: Request, res: Response) => {
  try {
    const { email, password, username } = req.body;

    // Check if user already exists
    const userExists = await User.findOne({ email });
    if (userExists) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Generate slug from username
    let slug = username.toLowerCase().replace(/\s+/g, '-');

    // Check if slug already exists
    const slugExists = await User.findOne({ slug });
    if (slugExists) {
      // Append a random number to make it unique
      slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
    }

    // Check for referral code in cookies
    const referralCode = req.cookies.referralCode;
    let referredBy = null;

    console.log('Registration process - All cookies:', req.cookies);
    console.log('Registration process - Checking for referral code in cookies:', referralCode);
    console.log('Registration process - Debug cookie:', req.cookies.referralCodeDebug);

    if (referralCode) {
      console.log('Registration process - Referral code found in cookies:', referralCode);

      // Check if it's a regular referral code
      let referrer = await User.findOne({ referralCode, referralEnabled: true });
      let isVipCode = false;

      console.log('Registration process - Checking standard referral code, found referrer:', referrer ? 'Yes' : 'No');

      // If not found, check if it's a VIP code
      if (!referrer) {
        referrer = await User.findOne({ referralVipCode: referralCode, referralEnabled: true });
        isVipCode = true;
        console.log('Registration process - Checking VIP referral code, found referrer:', referrer ? 'Yes' : 'No');

        // Increment VIP code usage count if valid
        if (referrer &&
            typeof referrer.referralVipUsageCount === 'number' &&
            typeof referrer.referralVipUsageLimit === 'number' &&
            referrer.referralVipUsageCount < referrer.referralVipUsageLimit) {
          // Use type assertion to ensure TypeScript knows these are numbers
          referrer.referralVipUsageCount = (referrer.referralVipUsageCount || 0) + 1;
          await referrer.save();
          console.log('Registration process - Incremented VIP code usage count to:', referrer.referralVipUsageCount);
        } else if (referrer) {
          // VIP code has reached its limit
          console.log('Registration process - VIP code has reached its usage limit');
          referrer = null;
        }
      }

      if (referrer) {
        // Use type assertion to handle the unknown type
        referredBy = referrer._id as any;
        console.log('Registration process - Setting referredBy to:', referredBy);

        // Clear the referral cookie after use
        res.cookie('referralCode', '', {
          httpOnly: true,
          expires: new Date(0),
          path: '/'
        });
        console.log('Registration process - Cleared referral cookie');
      } else {
        console.log('Registration process - No valid referrer found for code:', referralCode);
      }
    } else {
      console.log('Registration process - No referral code found in cookies');
    }

    // Generate default avatar URL
    const defaultAvatarUrl = generateDefaultAvatarUrl(username);

    // Generate a random pastel color for the banner background
    const generateRandomPastelColor = (): string => {
      // Generate random RGB values in the pastel range (180-240)
      const r = Math.floor(Math.random() * 60) + 180;
      const g = Math.floor(Math.random() * 60) + 180;
      const b = Math.floor(Math.random() * 60) + 180;

      // Convert to hex
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    };

    const randomBannerColor = generateRandomPastelColor();

    // Create user with referral info if available
    const userData: any = {
      email,
      password,
      username,
      slug,
      avatarUrl: defaultAvatarUrl,
      bannerUrl: '', // No default banner URL, we'll use the background color instead
      bannerColor: randomBannerColor, // Random pastel color
    };

    if (referredBy) {
      userData.referredBy = referredBy;
      console.log('Registration process - Added referredBy to userData:', referredBy);
    } else {
      console.log('Registration process - No referredBy to add to userData');
    }

    console.log('Registration process - Creating new user with data:', { ...userData, password: '[HIDDEN]' });
    const user = await User.create(userData);
    console.log('Registration process - User created with ID:', user._id);

    if (user) {
      // Use type assertion to handle the unknown type
      const userId = (user._id as any).toString();
      const token = generateToken(userId);

      // Set JWT as HTTP-only cookie
      res.cookie('jwt', token, TOKEN_COOKIE_OPTIONS);

      // Also return token in response for backward compatibility
      res.status(201).json({
        _id: user._id,
        email: user.email,
        username: user.username,
        slug: user.slug,
        bio: user.bio,
        avatarUrl: user.avatarUrl,
        avatarPosition: user.avatarPosition,
        avatarShape: user.avatarShape,
        bannerUrl: user.bannerUrl,
        bannerColor: user.bannerColor,
        pageBackgroundColor: user.pageBackgroundColor,
        cardBackgroundColor: user.cardBackgroundColor,
        fontColor: user.fontColor,
        fontFamily: user.fontFamily,
        backgroundImage: user.backgroundImage,
        backgroundPosition: user.backgroundPosition,
        backgroundRepeat: user.backgroundRepeat,
        backgroundSize: user.backgroundSize,
        backgroundAttachment: user.backgroundAttachment,
        theme: user.theme,
        directRedirectEnabled: user.directRedirectEnabled,
        directRedirectUrl: user.directRedirectUrl,
        verifiedProfile: user.verifiedProfile,
        verifiedProfileGold: user.verifiedProfileGold,
        countryProfiles: user.countryProfiles,
        token: token, // Include token in response for backward compatibility
      });
    } else {
      res.status(400).json({ message: 'Invalid user data' });
    }
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Login user & get token
// @route   POST /api/auth/login
// @access  Public
export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ email });

    // Check if user exists and password is correct
    if (user && (await user.comparePassword(password))) {
      // Use type assertion to handle the unknown type
      const userId = (user._id as any).toString();
      const token = generateToken(userId);

      // Get remember me option from request
      const rememberMe = req.body.rememberMe === true;

      // If rememberMe is true, use the full cookie age, otherwise set to session cookie (no maxAge)
      const cookieOptions = {
        ...TOKEN_COOKIE_OPTIONS,
        maxAge: rememberMe ? TOKEN_COOKIE_OPTIONS.maxAge : undefined
      };

      // Check for referral code in cookies if user doesn't already have a referrer
      if (!user.referredBy) {
        console.log('Login process - User has no referrer, checking for referral code in cookies');
        const referralCode = req.cookies.referralCode;
        console.log('Login process - Referral code from cookies:', referralCode);

        if (referralCode) {
          console.log('Login process - Referral code found in cookies:', referralCode);

          // Check if it's a regular referral code
          console.log('Login process - Checking if standard referral code is valid');
          let referrer = await User.findOne({ referralCode, referralEnabled: true });
          let isVipCode = false;

          if (referrer) {
            console.log('Login process - Found valid standard referral code for user:', referrer._id);
          } else {
            console.log('Login process - Standard referral code not found, checking VIP code');
          }

          // If not found, check if it's a VIP code
          if (!referrer) {
            referrer = await User.findOne({ referralVipCode: referralCode, referralEnabled: true });
            isVipCode = true;

            if (referrer) {
              console.log('Login process - Found valid VIP referral code for user:', referrer._id);

              // Increment VIP code usage count if valid
              if (typeof referrer.referralVipUsageCount === 'number' &&
                  typeof referrer.referralVipUsageLimit === 'number') {
                console.log('Login process - VIP code usage:', referrer.referralVipUsageCount, '/', referrer.referralVipUsageLimit);

                if (referrer.referralVipUsageCount < referrer.referralVipUsageLimit) {
                  // Use type assertion to ensure TypeScript knows these are numbers
                  referrer.referralVipUsageCount = (referrer.referralVipUsageCount || 0) + 1;
                  await referrer.save();
                  console.log('Login process - Incremented VIP code usage count to:', referrer.referralVipUsageCount);
                } else {
                  console.log('Login process - VIP code has reached its usage limit');
                  referrer = null;
                }
              }
            } else {
              console.log('Login process - VIP referral code not found');
            }
          }

          if (referrer &&
              referrer._id &&
              user._id &&
              (referrer._id as any).toString() !== (user._id as any).toString()) {
            console.log('Login process - Valid referrer found, updating user with referrer ID:', referrer._id);

            // Update user with referrer
            user.referredBy = referrer._id as any;
            await user.save();
            console.log('Login process - User updated with referrer');

            // Clear the referral cookie after use
            res.cookie('referralCode', '', {
              httpOnly: true,
              expires: new Date(0),
              path: '/'
            });
            console.log('Login process - Cleared referral cookie');
          } else if (referrer) {
            console.log('Login process - Referrer is the same as user or invalid, not updating');
          } else {
            console.log('Login process - No valid referrer found');
          }
        } else {
          console.log('Login process - No referral code found in cookies');
        }
      } else {
        console.log('Login process - User already has a referrer:', user.referredBy);
      }

      // Set JWT as HTTP-only cookie
      res.cookie('jwt', token, cookieOptions);

      // Also return token in response for backward compatibility
      res.json({
        _id: user._id,
        email: user.email,
        username: user.username,
        slug: user.slug,
        bio: user.bio,
        avatarUrl: user.avatarUrl,
        avatarPosition: user.avatarPosition,
        avatarShape: user.avatarShape,
        bannerUrl: user.bannerUrl,
        bannerColor: user.bannerColor,
        pageBackgroundColor: user.pageBackgroundColor,
        cardBackgroundColor: user.cardBackgroundColor,
        fontColor: user.fontColor,
        fontFamily: user.fontFamily,
        backgroundImage: user.backgroundImage,
        backgroundPosition: user.backgroundPosition,
        backgroundRepeat: user.backgroundRepeat,
        backgroundSize: user.backgroundSize,
        backgroundAttachment: user.backgroundAttachment,
        theme: user.theme,
        directRedirectEnabled: user.directRedirectEnabled,
        directRedirectUrl: user.directRedirectUrl,
        verifiedProfile: user.verifiedProfile,
        verifiedProfileGold: user.verifiedProfileGold,
        countryProfiles: user.countryProfiles,
        token: token, // Include token in response for backward compatibility
      });
    } else {
      res.status(401).json({ message: 'Invalid email or password' });
    }
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get user profile
// @route   GET /api/auth/profile
// @access  Private
export const getProfile = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const user = await User.findById(req.user._id).select('-password');
    if (user) {
      res.json(user);
    } else {
      res.status(404).json({ message: 'User not found' });
    }
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Logout user / clear cookie
// @route   POST /api/auth/logout
// @access  Public
export const logout = (req: Request, res: Response) => {
  // Clear the JWT cookie
  res.cookie('jwt', '', {
    httpOnly: true,
    expires: new Date(0), // Set expiration to epoch time (immediately expired)
    path: '/'
  });

  res.status(200).json({ message: 'Logged out successfully' });
};
