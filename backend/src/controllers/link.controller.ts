import { Request, Response } from 'express';
import Link from '../models/Link.model';
import ClickStat from '../models/ClickStat.model';

// @desc    Create a new link
// @route   POST /api/links
// @access  Private
export const createLink = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const {
      type = 'link',
      title,
      url,
      icon,
      badge,
      // Advanced options
      password,
      passwordEnabled,
      passwordExpiryDate,
      visibilityDates,
      // Announcement specific fields
      backgroundColor,
      backgroundGradient,
      useGradient,
      textColor,
      text,
      titleAlignment,
      textAlignment,
      buttonText,
      buttonBackgroundColor,
      buttonTextColor,
      // Shuffle cards specific fields
      shuffleCards,
      // MiniLead specific fields
      introText,
      fields,
      // Video specific fields
      videoUrl,
      thumbnailUrl,
      orientation,
      duration,
      videoId,
      // Embedded video specific fields
      embedType,
      youtubeCode,
      tikTokUrl,
      autoplay,
      muted,
      showControls,
      // Lottie animation specific fields
      lottieUrl,
      lottieJsonUrl,
      triggerEvent,
      triggerDelay,
      linkId
    } = req.body;

    const userId = req.user._id;

    // Validate required fields based on type
    if (type === 'link' && !url) {
      return res.status(400).json({ message: 'URL is required for link type' });
    }

    if (type === 'announcement' && !text) {
      return res.status(400).json({ message: 'Text is required for announcement type' });
    }

    if (type === 'shuffle' && (!shuffleCards || !shuffleCards.length)) {
      return res.status(400).json({ message: 'At least one shuffle card is required' });
    }

    if (type === 'minilead' && (!introText || !fields || !fields.length)) {
      return res.status(400).json({ message: 'Intro text and at least one field are required for minilead type' });
    }

    if (type === 'embedded') {
      if (embedType === 'youtube' && !youtubeCode) {
        return res.status(400).json({ message: 'YouTube video code is required for YouTube embedded video' });
      }
      if (embedType === 'tiktok' && !tikTokUrl) {
        return res.status(400).json({ message: 'TikTok URL is required for TikTok embedded video' });
      }
      if (!embedType && !youtubeCode) {
        return res.status(400).json({ message: 'Video code or URL is required for embedded video type' });
      }
    }

    // Get the highest order number for this user
    const highestOrderLink = await Link.findOne({ userId })
      .sort({ order: -1 })
      .limit(1);

    const order = highestOrderLink ? highestOrderLink.order + 1 : 0;

    const link = await Link.create({
      userId,
      type,
      title,
      url,
      icon,
      badge,
      order,
      // Advanced options
      password,
      passwordEnabled,
      passwordExpiryDate,
      visibilityDates,
      // Announcement specific fields
      backgroundColor,
      backgroundGradient,
      useGradient,
      textColor,
      text,
      titleAlignment,
      textAlignment,
      buttonText,
      buttonBackgroundColor,
      buttonTextColor,
      // Shuffle cards specific fields
      shuffleCards,
      // MiniLead specific fields
      introText,
      fields,
      // Video specific fields
      videoUrl,
      thumbnailUrl,
      orientation,
      duration,
      videoId,
      // Embedded video specific fields
      embedType,
      youtubeCode,
      tikTokUrl,
      autoplay,
      muted,
      showControls,
      // Lottie animation specific fields
      lottieUrl,
      lottieJsonUrl,
      triggerEvent,
      triggerDelay,
      linkId,
      // Digital Product specific fields
      productType,
      price,
      currency,
      description,
      downloadUrl,
      previewImages,
      fileSize,
      fileFormat,
      digitalProductId,
      // Tip Jar specific fields
      tipAmounts,
      customAmountEnabled,
      tipMessage,
      thankYouMessage,
      minTipAmount,
      maxTipAmount
    });

    res.status(201).json(link);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get all links for a user
// @route   GET /api/links
// @access  Private
export const getLinks = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const { archived } = req.query;
    const query: any = { userId: req.user._id };

    // Filter by archived status if specified
    if (archived !== undefined) {
      query.archived = archived === 'true';
    }

    const links = await Link.find(query).sort({ order: 1 });
    res.json(links);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get a single link
// @route   GET /api/links/:id
// @access  Private
export const getLinkById = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const link = await Link.findById(req.params.id);

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Check if the link belongs to the user
    if (link.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    res.json(link);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update a link
// @route   PUT /api/links/:id
// @access  Private
export const updateLink = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const {
      title,
      url,
      icon,
      badge,
      enabled,
      // Advanced options
      password,
      passwordEnabled,
      passwordExpiryDate,
      visibilityDates,
      // Announcement specific fields
      backgroundColor,
      backgroundGradient,
      useGradient,
      textColor,
      text,
      titleAlignment,
      textAlignment,
      buttonText,
      buttonBackgroundColor,
      buttonTextColor,
      // Shuffle cards specific fields
      shuffleCards,
      // MiniLead specific fields
      introText,
      fields,
      // Video specific fields
      videoUrl,
      thumbnailUrl,
      orientation,
      duration,
      videoId,
      // Embedded video specific fields
      embedType,
      youtubeCode,
      tikTokUrl,
      autoplay,
      muted,
      showControls
    } = req.body;

    const link = await Link.findById(req.params.id);

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Check if the link belongs to the user
    if (link.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    // Update common fields
    if (title) link.title = title;
    if (url !== undefined) link.url = url;

    // Only update icon if it's provided (including empty string)
    if (icon !== undefined) {
      link.icon = icon;
    }

    // Handle badge data - can be null to remove badge
    if (badge !== undefined) {
      link.badge = badge;
    }

    // Update enabled state if provided
    if (enabled !== undefined) {
      link.enabled = enabled;
    }

    // Update advanced options if provided
    if (password !== undefined) {
      link.password = password;
    }

    if (passwordEnabled !== undefined) {
      link.passwordEnabled = passwordEnabled;
    }

    if (passwordExpiryDate !== undefined) {
      link.passwordExpiryDate = passwordExpiryDate;
    }

    if (visibilityDates !== undefined) {
      link.visibilityDates = visibilityDates;
    }

    // Update type-specific fields
    if (link.type === 'announcement') {
      if (backgroundColor !== undefined) link.backgroundColor = backgroundColor;
      if (backgroundGradient !== undefined) link.backgroundGradient = backgroundGradient;
      if (useGradient !== undefined) link.useGradient = useGradient;
      if (textColor !== undefined) link.textColor = textColor;
      if (text !== undefined) link.text = text;
      if (titleAlignment !== undefined) link.titleAlignment = titleAlignment;
      if (textAlignment !== undefined) link.textAlignment = textAlignment;
      if (buttonText !== undefined) link.buttonText = buttonText;
      if (buttonBackgroundColor !== undefined) link.buttonBackgroundColor = buttonBackgroundColor;
      if (buttonTextColor !== undefined) link.buttonTextColor = buttonTextColor;
    }

    if (link.type === 'shuffle') {
      if (shuffleCards !== undefined) {
        // Validate shuffle cards
        if (shuffleCards.length > 3) {
          return res.status(400).json({ message: 'Maximum 3 shuffle cards allowed' });
        }
        link.shuffleCards = shuffleCards;
      }
    }

    if (link.type === 'minilead') {
      if (introText !== undefined) link.introText = introText;
      if (fields !== undefined) link.fields = fields;
    }

    if (link.type === 'video') {
      console.log('Updating video component:', {
        videoUrl,
        thumbnailUrl,
        orientation,
        duration,
        videoId,
        embedType,
        currentEmbedType: link.embedType
      });

      if (videoUrl !== undefined) link.videoUrl = videoUrl;
      if (thumbnailUrl !== undefined) link.thumbnailUrl = thumbnailUrl;
      if (orientation !== undefined) link.orientation = orientation;
      if (duration !== undefined) link.duration = duration;
      if (videoId !== undefined) link.videoId = videoId;
      if (embedType !== undefined) {
        console.log(`Setting embedType from ${link.embedType} to ${embedType}`);
        link.embedType = embedType;
      }
    }

    if (link.type === 'embedded') {
      if (embedType !== undefined) link.embedType = embedType;
      if (youtubeCode !== undefined) link.youtubeCode = youtubeCode;
      if (tikTokUrl !== undefined) link.tikTokUrl = tikTokUrl;
      if (autoplay !== undefined) link.autoplay = autoplay;
      if (muted !== undefined) link.muted = muted;
      if (showControls !== undefined) link.showControls = showControls;
    }

    // Handle Lottie animation specific fields
    if (link.type === 'lottie') {
      const { lottieUrl, lottieJsonUrl, triggerEvent, triggerDelay, linkId } = req.body;
      if (lottieUrl !== undefined) link.lottieUrl = lottieUrl;
      if (lottieJsonUrl !== undefined) link.lottieJsonUrl = lottieJsonUrl;
      if (triggerEvent !== undefined) link.triggerEvent = triggerEvent;
      if (triggerDelay !== undefined) link.triggerDelay = triggerDelay;
      if (linkId !== undefined) link.linkId = linkId;
    }

    const updatedLink = await link.save();
    res.json(updatedLink);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Delete a link
// @route   DELETE /api/links/:id
// @access  Private
export const deleteLink = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const link = await Link.findById(req.params.id);

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Check if the link belongs to the user
    if (link.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    await link.deleteOne();

    // Delete all click stats for this link
    await ClickStat.deleteMany({ linkId: link._id });

    res.json({ message: 'Link removed' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update link order
// @route   PUT /api/links/reorder
// @access  Private
export const updateLinkOrder = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const { links } = req.body;
    const userId = req.user._id;

    if (!Array.isArray(links)) {
      return res.status(400).json({ message: 'Links must be an array' });
    }

    // Validate that all links belong to the user
    for (const link of links) {
      // Make sure we have a valid ID
      if (!link._id) {
        return res.status(400).json({ message: 'Each link must have an _id' });
      }

      try {
        const existingLink = await Link.findById(link._id);
        if (!existingLink) {
          return res.status(404).json({ message: `Link with id ${link._id} not found` });
        }
        if (existingLink.userId.toString() !== userId.toString()) {
          return res.status(403).json({ message: 'Not authorized' });
        }
      } catch (err) {
        return res.status(400).json({ message: `Invalid link ID: ${link._id}` });
      }
    }

    // Update the order of each link
    const updatePromises = links.map((link: { _id: string; order: number }) => {
      return Link.findByIdAndUpdate(
        link._id,
        { order: link.order },
        { new: true }
      );
    });

    await Promise.all(updatePromises);

    const updatedLinks = await Link.find({ userId }).sort({ order: 1 });
    res.json(updatedLinks);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Track a link click
// @route   POST /api/links/:id/click
// @access  Public
export const trackLinkClick = async (req: Request, res: Response) => {
  try {
    const link = await Link.findById(req.params.id);
    const { password } = req.body;

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Check if link is visible based on date range
    if (link.visibilityDates?.enabled) {
      const now = new Date();
      const startDate = link.visibilityDates.startDate ? new Date(link.visibilityDates.startDate) : null;
      const endDate = link.visibilityDates.endDate ? new Date(link.visibilityDates.endDate) : null;

      if ((startDate && now < startDate) || (endDate && now > endDate)) {
        return res.status(404).json({ message: 'Link not available' });
      }
    }

    // Check if password protection is enabled
    if (link.passwordEnabled) {
      // Check if password expiry date has passed
      if (link.passwordExpiryDate) {
        const now = new Date();
        const expiryDate = new Date(link.passwordExpiryDate);

        if (now > expiryDate) {
          // Password protection has expired, proceed normally
        } else {
          // Password protection is still active
          if (!password) {
            // No password provided, inform client that password is required
            return res.json({
              success: false,
              passwordRequired: true,
              url: link.url
            });
          }

          // Check if password is correct
          if (password !== link.password) {
            return res.json({
              success: false,
              passwordRequired: true,
              passwordCorrect: false,
              url: link.url
            });
          }
          // Password is correct, proceed
        }
      } else {
        // No expiry date set, check password
        if (!password) {
          // No password provided, inform client that password is required
          return res.json({
            success: false,
            passwordRequired: true,
            url: link.url
          });
        }

        // Check if password is correct
        if (password !== link.password) {
          return res.json({
            success: false,
            passwordRequired: true,
            passwordCorrect: false,
            url: link.url
          });
        }
        // Password is correct, proceed
      }
    }

    // Increment click count
    link.clickCount += 1;
    await link.save();

    // Record click statistics
    await ClickStat.create({
      linkId: link._id,
      userId: link.userId,
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    });

    res.json({ success: true, url: link.url });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Archive a link
// @route   PUT /api/links/:id/archive
// @access  Private
export const archiveLink = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const link = await Link.findById(req.params.id);

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Check if the link belongs to the user
    if (link.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    link.archived = true;
    const updatedLink = await link.save();
    res.json(updatedLink);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Unarchive a link
// @route   PUT /api/links/:id/unarchive
// @access  Private
export const unarchiveLink = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const link = await Link.findById(req.params.id);

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Check if the link belongs to the user
    if (link.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    link.archived = false;
    const updatedLink = await link.save();
    res.json(updatedLink);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};
