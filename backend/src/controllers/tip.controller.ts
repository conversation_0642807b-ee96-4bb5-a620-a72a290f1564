import { Request, Response } from 'express';
import TipTransaction from '../models/TipTransaction.model';
import Link from '../models/Link.model';
import User from '../models/User.model';
import crypto from 'crypto';

// @desc    Create a new tip transaction
// @route   POST /api/tips
// @access  Public
export const createTip = async (req: Request, res: Response) => {
  try {
    const {
      tipJarId,
      amount,
      currency,
      paymentProvider,
      tipperEmail,
      tipperName,
      tipperMessage,
      isAnonymous,
      giftType,
      giftEmoji,
      giftMessage,
    } = req.body;

    // Validate tip jar exists
    const tipJar = await Link.findById(tipJarId);
    if (!tipJar || tipJar.type !== 'tipjar') {
      return res.status(404).json({ message: 'Tip jar not found' });
    }

    // Validate amount within limits
    const minAmount = tipJar.minTipAmount || 1;
    const maxAmount = tipJar.maxTipAmount || 1000;

    if (amount < minAmount || amount > maxAmount) {
      return res.status(400).json({ 
        message: `Tip amount must be between ${minAmount} and ${maxAmount} ${currency}` 
      });
    }

    // Generate unique transaction ID
    const transactionId = crypto.randomBytes(16).toString('hex');

    const tipTransaction = await TipTransaction.create({
      recipientId: tipJar.userId,
      tipJarId,
      transactionId,
      paymentProvider,
      amount,
      currency: currency || 'USD',
      tipperEmail: isAnonymous ? undefined : tipperEmail,
      tipperName: isAnonymous ? undefined : tipperName,
      tipperMessage,
      isAnonymous: isAnonymous || false,
      giftType: giftType || 'heart',
      giftEmoji: giftEmoji || '❤️',
      giftMessage,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.status(201).json({
      transactionId: tipTransaction.transactionId,
      amount: tipTransaction.amount,
      currency: tipTransaction.currency,
      message: 'Tip transaction created successfully',
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get tips received by user
// @route   GET /api/tips/received
// @access  Private
export const getReceivedTips = async (req: Request, res: Response) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 20, status, startDate, endDate } = req.query;

    const query: any = { recipientId: userId };

    if (status) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const tips = await TipTransaction.find(query)
      .sort({ createdAt: -1 })
      .limit(Number(limit) * 1)
      .skip((Number(page) - 1) * Number(limit))
      .populate('tipJarId', 'title');

    const total = await TipTransaction.countDocuments(query);

    // Calculate total earnings
    const totalEarnings = await TipTransaction.aggregate([
      { $match: { recipientId: userId, status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    res.json({
      tips,
      totalPages: Math.ceil(total / Number(limit)),
      currentPage: Number(page),
      total,
      totalEarnings: totalEarnings[0]?.total || 0,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get tip analytics for user
// @route   GET /api/tips/analytics
// @access  Private
export const getTipAnalytics = async (req: Request, res: Response) => {
  try {
    const userId = req.user._id;
    const { period = '30d' } = req.query;

    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    const analytics = await TipTransaction.aggregate([
      {
        $match: {
          recipientId: userId,
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalTips: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          averageTip: { $avg: '$amount' },
          uniqueTippers: { $addToSet: '$tipperEmail' }
        }
      }
    ]);

    // Get tips by gift type
    const tipsByGiftType = await TipTransaction.aggregate([
      {
        $match: {
          recipientId: userId,
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$giftType',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Get daily tips for chart
    const dailyTips = await TipTransaction.aggregate([
      {
        $match: {
          recipientId: userId,
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 },
          amount: { $sum: '$amount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    const result = {
      summary: analytics[0] || {
        totalTips: 0,
        totalAmount: 0,
        averageTip: 0,
        uniqueTippers: []
      },
      tipsByGiftType,
      dailyTips,
      uniqueTippersCount: analytics[0]?.uniqueTippers?.length || 0,
    };

    res.json(result);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update tip transaction status (webhook)
// @route   PUT /api/tips/:transactionId/status
// @access  Public (webhook)
export const updateTipStatus = async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const { status, paymentIntentId, paymentMethodId } = req.body;

    const tip = await TipTransaction.findOne({ transactionId });

    if (!tip) {
      return res.status(404).json({ message: 'Tip transaction not found' });
    }

    tip.status = status;
    if (paymentIntentId) tip.paymentIntentId = paymentIntentId;
    if (paymentMethodId) tip.paymentMethodId = paymentMethodId;

    await tip.save();

    res.json({ message: 'Tip status updated successfully' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get tip jar settings
// @route   GET /api/tips/jar/:tipJarId
// @access  Public
export const getTipJarSettings = async (req: Request, res: Response) => {
  try {
    const { tipJarId } = req.params;

    const tipJar = await Link.findById(tipJarId)
      .populate('userId', 'username slug tipJarEnabled');

    if (!tipJar || tipJar.type !== 'tipjar') {
      return res.status(404).json({ message: 'Tip jar not found' });
    }

    const user = tipJar.userId as any;
    if (!user.tipJarEnabled) {
      return res.status(403).json({ message: 'Tip jar is disabled' });
    }

    const settings = {
      title: tipJar.title,
      tipMessage: tipJar.tipMessage,
      thankYouMessage: tipJar.thankYouMessage,
      tipAmounts: tipJar.tipAmounts,
      customAmountEnabled: tipJar.customAmountEnabled,
      minTipAmount: tipJar.minTipAmount,
      maxTipAmount: tipJar.maxTipAmount,
      currency: user.defaultCurrency || 'USD',
      recipientName: user.username,
    };

    res.json(settings);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get recent tips for tip jar (public display)
// @route   GET /api/tips/jar/:tipJarId/recent
// @access  Public
export const getRecentTips = async (req: Request, res: Response) => {
  try {
    const { tipJarId } = req.params;
    const { limit = 5 } = req.query;

    const recentTips = await TipTransaction.find({
      tipJarId,
      status: 'completed',
      isAnonymous: false
    })
    .select('amount currency tipperName giftType giftEmoji giftMessage createdAt')
    .sort({ createdAt: -1 })
    .limit(Number(limit));

    res.json(recentTips);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};
