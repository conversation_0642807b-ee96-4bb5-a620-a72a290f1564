import { Request, Response } from 'express';
import Lead from '../models/Lead.model';
import Link from '../models/Link.model';
import mongoose from 'mongoose';

// @desc    Submit a new lead
// @route   POST /api/leads
// @access  Public
export const submitLead = async (req: Request, res: Response) => {
  try {
    const { linkId, formData } = req.body;

    if (!linkId || !formData) {
      return res.status(400).json({ message: 'Link ID and form data are required' });
    }

    // Find the link to get the title and userId
    const link = await Link.findById(linkId);

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    if (link.type !== 'minilead') {
      return res.status(400).json({ message: 'This link is not a mini lead form' });
    }

    // Create the lead
    const lead = await Lead.create({
      linkId,
      userId: link.userId,
      title: link.title,
      formData,
    });

    res.status(201).json({ success: true, lead });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get all leads for a user
// @route   GET /api/leads
// @access  Private
export const getLeads = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const userId = req.user._id;
    const { linkId } = req.query;

    console.log('Backend getLeads - received linkId:', linkId);
    console.log('Backend getLeads - linkId type:', typeof linkId);

    // Build query
    const query: any = { userId };

    // If linkId is provided, filter by linkId
    if (linkId) {
      try {
        // Ensure linkId is a valid ObjectId
        if (mongoose.Types.ObjectId.isValid(linkId as string)) {
          query.linkId = new mongoose.Types.ObjectId(linkId as string);
          console.log('Backend getLeads - using ObjectId for linkId:', query.linkId);
        } else {
          query.linkId = linkId;
          console.log('Backend getLeads - using string for linkId:', query.linkId);
        }
      } catch (err) {
        console.error('Error converting linkId to ObjectId:', err);
        query.linkId = linkId;
      }
    }

    console.log('Backend getLeads - final query:', JSON.stringify(query));

    // Get leads with link details
    const leads = await Lead.find(query)
      .sort({ createdAt: -1 })
      .populate('linkId', 'title type');

    console.log('Backend getLeads - found leads count:', leads.length);

    // Log the first few leads for debugging
    if (leads.length > 0) {
      console.log('Backend getLeads - first lead:', JSON.stringify(leads[0], null, 2));

      // Check if we're getting the expected structure after population
      const firstLead = leads[0];
      if (firstLead.linkId && typeof firstLead.linkId === 'object') {
        console.log('Backend getLeads - linkId is populated as an object');
        if ('_id' in firstLead.linkId) {
          console.log('Backend getLeads - linkId._id exists:', firstLead.linkId._id);
        }
      } else {
        console.log('Backend getLeads - linkId is not populated as expected:', typeof firstLead.linkId);
      }
    }

    res.json(leads);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get lead statistics
// @route   GET /api/leads/stats
// @access  Private
export const getLeadStats = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const userId = req.user._id;

    // Get total leads count
    const totalLeads = await Lead.countDocuments({ userId });

    // Get leads per form
    const leadsPerForm = await Lead.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId.toString()),
        },
      },
      {
        $lookup: {
          from: 'links',
          localField: 'linkId',
          foreignField: '_id',
          as: 'linkDetails'
        }
      },
      {
        $unwind: {
          path: '$linkDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: '$linkId',
          title: { $first: '$linkDetails.title' },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    console.log('Leads per form:', JSON.stringify(leadsPerForm, null, 2));

    // Get leads per day for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const leadsPerDay = await Lead.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId.toString()),
          createdAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            day: { $dayOfMonth: '$createdAt' },
            month: { $month: '$createdAt' },
            year: { $year: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: {
          '_id.year': 1,
          '_id.month': 1,
          '_id.day': 1,
        },
      },
    ]);

    // Format the data for the frontend
    const formattedLeadsPerDay = leadsPerDay.map(item => {
      const date = new Date(
        item._id.year,
        item._id.month - 1,
        item._id.day
      );
      return {
        date: date.toISOString().split('T')[0],
        count: item.count,
      };
    });

    res.json({
      totalLeads,
      leadsPerForm,
      leadsPerDay: formattedLeadsPerDay,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Delete a lead
// @route   DELETE /api/leads/:id
// @access  Private
export const deleteLead = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const lead = await Lead.findById(req.params.id);

    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    // Check if the lead belongs to the user
    if (lead.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    await lead.deleteOne();
    res.json({ message: 'Lead removed' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};
