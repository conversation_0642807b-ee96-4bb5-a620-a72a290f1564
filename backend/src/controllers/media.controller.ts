import { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import User from '../models/User.model';
import { getThumbnailPath } from '../utils/videoProcessor';
import { uploadToBunnyCDN, deleteFromBunnyCDN } from '../services/bunnycdn.service';

// Ensure media directories exist
const mediaDir = path.join(__dirname, '../../media');
const usersMediaDir = path.join(mediaDir, 'users');

// Create directories if they don't exist
if (!fs.existsSync(mediaDir)) {
  fs.mkdirSync(mediaDir, { recursive: true });
}
if (!fs.existsSync(usersMediaDir)) {
  fs.mkdirSync(usersMediaDir, { recursive: true });
}

/**
 * Create a sanitized directory name for user media
 * @param username User's username
 * @returns Sanitized directory name
 */
const createUserMediaDir = (username: string): string => {
  // Sanitize username (remove special chars, lowercase)
  const sanitized = username.toLowerCase().replace(/[^a-z0-9]/g, '');
  return sanitized;
};

// @desc    Upload profile image (avatar or banner)
// @route   POST /api/media/upload
// @access  Private
export const uploadProfileImage = async (req: Request, res: Response) => {
  try {
    console.log('uploadProfileImage called with type:', req.body.type);

    if (!req.file) {
      console.error('No file uploaded');
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('File details:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
    });

    const { type } = req.body;
    if (!type || (type !== 'avatar' && type !== 'banner' && type !== 'shuffle' && type !== 'video-thumbnail' && type !== 'background')) {
      console.error('Invalid image type:', type);
      return res.status(400).json({ message: 'Invalid image type' });
    }

    // Get user from database
    const user = await User.findById(req.user._id);
    if (!user) {
      console.error('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    const username = user.username;
    const userDir = createUserMediaDir(username);
    const userMediaDir = path.join(usersMediaDir, userDir);

    console.log('User directory:', userMediaDir);

    // Create user directory if it doesn't exist
    if (!fs.existsSync(userMediaDir)) {
      console.log('Creating user directory');
      fs.mkdirSync(userMediaDir, { recursive: true });
    }

    // Generate unique filename
    // Ensure we use .webp extension for WebP images
    let fileExtension = path.extname(req.file.originalname).toLowerCase();

    // If the mimetype is webp or the filename already ends with .webp, use .webp extension
    if (req.file.mimetype === 'image/webp' || fileExtension === '.webp') {
      fileExtension = '.webp';
    }

    // Generate a filename based on the type
    let fileName;
    if (type === 'video-thumbnail') {
      // Extract videoId from the original filename if it exists
      const match = req.file.originalname.match(/video_thumbnail_([0-9a-f-]+)/i);
      if (match && match[1]) {
        const videoId = match[1];
        console.log('Extracted videoId from thumbnail filename:', videoId);
        fileName = `video_thumbnail_${videoId}${fileExtension}`;
      } else {
        // If no videoId in filename, use a new UUID
        fileName = `video_thumbnail_${uuidv4()}${fileExtension}`;
      }
    } else {
      fileName = `${type}_${uuidv4()}${fileExtension}`;
    }

    const filePath = path.join(userMediaDir, fileName);

    console.log('Saving file to:', filePath);

    // Write file to disk
    fs.writeFileSync(filePath, req.file.buffer);
    console.log('File saved successfully');

    // Update user profile with new image URL
    const imageUrl = `/media/users/${userDir}/${fileName}`;
    console.log('Generated image URL:', imageUrl);

    // Update user in database with new image URL
    if (type === 'avatar') {
      user.avatarUrl = imageUrl;
      await user.save();
      console.log('Updated user avatar URL');
    } else if (type === 'banner') {
      user.bannerUrl = imageUrl;
      await user.save();
      console.log('Updated user banner URL');
    } else if (type === 'background') {
      user.backgroundImage = imageUrl;
      await user.save();
      console.log('Updated user background image URL');
    } else if (type === 'video-thumbnail') {
      console.log('Video thumbnail uploaded, URL will be stored with the video component');
    }
    // For shuffle cards and video thumbnails, we don't need to update the user model
    // The image URL will be stored in the link document by the client

    res.status(200).json({ imageUrl });
  } catch (error: any) {
    console.error('Upload error:', error);
    res.status(500).json({ message: error.message });
  }
};

// @desc    Delete profile image
// @route   DELETE /api/media/delete
// @access  Private
export const deleteProfileImage = async (req: Request, res: Response) => {
  try {
    const { imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({ message: 'Image URL is required' });
    }

    // Get user from database
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if the image belongs to the user
    const isUserImage = (user.avatarUrl && user.avatarUrl === imageUrl) ||
                        (user.bannerUrl && user.bannerUrl === imageUrl) ||
                        (user.backgroundImage && user.backgroundImage === imageUrl);

    // For shuffle card images, we need to check if the image path starts with the user's directory
    const userDir = createUserMediaDir(user.username);
    const isShuffleImage = imageUrl.includes(`/media/users/${userDir}/`);

    if (isUserImage || isShuffleImage) {
      // Get the file path
      const filePath = path.join(__dirname, '../..', imageUrl);

      // Check if file exists
      if (fs.existsSync(filePath)) {
        // Delete the file
        fs.unlinkSync(filePath);
      }

      // Update user in database if it's an avatar or banner
      if (isUserImage) {
        if (user.avatarUrl === imageUrl) {
          user.avatarUrl = '';
        } else if (user.bannerUrl === imageUrl) {
          user.bannerUrl = '';
        } else if (user.backgroundImage === imageUrl) {
          user.backgroundImage = '';
        }
        await user.save();
      }
      // For shuffle images, we don't need to update the user model
      // The client will handle updating the link document

      res.status(200).json({ success: true });
    } else {
      res.status(403).json({ message: 'You do not have permission to delete this image' });
    }
  } catch (error: any) {
    console.error('Delete error:', error);
    res.status(500).json({ message: error.message });
  }
};

// @desc    Upload video to Bunny CDN
// @route   POST /api/media/upload-bunny-video
// @access  Private
export const uploadBunnyVideo = async (req: Request, res: Response) => {
  try {
    console.log('uploadBunnyVideo called');

    if (!req.file) {
      console.error('No video file uploaded');
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('Video file details:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
    });

    // Get user from database
    const user = await User.findById(req.user._id);
    if (!user) {
      console.error('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    // Get video title from request or use default
    const title = req.body.title || `Video by ${user.username}`;

    console.log('Uploading video to Bunny CDN...');
    // Upload video to Bunny CDN
    const { videoUrl, videoId, thumbnailUrl } = await uploadToBunnyCDN(
      req.file.buffer,
      title
    );
    console.log('Video uploaded to Bunny CDN successfully:', { videoUrl, videoId, thumbnailUrl });

    // Return the video details
    res.status(200).json({
      videoUrl,
      videoId,
      thumbnailUrl,
      // For compatibility with existing code
      duration: 0
    });
  } catch (error: any) {
    console.error('Bunny CDN video upload error:', error);
    res.status(500).json({ message: error.message || 'Failed to upload video to Bunny CDN' });
  }
};

// @desc    Delete video from Bunny CDN
// @route   DELETE /api/media/delete-bunny-video
// @access  Private
export const deleteBunnyVideo = async (req: Request, res: Response) => {
  try {
    const { videoId } = req.body;

    if (!videoId) {
      return res.status(400).json({ message: 'Video ID is required' });
    }

    console.log('Deleting video from Bunny CDN:', videoId);
    // Delete video from Bunny CDN
    await deleteFromBunnyCDN(videoId);
    console.log('Video deleted from Bunny CDN successfully');

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error('Bunny CDN video delete error:', error);
    res.status(500).json({ message: error.message || 'Failed to delete video from Bunny CDN' });
  }
};

// @desc    Upload Lottie JSON file
// @route   POST /api/media/upload-lottie-json
// @access  Private
export const uploadLottieJson = async (req: Request, res: Response) => {
  try {
    console.log('uploadLottieJson called');

    if (!req.file) {
      console.error('No JSON file uploaded');
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('JSON file details:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
    });

    // Validate JSON content
    try {
      const jsonContent = JSON.parse(req.file.buffer.toString('utf8'));

      // Basic validation for Lottie JSON structure
      if (!jsonContent.v || !jsonContent.fr || !jsonContent.layers) {
        return res.status(400).json({
          message: 'Invalid Lottie JSON format. Missing required properties (v, fr, layers)'
        });
      }
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      return res.status(400).json({ message: 'Invalid JSON file format' });
    }

    // Get user from database
    const user = await User.findById(req.user._id);
    if (!user) {
      console.error('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    // Create user-specific directory
    const userDir = path.join(usersMediaDir, (user._id as string).toString());
    if (!fs.existsSync(userDir)) {
      fs.mkdirSync(userDir, { recursive: true });
    }

    // Create lottie subdirectory
    const lottieDir = path.join(userDir, 'lottie');
    if (!fs.existsSync(lottieDir)) {
      fs.mkdirSync(lottieDir, { recursive: true });
    }

    // Generate unique filename
    const fileExtension = '.json';
    const fileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(lottieDir, fileName);

    // Save the file
    fs.writeFileSync(filePath, req.file.buffer);
    console.log('Lottie JSON file saved to:', filePath);

    // Return the relative URL
    const jsonUrl = `/media/users/${user._id}/lottie/${fileName}`;
    console.log('Lottie JSON URL:', jsonUrl);

    res.status(200).json({
      jsonUrl,
      fileName,
      size: req.file.size
    });
  } catch (error: any) {
    console.error('Lottie JSON upload error:', error);
    res.status(500).json({ message: error.message || 'Failed to upload Lottie JSON file' });
  }
};

// @desc    Delete Lottie JSON file
// @route   DELETE /api/media/delete-lottie-json
// @access  Private
export const deleteLottieJson = async (req: Request, res: Response) => {
  try {
    const { jsonUrl } = req.body;

    if (!jsonUrl) {
      return res.status(400).json({ message: 'JSON URL is required' });
    }

    // Get user from database
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Extract filename from URL
    const urlParts = jsonUrl.split('/');
    const fileName = urlParts[urlParts.length - 1];

    // Construct file path
    const filePath = path.join(usersMediaDir, (user._id as string).toString(), 'lottie', fileName);

    // Check if file exists and delete it
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log('Lottie JSON file deleted:', filePath);
    }

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error('Lottie JSON deletion error:', error);
    res.status(500).json({ message: error.message || 'Failed to delete Lottie JSON file' });
  }
};
