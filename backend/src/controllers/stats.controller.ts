import { Request, Response } from 'express';
import ClickStat from '../models/ClickStat.model';
import Link from '../models/Link.model';
import mongoose from 'mongoose';

// @desc    Get click statistics for all user links
// @route   GET /api/stats
// @access  Private
export const getStats = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const userId = req.user._id;

    // Get all links for the user
    const links = await Link.find({ userId });
    const linkIds = links.map(link => link._id);

    // Get total clicks per link
    const totalClicksPerLink = await ClickStat.aggregate([
      {
        $match: {
          linkId: { $in: linkIds },
          userId: new mongoose.Types.ObjectId(userId.toString()),
        },
      },
      {
        $group: {
          _id: '$linkId',
          totalClicks: { $sum: 1 },
        },
      },
    ]);

    // Get clicks per day for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const clicksPerDay = await ClickStat.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId.toString()),
          timestamp: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            day: { $dayOfMonth: '$timestamp' },
            month: { $month: '$timestamp' },
            year: { $year: '$timestamp' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: {
          '_id.year': 1,
          '_id.month': 1,
          '_id.day': 1,
        },
      },
    ]);

    // Format the data for the frontend
    const formattedClicksPerDay = clicksPerDay.map(item => {
      const date = new Date(
        item._id.year,
        item._id.month - 1,
        item._id.day
      );
      return {
        date: date.toISOString().split('T')[0],
        count: item.count,
      };
    });

    // Get total clicks
    const totalClicks = totalClicksPerLink.reduce(
      (sum, item) => sum + item.totalClicks,
      0
    );

    // Combine link data with click stats
    const linksWithStats = links.map(link => {
      const stats = totalClicksPerLink.find(
        item => item._id && link._id && item._id.toString() === link._id.toString()
      );
      return {
        _id: link._id,
        title: link.title,
        url: link.url,
        order: link.order,
        clickCount: link.clickCount,
        totalClicks: stats ? stats.totalClicks : 0,
      };
    });

    res.json({
      totalClicks,
      linksWithStats,
      clicksPerDay: formattedClicksPerDay,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get click statistics for a specific link
// @route   GET /api/stats/:linkId
// @access  Private
export const getLinkStats = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const { linkId } = req.params;
    const userId = req.user._id;

    // Check if the link exists and belongs to the user
    const link = await Link.findOne({
      _id: linkId,
      userId,
    });

    if (!link) {
      return res.status(404).json({ message: 'Link not found' });
    }

    // Get clicks per day for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const clicksPerDay = await ClickStat.aggregate([
      {
        $match: {
          linkId: new mongoose.Types.ObjectId(linkId),
          userId: new mongoose.Types.ObjectId(userId.toString()),
          timestamp: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            day: { $dayOfMonth: '$timestamp' },
            month: { $month: '$timestamp' },
            year: { $year: '$timestamp' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: {
          '_id.year': 1,
          '_id.month': 1,
          '_id.day': 1,
        },
      },
    ]);

    // Format the data for the frontend
    const formattedClicksPerDay = clicksPerDay.map(item => {
      const date = new Date(
        item._id.year,
        item._id.month - 1,
        item._id.day
      );
      return {
        date: date.toISOString().split('T')[0],
        count: item.count,
      };
    });

    // Get total clicks
    const totalClicks = await ClickStat.countDocuments({
      linkId,
      userId,
    });

    res.json({
      link,
      totalClicks,
      clicksPerDay: formattedClicksPerDay,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};
