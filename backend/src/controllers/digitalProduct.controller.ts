import { Request, Response } from 'express';
import DigitalProduct from '../models/DigitalProduct.model';
import Purchase from '../models/Purchase.model';
import { generateSlug } from '../utils/slugGenerator';
import crypto from 'crypto';

// @desc    Create a new digital product
// @route   POST /api/digital-products
// @access  Private
export const createDigitalProduct = async (req: Request, res: Response) => {
  try {
    const {
      title,
      description,
      productType,
      price,
      currency,
      downloadUrl,
      previewImages,
      fileSize,
      fileFormat,
      tags,
      metaDescription,
      originalFileName,
      requiresAccount,
      maxDownloads,
      downloadExpiryDays,
    } = req.body;

    const userId = req.user._id;

    // Generate unique slug
    const baseSlug = generateSlug(title);
    let slug = baseSlug;
    let counter = 1;

    while (await DigitalProduct.findOne({ userId, slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Generate file hash for security
    const fileHash = crypto.randomBytes(32).toString('hex');

    const digitalProduct = await DigitalProduct.create({
      userId,
      title,
      description,
      productType,
      price,
      currency: currency || 'USD',
      downloadUrl,
      previewImages: previewImages || [],
      fileSize,
      fileFormat,
      tags: tags || [],
      slug,
      metaDescription,
      originalFileName,
      fileHash,
      requiresAccount: requiresAccount || false,
      maxDownloads: maxDownloads || -1,
      downloadExpiryDays: downloadExpiryDays || 30,
    });

    res.status(201).json(digitalProduct);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get all digital products for a user
// @route   GET /api/digital-products
// @access  Private
export const getDigitalProducts = async (req: Request, res: Response) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, search, productType, isActive } = req.query;

    const query: any = { userId };

    if (search) {
      query.$text = { $search: search as string };
    }

    if (productType) {
      query.productType = productType;
    }

    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    const products = await DigitalProduct.find(query)
      .sort({ createdAt: -1 })
      .limit(Number(limit) * 1)
      .skip((Number(page) - 1) * Number(limit));

    const total = await DigitalProduct.countDocuments(query);

    res.json({
      products,
      totalPages: Math.ceil(total / Number(limit)),
      currentPage: Number(page),
      total,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get public digital products for marketplace
// @route   GET /api/digital-products/marketplace/:userId
// @access  Public
export const getMarketplaceProducts = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 12, search, productType } = req.query;

    const query: any = { 
      userId,
      isActive: true 
    };

    if (search) {
      query.$text = { $search: search as string };
    }

    if (productType) {
      query.productType = productType;
    }

    const products = await DigitalProduct.find(query)
      .select('-downloadUrl -fileHash') // Don't expose download URLs
      .sort({ createdAt: -1 })
      .limit(Number(limit) * 1)
      .skip((Number(page) - 1) * Number(limit))
      .populate('userId', 'username slug');

    const total = await DigitalProduct.countDocuments(query);

    res.json({
      products,
      totalPages: Math.ceil(total / Number(limit)),
      currentPage: Number(page),
      total,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get single digital product
// @route   GET /api/digital-products/:id
// @access  Private
export const getDigitalProduct = async (req: Request, res: Response) => {
  try {
    const product = await DigitalProduct.findById(req.params.id);

    if (!product) {
      return res.status(404).json({ message: 'Digital product not found' });
    }

    // Check if user owns the product
    if (product.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(product);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update digital product
// @route   PUT /api/digital-products/:id
// @access  Private
export const updateDigitalProduct = async (req: Request, res: Response) => {
  try {
    const product = await DigitalProduct.findById(req.params.id);

    if (!product) {
      return res.status(404).json({ message: 'Digital product not found' });
    }

    // Check if user owns the product
    if (product.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Update slug if title changed
    if (req.body.title && req.body.title !== product.title) {
      const baseSlug = generateSlug(req.body.title);
      let slug = baseSlug;
      let counter = 1;

      while (await DigitalProduct.findOne({ 
        userId: req.user._id, 
        slug, 
        _id: { $ne: product._id } 
      })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }
      req.body.slug = slug;
    }

    const updatedProduct = await DigitalProduct.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    res.json(updatedProduct);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Delete digital product
// @route   DELETE /api/digital-products/:id
// @access  Private
export const deleteDigitalProduct = async (req: Request, res: Response) => {
  try {
    const product = await DigitalProduct.findById(req.params.id);

    if (!product) {
      return res.status(404).json({ message: 'Digital product not found' });
    }

    // Check if user owns the product
    if (product.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Check if product has purchases
    const purchaseCount = await Purchase.countDocuments({ digitalProductId: product._id });
    if (purchaseCount > 0) {
      return res.status(400).json({ 
        message: 'Cannot delete product with existing purchases. Deactivate instead.' 
      });
    }

    await DigitalProduct.findByIdAndDelete(req.params.id);

    res.json({ message: 'Digital product deleted successfully' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get product analytics
// @route   GET /api/digital-products/:id/analytics
// @access  Private
export const getProductAnalytics = async (req: Request, res: Response) => {
  try {
    const product = await DigitalProduct.findById(req.params.id);

    if (!product) {
      return res.status(404).json({ message: 'Digital product not found' });
    }

    // Check if user owns the product
    if (product.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Get purchase analytics
    const purchases = await Purchase.find({ digitalProductId: product._id });
    const totalRevenue = purchases
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0);

    const analytics = {
      totalPurchases: product.purchaseCount,
      totalDownloads: product.downloadCount,
      totalRevenue,
      averageOrderValue: product.purchaseCount > 0 ? totalRevenue / product.purchaseCount : 0,
      recentPurchases: purchases.slice(-10),
    };

    res.json(analytics);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};
