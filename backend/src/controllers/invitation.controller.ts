import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import User from '../models/User.model';
import Invitation, { InvitationStatus, UserRole } from '../models/Invitation.model';
import { sendInvitationEmail, sendConfirmationEmail } from '../services/email.service';

// @desc    Send an invitation to collaborate
// @route   POST /api/invitations
// @access  Private
export const sendInvitation = async (req: Request, res: Response) => {
  try {
    const { email, role } = req.body;

    // Check if user exists
    const inviter = await User.findById(req.user._id);
    if (!inviter) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if the email is already registered
    const invitedUser = await User.findOne({ email });

    // Check if there's already a pending invitation for this email
    const existingInvitation = await Invitation.findOne({
      email,
      profileId: inviter._id,
      status: InvitationStatus.PENDING,
    });

    if (existingInvitation) {
      return res.status(400).json({ message: 'An invitation has already been sent to this email' });
    }

    // Check if the user is already a collaborator
    if (invitedUser) {
      const isAlreadyCollaborator = inviter.collaborators?.some(
        (collab) => collab.userId.toString() === invitedUser._id?.toString()
      );

      if (isAlreadyCollaborator) {
        return res.status(400).json({ message: 'This user is already a collaborator' });
      }
    }

    // Create a unique token for the invitation
    const token = uuidv4();

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create the invitation
    const invitation = await Invitation.create({
      email,
      profileId: inviter._id,
      invitedBy: inviter._id,
      role: role || UserRole.EDITOR,
      status: InvitationStatus.PENDING,
      token,
      expiresAt,
    });

    // Send invitation email
    await sendInvitationEmail(
      email,
      inviter.username,
      inviter.username,
      role || 'editor',
      token
    );

    res.status(201).json({
      _id: invitation._id,
      email: invitation.email,
      role: invitation.role,
      status: invitation.status,
      expiresAt: invitation.expiresAt,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Accept an invitation
// @route   POST /api/invitations/accept/:token
// @access  Public
export const acceptInvitation = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;

    // Find the invitation
    const invitation = await Invitation.findOne({
      token,
      status: InvitationStatus.PENDING,
    });

    if (!invitation) {
      return res.status(404).json({ message: 'Invalid or expired invitation' });
    }

    // Check if invitation has expired
    if (invitation.expiresAt < new Date()) {
      invitation.status = InvitationStatus.EXPIRED;
      await invitation.save();
      return res.status(400).json({ message: 'Invitation has expired' });
    }

    // Find the profile owner
    const profileOwner = await User.findById(invitation.profileId);
    if (!profileOwner) {
      return res.status(404).json({ message: 'Profile not found' });
    }

    // Check if the user is authenticated
    if (!req.user) {
      // For unauthenticated users, return the invitation details
      // so the frontend can show a registration/login form
      return res.status(200).json({
        invitation: {
          _id: invitation._id,
          email: invitation.email,
          role: invitation.role,
          profileId: invitation.profileId,
          profileName: profileOwner.username,
        },
        requiresAuth: true,
      });
    }

    // If user is authenticated, check if their email matches the invitation
    if (req.user.email !== invitation.email) {
      return res.status(400).json({
        message: 'This invitation was sent to a different email address',
        invitationEmail: invitation.email,
      });
    }

    // Update the invitation status
    invitation.status = InvitationStatus.ACCEPTED;
    await invitation.save();

    // Add the user as a collaborator to the profile
    const collaborator = {
      userId: req.user._id,
      role: invitation.role,
      addedAt: new Date(),
    };

    // Initialize collaborators array if it doesn't exist
    if (!profileOwner.collaborators) {
      profileOwner.collaborators = [];
    }

    // Add the collaborator if not already present
    const userId = req.user._id.toString();
    if (!profileOwner.collaborators.some(c => c.userId.toString() === userId)) {
      profileOwner.collaborators.push(collaborator);
      await profileOwner.save();
    }

    // Send confirmation email
    await sendConfirmationEmail(
      req.user.email,
      profileOwner.username,
      invitation.role
    );

    res.status(200).json({
      message: 'Invitation accepted successfully',
      profileId: profileOwner._id,
      profileSlug: profileOwner.slug,
      role: invitation.role,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Reject an invitation
// @route   POST /api/invitations/reject/:token
// @access  Public
export const rejectInvitation = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;

    // Find the invitation
    const invitation = await Invitation.findOne({
      token,
      status: InvitationStatus.PENDING,
    });

    if (!invitation) {
      return res.status(404).json({ message: 'Invalid or expired invitation' });
    }

    // Update the invitation status
    invitation.status = InvitationStatus.REJECTED;
    await invitation.save();

    res.status(200).json({ message: 'Invitation rejected successfully' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get all invitations for a profile
// @route   GET /api/invitations
// @access  Private
export const getInvitations = async (req: Request, res: Response) => {
  try {
    const invitations = await Invitation.find({
      profileId: req.user._id,
    }).sort({ createdAt: -1 });

    res.status(200).json(invitations);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Cancel an invitation
// @route   DELETE /api/invitations/:id
// @access  Private
export const cancelInvitation = async (req: Request, res: Response) => {
  try {
    const invitation = await Invitation.findById(req.params.id);

    if (!invitation) {
      return res.status(404).json({ message: 'Invitation not found' });
    }

    // Check if the user is the profile owner
    if (invitation.profileId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized to cancel this invitation' });
    }

    // Delete the invitation
    await Invitation.deleteOne({ _id: invitation._id });

    res.status(200).json({ message: 'Invitation cancelled successfully' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get all collaborators for a profile
// @route   GET /api/invitations/collaborators
// @access  Private
export const getCollaborators = async (req: Request, res: Response) => {
  try {
    const user = await User.findById(req.user._id)
      .populate('collaborators.userId', 'username email');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Always return an array, even if collaborators is undefined
    res.status(200).json(user.collaborators || []);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Remove a collaborator
// @route   DELETE /api/invitations/collaborators/:userId
// @access  Private
export const removeCollaborator = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if collaborators array exists
    if (!user.collaborators) {
      return res.status(404).json({ message: 'No collaborators found' });
    }

    // Check if the specific collaborator exists
    if (!user.collaborators.some(c => c.userId.toString() === userId)) {
      return res.status(404).json({ message: 'Collaborator not found' });
    }

    // Remove the collaborator
    user.collaborators = user.collaborators.filter(c => c.userId.toString() !== userId);
    await user.save();

    res.status(200).json({ message: 'Collaborator removed successfully' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update collaborator role
// @route   PUT /api/invitations/collaborators/:userId
// @access  Private
export const updateCollaboratorRole = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    if (!role || !Object.values(UserRole).includes(role as UserRole)) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if the collaborator exists
    if (!user.collaborators) {
      return res.status(404).json({ message: 'No collaborators found' });
    }

    const collaboratorIndex = user.collaborators.findIndex(c => c.userId.toString() === userId);

    if (collaboratorIndex === -1) {
      return res.status(404).json({ message: 'Collaborator not found' });
    }

    // Update the role
    user.collaborators[collaboratorIndex].role = role;
    await user.save();

    res.status(200).json({
      message: 'Collaborator role updated successfully',
      collaborator: user.collaborators[collaboratorIndex],
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};