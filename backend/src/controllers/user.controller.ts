import { Request, Response } from 'express';
import User, { CountryProfile } from '../models/User.model';
import Link from '../models/Link.model';
import QRCode from 'qrcode';
import { createCanvas, loadImage } from 'canvas';

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
export const updateProfile = async (req: Request, res: Response) => {
  try {
    const {
      username,
      bio,
      avatarUrl,
      avatarPosition,
      avatarShape,
      bannerUrl,
      bannerColor,
      pageBackgroundColor,
      cardBackgroundColor,
      fontColor,
      fontFamily,
      backgroundImage,
      backgroundPosition,
      backgroundRepeat,
      backgroundSize,
      backgroundAttachment,
      theme,
      directRedirectEnabled,
      directRedirectUrl,
      verifiedProfile,
      verifiedProfileGold,
      // Analytics settings
      googleAnalyticsId,
      microsoftClarityId,
      // SEO settings
      seoTitle,
      seoDescription,
      ogTitle,
      ogDescription,
      ogImage,
      ogUrl,
      ogType,
      // Direct Monetization settings
      stripePaymentLink,
      paypalPaymentLink,
      molliePaymentLink,
      razorpayPaymentLink,
      // Amazon Affiliate settings
      amazonAffiliateId,
      amazonAffiliateAutoApply
    } = req.body;

    console.log('updateProfile: Request body:', req.body);

    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.username = username || user.username;
    user.bio = bio !== undefined ? bio : user.bio;
    user.avatarUrl = avatarUrl || user.avatarUrl;
    user.avatarPosition = avatarPosition || user.avatarPosition;
    user.avatarShape = avatarShape || user.avatarShape;
    user.bannerUrl = bannerUrl || user.bannerUrl;
    user.bannerColor = bannerColor || user.bannerColor;
    user.pageBackgroundColor = pageBackgroundColor || user.pageBackgroundColor;
    user.cardBackgroundColor = cardBackgroundColor || user.cardBackgroundColor;
    user.fontColor = fontColor || user.fontColor;
    user.fontFamily = fontFamily !== undefined ? fontFamily : user.fontFamily;

    // Background image and properties
    user.backgroundImage = backgroundImage !== undefined ? backgroundImage : user.backgroundImage;
    user.backgroundPosition = backgroundPosition || user.backgroundPosition;
    user.backgroundRepeat = backgroundRepeat || user.backgroundRepeat;
    user.backgroundSize = backgroundSize || user.backgroundSize;
    user.backgroundAttachment = backgroundAttachment || user.backgroundAttachment;

    console.log('updateProfile: Background image and properties:', {
      backgroundImage: user.backgroundImage,
      backgroundPosition: user.backgroundPosition,
      backgroundRepeat: user.backgroundRepeat,
      backgroundSize: user.backgroundSize,
      backgroundAttachment: user.backgroundAttachment
    });

    user.theme = theme || user.theme;
    // Handle verified profile settings
    user.verifiedProfile = verifiedProfile !== undefined ? verifiedProfile : user.verifiedProfile;
    user.verifiedProfileGold = verifiedProfileGold !== undefined ? verifiedProfileGold : user.verifiedProfileGold;

    // Analytics settings
    user.googleAnalyticsId = googleAnalyticsId !== undefined ? googleAnalyticsId : user.googleAnalyticsId;
    user.microsoftClarityId = microsoftClarityId !== undefined ? microsoftClarityId : user.microsoftClarityId;

    // SEO settings
    user.seoTitle = seoTitle !== undefined ? seoTitle : user.seoTitle;
    user.seoDescription = seoDescription !== undefined ? seoDescription : user.seoDescription;
    user.ogTitle = ogTitle !== undefined ? ogTitle : user.ogTitle;
    user.ogDescription = ogDescription !== undefined ? ogDescription : user.ogDescription;
    user.ogImage = ogImage !== undefined ? ogImage : user.ogImage;
    user.ogUrl = ogUrl !== undefined ? ogUrl : user.ogUrl;
    user.ogType = ogType !== undefined ? ogType : user.ogType;

    // Direct Monetization settings
    user.stripePaymentLink = stripePaymentLink !== undefined ? stripePaymentLink : user.stripePaymentLink;
    user.paypalPaymentLink = paypalPaymentLink !== undefined ? paypalPaymentLink : user.paypalPaymentLink;
    user.molliePaymentLink = molliePaymentLink !== undefined ? molliePaymentLink : user.molliePaymentLink;
    user.razorpayPaymentLink = razorpayPaymentLink !== undefined ? razorpayPaymentLink : user.razorpayPaymentLink;

    // Amazon Affiliate settings
    user.amazonAffiliateId = amazonAffiliateId !== undefined ? amazonAffiliateId : user.amazonAffiliateId;
    user.amazonAffiliateAutoApply = amazonAffiliateAutoApply !== undefined ? amazonAffiliateAutoApply : user.amazonAffiliateAutoApply;

    // Handle direct redirect settings
    const previousRedirectEnabled = user.directRedirectEnabled;
    user.directRedirectEnabled = directRedirectEnabled !== undefined ? directRedirectEnabled : user.directRedirectEnabled;

    console.log('Direct redirect settings:', {
      previousValue: previousRedirectEnabled,
      requestedValue: directRedirectEnabled,
      newValue: user.directRedirectEnabled
    });

    // Always update the URL if it's provided, regardless of whether redirect is enabled
    // This allows the URL to be saved even when redirect is disabled
    if (directRedirectUrl !== undefined) {
      user.directRedirectUrl = directRedirectUrl;
      console.log('Updated redirect URL to:', directRedirectUrl);
    }

    const updatedUser = await user.save();

    const response = {
      _id: updatedUser._id,
      email: updatedUser.email,
      username: updatedUser.username,
      slug: updatedUser.slug,
      bio: updatedUser.bio,
      avatarUrl: updatedUser.avatarUrl,
      avatarPosition: updatedUser.avatarPosition,
      avatarShape: updatedUser.avatarShape,
      bannerUrl: updatedUser.bannerUrl,
      bannerColor: updatedUser.bannerColor,
      pageBackgroundColor: updatedUser.pageBackgroundColor,
      cardBackgroundColor: updatedUser.cardBackgroundColor,
      fontColor: updatedUser.fontColor,
      fontFamily: updatedUser.fontFamily,
      // Background image and properties
      backgroundImage: updatedUser.backgroundImage,
      backgroundPosition: updatedUser.backgroundPosition,
      backgroundRepeat: updatedUser.backgroundRepeat,
      backgroundSize: updatedUser.backgroundSize,
      backgroundAttachment: updatedUser.backgroundAttachment,
      theme: updatedUser.theme,
      directRedirectEnabled: updatedUser.directRedirectEnabled,
      directRedirectUrl: updatedUser.directRedirectUrl,
      verifiedProfile: updatedUser.verifiedProfile,
      verifiedProfileGold: updatedUser.verifiedProfileGold,
      countryProfiles: updatedUser.countryProfiles,
      // Analytics settings
      googleAnalyticsId: updatedUser.googleAnalyticsId,
      microsoftClarityId: updatedUser.microsoftClarityId,
      // SEO settings
      seoTitle: updatedUser.seoTitle,
      seoDescription: updatedUser.seoDescription,
      ogTitle: updatedUser.ogTitle,
      ogDescription: updatedUser.ogDescription,
      ogImage: updatedUser.ogImage,
      ogUrl: updatedUser.ogUrl,
      ogType: updatedUser.ogType,
      // Direct Monetization settings
      stripePaymentLink: updatedUser.stripePaymentLink,
      paypalPaymentLink: updatedUser.paypalPaymentLink,
      molliePaymentLink: updatedUser.molliePaymentLink,
      razorpayPaymentLink: updatedUser.razorpayPaymentLink,
      // Amazon Affiliate settings
      amazonAffiliateId: updatedUser.amazonAffiliateId,
      amazonAffiliateAutoApply: updatedUser.amazonAffiliateAutoApply,
      // Payment callback URLs
      paymentThankYouUrl: updatedUser.paymentThankYouUrl,
      paymentCancellationUrl: updatedUser.paymentCancellationUrl,
    };

    console.log('updateProfile: Response with background properties:', {
      backgroundImage: response.backgroundImage,
      backgroundPosition: response.backgroundPosition,
      backgroundRepeat: response.backgroundRepeat,
      backgroundSize: response.backgroundSize,
      backgroundAttachment: response.backgroundAttachment
    });

    // Log direct redirect settings in response
    console.log('updateProfile: Response with direct redirect settings:', {
      directRedirectEnabled: response.directRedirectEnabled,
      directRedirectUrl: response.directRedirectUrl
    });

    res.json(response);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update user slug
// @route   PUT /api/users/slug
// @access  Private
export const updateSlug = async (req: Request, res: Response) => {
  try {
    const { slug } = req.body;

    if (!slug) {
      return res.status(400).json({ message: 'Slug is required' });
    }

    // Check if slug is already taken
    const slugExists = await User.findOne({
      slug,
      _id: { $ne: req.user._id }
    });

    if (slugExists) {
      return res.status(400).json({ message: 'This slug is already taken' });
    }

    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.slug = slug.toLowerCase().replace(/\s+/g, '-');
    const updatedUser = await user.save();

    res.json({
      _id: updatedUser._id,
      slug: updatedUser.slug,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Get public user profile by slug
// @route   GET /api/users/:slug
// @access  Public
export const getPublicProfile = async (req: Request, res: Response) => {
  try {
    const { countryCode } = req.query;
    const user = await User.findOne({ slug: req.params.slug }).select('-password -email');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get user's active (non-archived) and enabled links only
    const links = await Link.find({
      userId: user._id,
      archived: false,
      enabled: true
    }).sort({ order: 1 });

    // If countryCode is provided and a country profile exists, merge it with the user data
    let responseUser = { ...user.toObject() };

    if (countryCode && user.countryProfiles && user.countryProfiles.length > 0) {
      const countryProfile = user.countryProfiles.find(
        (profile: CountryProfile) => profile.countryCode === countryCode && profile.enabled
      );

      if (countryProfile) {
        // Merge country profile with user data
        responseUser = {
          ...responseUser,
          bio: countryProfile.bio || responseUser.bio,
          avatarUrl: countryProfile.avatarUrl || responseUser.avatarUrl,
          avatarPosition: countryProfile.avatarPosition || responseUser.avatarPosition,
          avatarShape: countryProfile.avatarShape || responseUser.avatarShape,
          bannerUrl: countryProfile.bannerUrl || responseUser.bannerUrl,
          bannerColor: countryProfile.bannerColor || responseUser.bannerColor,
          pageBackgroundColor: countryProfile.pageBackgroundColor || responseUser.pageBackgroundColor,
          cardBackgroundColor: countryProfile.cardBackgroundColor || responseUser.cardBackgroundColor,
          fontColor: countryProfile.fontColor || responseUser.fontColor,
          fontFamily: countryProfile.fontFamily || responseUser.fontFamily,
          backgroundImage: countryProfile.backgroundImage || responseUser.backgroundImage,
          backgroundPosition: countryProfile.backgroundPosition || responseUser.backgroundPosition,
          backgroundRepeat: countryProfile.backgroundRepeat || responseUser.backgroundRepeat,
          backgroundSize: countryProfile.backgroundSize || responseUser.backgroundSize,
          backgroundAttachment: countryProfile.backgroundAttachment || responseUser.backgroundAttachment
        };
      }
    }

    res.json({
      user: responseUser,
      links,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update country profile
// @route   PUT /api/users/country-profile
// @access  Private
export const updateCountryProfile = async (req: Request, res: Response) => {
  try {
    const {
      countryCode,
      bio,
      avatarUrl,
      avatarPosition,
      avatarShape,
      bannerUrl,
      bannerColor,
      pageBackgroundColor,
      cardBackgroundColor,
      fontColor,
      fontFamily,
      backgroundImage,
      backgroundPosition,
      backgroundRepeat,
      backgroundSize,
      backgroundAttachment,
      enabled
    } = req.body;

    if (!countryCode) {
      return res.status(400).json({ message: 'Country code is required' });
    }

    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Initialize countryProfiles array if it doesn't exist
    if (!user.countryProfiles) {
      user.countryProfiles = [];
    }

    // Check if country profile already exists
    const existingProfileIndex = user.countryProfiles.findIndex(
      (profile: CountryProfile) => profile.countryCode === countryCode
    );

    if (existingProfileIndex >= 0) {
      // Update existing profile
      const updatedProfile = {
        ...user.countryProfiles[existingProfileIndex],
        bio: bio !== undefined ? bio : user.countryProfiles[existingProfileIndex].bio,
        avatarUrl: avatarUrl || user.countryProfiles[existingProfileIndex].avatarUrl,
        avatarPosition: avatarPosition || user.countryProfiles[existingProfileIndex].avatarPosition,
        avatarShape: avatarShape || user.countryProfiles[existingProfileIndex].avatarShape,
        bannerUrl: bannerUrl || user.countryProfiles[existingProfileIndex].bannerUrl,
        bannerColor: bannerColor || user.countryProfiles[existingProfileIndex].bannerColor,
        pageBackgroundColor: pageBackgroundColor || user.countryProfiles[existingProfileIndex].pageBackgroundColor,
        cardBackgroundColor: cardBackgroundColor || user.countryProfiles[existingProfileIndex].cardBackgroundColor,
        fontColor: fontColor || user.countryProfiles[existingProfileIndex].fontColor,
        fontFamily: fontFamily !== undefined ? fontFamily : user.countryProfiles[existingProfileIndex].fontFamily,
        backgroundImage: backgroundImage !== undefined ? backgroundImage : user.countryProfiles[existingProfileIndex].backgroundImage,
        backgroundPosition: backgroundPosition || user.countryProfiles[existingProfileIndex].backgroundPosition,
        backgroundRepeat: backgroundRepeat || user.countryProfiles[existingProfileIndex].backgroundRepeat,
        backgroundSize: backgroundSize || user.countryProfiles[existingProfileIndex].backgroundSize,
        backgroundAttachment: backgroundAttachment || user.countryProfiles[existingProfileIndex].backgroundAttachment,
        enabled: enabled !== undefined ? enabled : user.countryProfiles[existingProfileIndex].enabled
      };

      user.countryProfiles[existingProfileIndex] = updatedProfile;
    } else {
      // Create new profile
      const newProfile: CountryProfile = {
        countryCode,
        bio: bio || user.bio || '',
        avatarUrl: avatarUrl || user.avatarUrl || '',
        avatarPosition: avatarPosition || user.avatarPosition || 'center',
        avatarShape: avatarShape || user.avatarShape || 'circle',
        bannerUrl: bannerUrl || user.bannerUrl || '',
        bannerColor: bannerColor || user.bannerColor || '#e0f2fe',
        pageBackgroundColor: pageBackgroundColor || user.pageBackgroundColor || '#f8f9fa',
        cardBackgroundColor: cardBackgroundColor || user.cardBackgroundColor || '#ffffff',
        fontColor: fontColor || user.fontColor || '#000000',
        fontFamily: fontFamily !== undefined ? fontFamily : user.fontFamily || '',
        backgroundImage: user.backgroundImage || '',
        backgroundPosition: user.backgroundPosition || 'center',
        backgroundRepeat: user.backgroundRepeat || 'no-repeat',
        backgroundSize: user.backgroundSize || 'cover',
        backgroundAttachment: user.backgroundAttachment || 'scroll',
        enabled: enabled !== undefined ? enabled : true
      };

      user.countryProfiles.push(newProfile);
    }

    const updatedUser = await user.save();

    res.json({
      _id: updatedUser._id,
      email: updatedUser.email,
      username: updatedUser.username,
      slug: updatedUser.slug,
      bio: updatedUser.bio,
      avatarUrl: updatedUser.avatarUrl,
      avatarPosition: updatedUser.avatarPosition,
      avatarShape: updatedUser.avatarShape,
      bannerUrl: updatedUser.bannerUrl,
      bannerColor: updatedUser.bannerColor,
      pageBackgroundColor: updatedUser.pageBackgroundColor,
      cardBackgroundColor: updatedUser.cardBackgroundColor,
      fontColor: updatedUser.fontColor,
      fontFamily: updatedUser.fontFamily,
      backgroundImage: updatedUser.backgroundImage,
      backgroundPosition: updatedUser.backgroundPosition,
      backgroundRepeat: updatedUser.backgroundRepeat,
      backgroundSize: updatedUser.backgroundSize,
      backgroundAttachment: updatedUser.backgroundAttachment,
      theme: updatedUser.theme,
      countryProfiles: updatedUser.countryProfiles,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Delete country profile
// @route   DELETE /api/users/country-profile/:countryCode
// @access  Private
export const deleteCountryProfile = async (req: Request, res: Response) => {
  try {
    const { countryCode } = req.params;

    if (!countryCode) {
      return res.status(400).json({ message: 'Country code is required' });
    }

    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if country profile exists
    if (!user.countryProfiles || user.countryProfiles.length === 0) {
      return res.status(404).json({ message: 'No country profiles found' });
    }

    // Filter out the country profile to delete
    user.countryProfiles = user.countryProfiles.filter(
      (profile: CountryProfile) => profile.countryCode !== countryCode
    );

    const updatedUser = await user.save();

    res.json({
      _id: updatedUser._id,
      email: updatedUser.email,
      username: updatedUser.username,
      slug: updatedUser.slug,
      bio: updatedUser.bio,
      avatarUrl: updatedUser.avatarUrl,
      avatarPosition: updatedUser.avatarPosition,
      avatarShape: updatedUser.avatarShape,
      bannerUrl: updatedUser.bannerUrl,
      bannerColor: updatedUser.bannerColor,
      pageBackgroundColor: updatedUser.pageBackgroundColor,
      cardBackgroundColor: updatedUser.cardBackgroundColor,
      fontColor: updatedUser.fontColor,
      fontFamily: updatedUser.fontFamily,
      backgroundImage: updatedUser.backgroundImage,
      backgroundPosition: updatedUser.backgroundPosition,
      backgroundRepeat: updatedUser.backgroundRepeat,
      backgroundSize: updatedUser.backgroundSize,
      backgroundAttachment: updatedUser.backgroundAttachment,
      theme: updatedUser.theme,
      countryProfiles: updatedUser.countryProfiles,
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Generate QR code for user profile
// @route   GET /api/users/qrcode
// @access  Private
export const generateQRCode = async (req: Request, res: Response) => {
  try {
    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Generate the profile URL
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const profileUrl = `${baseUrl}/#/u/${user.slug}`;

    // Create a canvas for the QR code
    const size = 300;
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }

    // Fill with white background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, size, size);

    // Generate QR code with high error correction to allow for center logo
    await QRCode.toCanvas(canvas, profileUrl, {
      width: size,
      margin: 2,
      errorCorrectionLevel: 'H', // High error correction level to allow for center logo
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });

    // Add a white circle in the center
    const centerX = size / 2;
    const centerY = size / 2;
    const radius = size * 0.15; // 15% of the QR code size

    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fill();

    // Add the "Hi!" text
    ctx.fillStyle = '#000000';
    ctx.font = `bold ${Math.floor(radius)}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('Hi!', centerX, centerY);

    // Convert to data URL
    const qrCodeDataUrl = canvas.toDataURL();

    res.json({
      qrCode: qrCodeDataUrl,
      profileUrl
    });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};