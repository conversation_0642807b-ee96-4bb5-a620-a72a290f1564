import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Import routes
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import linkRoutes from './routes/link.routes';
import statsRoutes from './routes/stats.routes';
import mediaRoutes from './routes/media.routes';
import leadRoutes from './routes/lead.routes';
import referralRoutes from './routes/referral.routes';
import directRedirectRoutes from './routes/directRedirect.routes';
import invitationRoutes from './routes/invitation.routes';

// Initialize express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow: boolean) => void) => {
    // Allow requests with no origin (like mobile apps, curl, Postman)
    if (!origin) {
      callback(null, true);
      return;
    }

    // Define allowed origins
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:5173',
      'http://localhost:5001',
      'http://localhost:5173'
    ];

    // Check if the origin is allowed
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`Origin ${origin} not allowed by CORS`);
      callback(null, true); // Allow all origins in development for easier debugging
    }
  },
  credentials: true, // Allow cookies to be sent with requests
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

console.log('CORS options:', corsOptions);
app.use(cors(corsOptions));

// Add cookie middleware
app.use(cookieParser()); // Parse cookies

// Add body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add logging middleware
app.use(morgan('dev'));

// Log all requests for debugging
app.use((req, res, next) => {
  console.log('Request:', {
    method: req.method,
    url: req.url,
    cookies: req.cookies,
    headers: req.headers
  });
  next();
});

// Serve static files from media directory
app.use('/media', express.static(path.join(__dirname, '../media')));

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to Linktree Clone API' });
});

// Apply routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/links', linkRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/media', mediaRoutes);
app.use('/api/leads', leadRoutes);
app.use('/api/referrals', referralRoutes);
app.use('/api/direct-redirect', directRedirectRoutes);
app.use('/api/invitations', invitationRoutes);

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/linktree-clone';
console.log('MongoDB URI:', MONGODB_URI);

mongoose
  .connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    // Start server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  })
  .catch((error) => {
    console.error('MongoDB connection error:', error);
  });
