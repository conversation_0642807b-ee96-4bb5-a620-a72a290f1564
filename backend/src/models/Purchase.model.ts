import mongoose, { Document, Schema } from 'mongoose';

export interface IPurchase extends Document {
  userId: mongoose.Types.ObjectId; // Buyer
  sellerId: mongoose.Types.ObjectId; // Product owner
  digitalProductId: mongoose.Types.ObjectId;
  // Transaction details
  transactionId: string;
  paymentProvider: 'stripe' | 'paypal' | 'mollie' | 'razorpay';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  // Customer information
  customerEmail: string;
  customerName?: string;
  // Download tracking
  downloadCount: number;
  maxDownloads: number;
  downloadExpiryDate?: Date;
  downloadTokens: string[];
  // Payment metadata
  paymentIntentId?: string;
  paymentMethodId?: string;
  refundId?: string;
  refundAmount?: number;
  refundReason?: string;
  // Additional data
  ipAddress?: string;
  userAgent?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const PurchaseSchema = new Schema<IPurchase>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    sellerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    digitalProductId: {
      type: Schema.Types.ObjectId,
      ref: 'DigitalProduct',
      required: true,
    },
    transactionId: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    paymentProvider: {
      type: String,
      enum: ['stripe', 'paypal', 'mollie', 'razorpay'],
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      required: true,
      uppercase: true,
      maxlength: 3,
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending',
    },
    customerEmail: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    customerName: {
      type: String,
      trim: true,
    },
    downloadCount: {
      type: Number,
      default: 0,
    },
    maxDownloads: {
      type: Number,
      default: 3,
    },
    downloadExpiryDate: {
      type: Date,
    },
    downloadTokens: {
      type: [String],
      default: [],
    },
    paymentIntentId: {
      type: String,
      trim: true,
    },
    paymentMethodId: {
      type: String,
      trim: true,
    },
    refundId: {
      type: String,
      trim: true,
    },
    refundAmount: {
      type: Number,
      min: 0,
    },
    refundReason: {
      type: String,
      trim: true,
    },
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for efficient queries
PurchaseSchema.index({ userId: 1, createdAt: -1 });
PurchaseSchema.index({ sellerId: 1, createdAt: -1 });
PurchaseSchema.index({ digitalProductId: 1, createdAt: -1 });
// Note: transactionId already has unique index from field definition
PurchaseSchema.index({ status: 1 });
PurchaseSchema.index({ customerEmail: 1 });

export default mongoose.model<IPurchase>('Purchase', PurchaseSchema);
