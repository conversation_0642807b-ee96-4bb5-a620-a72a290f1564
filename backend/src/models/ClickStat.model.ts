import mongoose, { Document, Schema } from 'mongoose';

export interface IClickStat extends Document {
  linkId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
}

const ClickStatSchema = new Schema<IClickStat>({
  linkId: {
    type: Schema.Types.ObjectId,
    ref: 'Link',
    required: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  userAgent: {
    type: String,
  },
  ip: {
    type: String,
  },
});

export default mongoose.model<IClickStat>('ClickStat', ClickStatSchema);
