import mongoose, { Document, Schema } from 'mongoose';

export interface IDigitalProduct extends Document {
  userId: mongoose.Types.ObjectId;
  title: string;
  description: string;
  productType: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other';
  price: number;
  currency: string;
  downloadUrl: string;
  previewImages: string[];
  fileSize: string;
  fileFormat: string;
  tags: string[];
  isActive: boolean;
  downloadCount: number;
  purchaseCount: number;
  // SEO and marketing
  slug: string;
  metaDescription?: string;
  // File management
  originalFileName?: string;
  fileHash?: string;
  // Pricing options
  discountPrice?: number;
  discountEndDate?: Date;
  // Access control
  requiresAccount?: boolean;
  maxDownloads?: number;
  downloadExpiryDays?: number;
  createdAt: Date;
  updatedAt: Date;
}

const DigitalProductSchema = new Schema<IDigitalProduct>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 2000,
    },
    productType: {
      type: String,
      enum: ['template', 'preset', 'guide', 'ebook', 'course', 'software', 'other'],
      required: true,
    },
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true,
      maxlength: 3,
    },
    downloadUrl: {
      type: String,
      required: true,
      trim: true,
    },
    previewImages: {
      type: [String],
      default: [],
      validate: {
        validator: function(images: string[]) {
          return images.length <= 5;
        },
        message: 'Maximum 5 preview images allowed'
      }
    },
    fileSize: {
      type: String,
      trim: true,
    },
    fileFormat: {
      type: String,
      trim: true,
      uppercase: true,
    },
    tags: {
      type: [String],
      default: [],
      validate: {
        validator: function(tags: string[]) {
          return tags.length <= 10;
        },
        message: 'Maximum 10 tags allowed'
      }
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    downloadCount: {
      type: Number,
      default: 0,
    },
    purchaseCount: {
      type: Number,
      default: 0,
    },
    slug: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    metaDescription: {
      type: String,
      trim: true,
      maxlength: 160,
    },
    originalFileName: {
      type: String,
      trim: true,
    },
    fileHash: {
      type: String,
      trim: true,
    },
    discountPrice: {
      type: Number,
      min: 0,
    },
    discountEndDate: {
      type: Date,
    },
    requiresAccount: {
      type: Boolean,
      default: false,
    },
    maxDownloads: {
      type: Number,
      default: -1, // -1 means unlimited
    },
    downloadExpiryDays: {
      type: Number,
      default: 30,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound index for user and slug
DigitalProductSchema.index({ userId: 1, slug: 1 }, { unique: true });

// Create index for search functionality
DigitalProductSchema.index({ title: 'text', description: 'text', tags: 'text' });

export default mongoose.model<IDigitalProduct>('DigitalProduct', DigitalProductSchema);
