import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import { UserRole } from './Invitation.model';

export interface ICollaborator {
  userId: mongoose.Schema.Types.ObjectId;
  role: string;
  addedAt: Date;
}

export interface CountryProfile {
  countryCode: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: string;
  avatarShape?: string;
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  backgroundSize?: string;
  backgroundAttachment?: string;
  enabled: boolean;
}

export interface IUser extends Document {
  email: string;
  password: string;
  username: string;
  slug: string;
  bio?: string;
  avatarUrl?: string;
  avatarPosition?: string;
  avatarShape?: string;
  bannerUrl?: string;
  bannerColor?: string;
  pageBackgroundColor?: string;
  cardBackgroundColor?: string;
  fontColor?: string;
  fontFamily?: string;
  backgroundImage?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  backgroundSize?: string;
  backgroundAttachment?: string;
  theme?: string;
  directRedirectEnabled?: boolean;
  directRedirectUrl?: string;
  directRedirectCount?: number;
  verifiedProfile?: boolean;
  verifiedProfileGold?: boolean;
  countryProfiles?: CountryProfile[];
  // Analytics settings
  googleAnalyticsId?: string;
  microsoftClarityId?: string;
  // SEO settings
  seoTitle?: string;
  seoDescription?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  ogType?: string;
  // Direct Monetization settings
  stripePaymentLink?: string;
  paypalPaymentLink?: string;
  molliePaymentLink?: string;
  razorpayPaymentLink?: string;
  // Amazon Affiliate settings
  amazonAffiliateId?: string;
  amazonAffiliateAutoApply?: boolean;
  // Payment callback URLs
  paymentThankYouUrl?: string;
  paymentCancellationUrl?: string;
  // Referral settings
  referralCode?: string;
  referralEnabled?: boolean;
  referralVipCode?: string;
  referralVipUsageLimit?: number;
  referralVipUsageCount?: number;
  referredBy?: mongoose.Schema.Types.ObjectId;
  referrals?: mongoose.Schema.Types.ObjectId[];
  // Collaborators
  collaborators?: ICollaborator[];
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const CountryProfileSchema = new Schema({
  countryCode: {
    type: String,
    required: true,
  },
  bio: {
    type: String,
    default: '',
  },
  avatarUrl: {
    type: String,
    default: '',
  },
  avatarPosition: {
    type: String,
    enum: ['left', 'center', 'right'],
    default: 'center',
  },
  avatarShape: {
    type: String,
    enum: ['circle', 'square'],
    default: 'circle',
  },
  bannerUrl: {
    type: String,
    default: '',
  },
  bannerColor: {
    type: String,
    default: '#e0f2fe',
  },
  pageBackgroundColor: {
    type: String,
    default: '#f8f9fa',
  },
  cardBackgroundColor: {
    type: String,
    default: '#ffffff',
  },
  fontColor: {
    type: String,
    default: '#000000',
  },
  fontFamily: {
    type: String,
    default: '',
  },
  backgroundImage: {
    type: String,
    default: '',
  },
  backgroundPosition: {
    type: String,
    enum: ['left', 'center', 'right', 'top', 'bottom'],
    default: 'center',
  },
  backgroundRepeat: {
    type: String,
    enum: ['no-repeat', 'repeat', 'space'],
    default: 'no-repeat',
  },
  backgroundSize: {
    type: String,
    enum: ['auto', 'cover'],
    default: 'cover',
  },
  backgroundAttachment: {
    type: String,
    enum: ['fixed', 'scroll'],
    default: 'scroll',
  },
  enabled: {
    type: Boolean,
    default: true,
  },
}, { _id: false });

const UserSchema = new Schema<IUser>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
    },
    username: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    bio: {
      type: String,
      default: '',
    },
    avatarUrl: {
      type: String,
      default: '',
    },
    avatarPosition: {
      type: String,
      enum: ['left', 'center', 'right'],
      default: 'center',
    },
    avatarShape: {
      type: String,
      enum: ['circle', 'square'],
      default: 'circle',
    },
    bannerUrl: {
      type: String,
      default: '',
    },
    bannerColor: {
      type: String,
      default: '#e0f2fe',
    },
    pageBackgroundColor: {
      type: String,
      default: '#f8f9fa',
    },
    cardBackgroundColor: {
      type: String,
      default: '#ffffff',
    },
    fontColor: {
      type: String,
      default: '#000000',
    },
    fontFamily: {
      type: String,
      default: '',
    },
    backgroundImage: {
      type: String,
      default: '',
    },
    backgroundPosition: {
      type: String,
      enum: ['left', 'center', 'right', 'top', 'bottom'],
      default: 'center',
    },
    backgroundRepeat: {
      type: String,
      enum: ['no-repeat', 'repeat', 'space'],
      default: 'no-repeat',
    },
    backgroundSize: {
      type: String,
      enum: ['auto', 'cover'],
      default: 'cover',
    },
    backgroundAttachment: {
      type: String,
      enum: ['fixed', 'scroll'],
      default: 'scroll',
    },
    theme: {
      type: String,
      enum: ['light', 'dark', 'custom'],
      default: 'light',
    },
    directRedirectEnabled: {
      type: Boolean,
      default: false,
    },
    directRedirectUrl: {
      type: String,
      default: '',
    },
    directRedirectCount: {
      type: Number,
      default: 0,
    },
    verifiedProfile: {
      type: Boolean,
      default: false,
    },
    verifiedProfileGold: {
      type: Boolean,
      default: false,
    },
    countryProfiles: {
      type: [CountryProfileSchema],
      default: [],
    },
    // Analytics settings
    googleAnalyticsId: {
      type: String,
      default: '',
    },
    microsoftClarityId: {
      type: String,
      default: '',
    },
    // SEO settings
    seoTitle: {
      type: String,
      default: '',
      maxlength: 60,
    },
    seoDescription: {
      type: String,
      default: '',
      maxlength: 160,
    },
    ogTitle: {
      type: String,
      default: '',
    },
    ogDescription: {
      type: String,
      default: '',
    },
    ogImage: {
      type: String,
      default: '',
    },
    ogUrl: {
      type: String,
      default: '',
    },
    ogType: {
      type: String,
      default: 'website',
    },
    // Direct Monetization settings
    stripePaymentLink: {
      type: String,
      default: '',
    },
    paypalPaymentLink: {
      type: String,
      default: '',
    },
    molliePaymentLink: {
      type: String,
      default: '',
    },
    razorpayPaymentLink: {
      type: String,
      default: '',
    },
    // Amazon Affiliate settings
    amazonAffiliateId: {
      type: String,
      default: '',
    },
    amazonAffiliateAutoApply: {
      type: Boolean,
      default: false,
    },
    // Payment callback URLs
    paymentThankYouUrl: {
      type: String,
      default: '',
    },
    paymentCancellationUrl: {
      type: String,
      default: '',
    },
    // Referral settings
    referralCode: {
      type: String,
      default: '',
    },
    referralEnabled: {
      type: Boolean,
      default: false,
    },
    referralVipCode: {
      type: String,
      default: '',
    },
    referralVipUsageLimit: {
      type: Number,
      default: 10,
    },
    referralVipUsageCount: {
      type: Number,
      default: 0,
    },
    referredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    referrals: {
      type: [mongoose.Schema.Types.ObjectId],
      ref: 'User',
      default: [],
    },
    // Collaborators
    collaborators: {
      type: [{
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        role: {
          type: String,
          enum: Object.values(UserRole),
          default: UserRole.EDITOR,
        },
        addedAt: {
          type: Date,
          default: Date.now,
        },
      }],
      default: [],
    },
  },
  {
    timestamps: true,
  }
);

// Hash password before saving
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Compare password method
UserSchema.methods.comparePassword = async function (
  candidatePassword: string
): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.model<IUser>('User', UserSchema);
