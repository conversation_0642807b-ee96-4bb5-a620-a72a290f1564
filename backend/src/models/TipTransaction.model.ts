import mongoose, { Document, Schema } from 'mongoose';

export interface ITipTransaction extends Document {
  recipientId: mongoose.Types.ObjectId; // User receiving the tip
  tipJarId: mongoose.Types.ObjectId; // Link ID of the tip jar component
  // Transaction details
  transactionId: string;
  paymentProvider: 'stripe' | 'paypal' | 'mollie' | 'razorpay';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  // Tipper information (anonymous or registered)
  tipperEmail?: string;
  tipperName?: string;
  tipperMessage?: string;
  isAnonymous: boolean;
  // Payment metadata
  paymentIntentId?: string;
  paymentMethodId?: string;
  refundId?: string;
  refundAmount?: number;
  refundReason?: string;
  // Additional data
  ipAddress?: string;
  userAgent?: string;
  // Virtual gift details (if applicable)
  giftType?: 'coffee' | 'beer' | 'pizza' | 'heart' | 'star' | 'custom';
  giftEmoji?: string;
  giftMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

const TipTransactionSchema = new Schema<ITipTransaction>(
  {
    recipientId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    tipJarId: {
      type: Schema.Types.ObjectId,
      ref: 'Link',
      required: true,
    },
    transactionId: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    paymentProvider: {
      type: String,
      enum: ['stripe', 'paypal', 'mollie', 'razorpay'],
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0.5, // Minimum tip amount
    },
    currency: {
      type: String,
      required: true,
      uppercase: true,
      maxlength: 3,
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending',
    },
    tipperEmail: {
      type: String,
      trim: true,
      lowercase: true,
    },
    tipperName: {
      type: String,
      trim: true,
      maxlength: 100,
    },
    tipperMessage: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    isAnonymous: {
      type: Boolean,
      default: false,
    },
    paymentIntentId: {
      type: String,
      trim: true,
    },
    paymentMethodId: {
      type: String,
      trim: true,
    },
    refundId: {
      type: String,
      trim: true,
    },
    refundAmount: {
      type: Number,
      min: 0,
    },
    refundReason: {
      type: String,
      trim: true,
    },
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    giftType: {
      type: String,
      enum: ['coffee', 'beer', 'pizza', 'heart', 'star', 'custom'],
      default: 'heart',
    },
    giftEmoji: {
      type: String,
      default: '❤️',
    },
    giftMessage: {
      type: String,
      trim: true,
      maxlength: 200,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for efficient queries
TipTransactionSchema.index({ recipientId: 1, createdAt: -1 });
TipTransactionSchema.index({ tipJarId: 1, createdAt: -1 });
// Note: transactionId already has unique index from field definition
TipTransactionSchema.index({ status: 1 });
TipTransactionSchema.index({ tipperEmail: 1 });
TipTransactionSchema.index({ amount: 1 });

export default mongoose.model<ITipTransaction>('TipTransaction', TipTransactionSchema);
