import mongoose, { Document, Schema } from 'mongoose';

export interface IDirectRedirectStat extends Document {
  userId: mongoose.Types.ObjectId;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  countryCode?: string;
  referrer?: string;
}

const DirectRedirectStatSchema = new Schema<IDirectRedirectStat>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true, // Add index for faster queries
  },
  timestamp: {
    type: Date,
    default: Date.now,
    // Note: TTL index is created separately below
  },
  userAgent: {
    type: String,
  },
  ip: {
    type: String,
  },
  countryCode: {
    type: String,
    index: true, // Add index for country-based analytics
  },
  referrer: {
    type: String,
  },
}, {
  timestamps: false, // We're using our own timestamp field
  // Add options for better performance with high-volume data
  capped: {
    size: 1024 * 1024 * 100, // 100MB max size
    max: 1000000, // Maximum 1 million documents
  },
  // Disable _id validation for better performance
  validateBeforeSave: false,
});

// Add TTL index to automatically delete old records after 90 days
DirectRedirectStatSchema.index({ timestamp: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });

// Add compound index for common query patterns
DirectRedirectStatSchema.index({ userId: 1, timestamp: 1 });

// Add compound index for country analytics
DirectRedirectStatSchema.index({ userId: 1, countryCode: 1, timestamp: 1 });

export default mongoose.model<IDirectRedirectStat>('DirectRedirectStat', DirectRedirectStatSchema);
