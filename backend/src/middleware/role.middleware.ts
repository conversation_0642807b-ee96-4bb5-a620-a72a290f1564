import { Request, Response, NextFunction } from 'express';
import User from '../models/User.model';
import { UserRole } from '../models/Invitation.model';

// Middleware to check if the user is the owner of a profile or has owner role
export const isProfileOwner = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get the profile ID from the request params or query
    const profileId = req.params.profileId || req.query.profileId;
    
    if (!profileId) {
      return res.status(400).json({ message: 'Profile ID is required' });
    }
    
    // Check if the user is the profile owner
    if (req.user._id.toString() === profileId) {
      return next();
    }
    
    // Check if the user is a collaborator with owner role
    const profile = await User.findById(profileId);
    
    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }
    
    const isOwnerCollaborator = profile.collaborators?.some(
      c => c.userId.toString() === req.user._id.toString() && c.role === UserRole.OWNER
    );
    
    if (isOwnerCollaborator) {
      return next();
    }
    
    res.status(403).json({ message: 'Not authorized as profile owner' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Middleware to check if the user is a collaborator (any role)
export const isCollaborator = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get the profile ID from the request params or query
    const profileId = req.params.profileId || req.query.profileId;
    
    if (!profileId) {
      return res.status(400).json({ message: 'Profile ID is required' });
    }
    
    // Check if the user is the profile owner
    if (req.user._id.toString() === profileId) {
      return next();
    }
    
    // Check if the user is a collaborator
    const profile = await User.findById(profileId);
    
    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }
    
    const isCollaborator = profile.collaborators?.some(
      c => c.userId.toString() === req.user._id.toString()
    );
    
    if (isCollaborator) {
      return next();
    }
    
    res.status(403).json({ message: 'Not authorized as collaborator' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};
