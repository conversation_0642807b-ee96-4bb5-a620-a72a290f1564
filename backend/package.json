{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "npx nodemon --exec npx ts-node src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "qrcode": "^1.5.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.17", "@types/qrcode": "^1.5.5", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}