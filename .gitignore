# Dependencies
node_modules
*/node_modules
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build outputs
dist
dist-ssr
build
*/dist
*/build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Media uploads
/media
/uploads
/public/uploads
/backend/media
/backend/uploads
