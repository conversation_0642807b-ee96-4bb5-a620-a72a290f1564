{"name": "linktree-clone", "version": "1.0.0", "description": "A full-stack Linktree clone built with React, Node.js, and MongoDB", "main": "index.js", "scripts": {"start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["linktree", "react", "node", "mongodb", "express"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"@radix-ui/react-radio-group": "^1.3.6", "@tiptap/extension-text-align": "^2.12.0", "@types/uuid": "^10.0.0", "react-helmet-async": "^2.0.5", "uuid": "^11.1.0"}}