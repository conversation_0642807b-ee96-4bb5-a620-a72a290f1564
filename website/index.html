<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="HiThere - One Link for Your Entire Digital World. Share all your content, social profiles, and links with a single, customizable page." />
    <title>HiThere - One Link for Your Entire Digital World</title>

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Playfair+Display:wght@400;500;600;700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Playfair+Display:wght@400;500;600;700&display=swap" media="print" onload="this.media='all'" />

    <!-- Critical CSS inlined to prevent render blocking -->
    <style>
      :root {
        --background: 0 0% 0%;
        --foreground: 0 0% 100%;
        --primary: 0 0% 100%;
        --primary-foreground: 0 0% 0%;
      }
      body {
        margin: 0;
        font-family: 'Inter', sans-serif;
        background-color: hsl(var(--background));
        color: hsl(var(--foreground));
      }
      .font-heading {
        font-family: 'Playfair Display', serif;
      }
      .gradient-text {
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        background-image: linear-gradient(to right, hsl(var(--primary)), rgba(255, 255, 255, 0.7));
      }
      /* Loading indicator */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100%;
      }
      .loading-text {
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        color: hsl(var(--primary));
        animation: pulse 1.5s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    </style>

    <!-- Preload critical assets -->
    <link rel="preload" href="/src/main.tsx" as="script" type="module" />
  </head>
  <body>
    <!-- Initial loading state -->
    <div id="loading-indicator" class="loading-container">
      <div class="loading-text">Loading HiThere...</div>
    </div>

    <div id="root"></div>

    <script type="module" src="/src/main.tsx"></script>

    <!-- Remove loading indicator once app is loaded -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Hide loading indicator when app is ready
        window.addEventListener('load', function() {
          document.getElementById('loading-indicator').style.display = 'none';
        });

        // Fallback to hide loading indicator after 2 seconds if load event doesn't fire
        setTimeout(function() {
          document.getElementById('loading-indicator').style.display = 'none';
        }, 2000);
      });
    </script>
  </body>
</html>
