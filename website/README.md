# HiThere Website

This is the landing page website for HiThere, a comprehensive Linktree clone that allows users to create personalized profile pages with various interactive components.

## Features

- Modern, responsive design
- Built with React 18 and TypeScript
- Next.js for Static Site Generation (SSG)
- Tailwind CSS for styling
- Framer Motion for animations
- Lucide React for icons
- SEO optimized with Next.js
- GDPR-compliant cookie consent management
- Conditional Google Tag Manager integration

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the website directory
3. Copy the environment variables file:

```bash
cp .env.example .env
```

4. Edit the `.env` file to add your Google Tag Manager ID if needed
5. Install dependencies:

```bash
npm install
# or
yarn install
```

### Development

To start the development server:

```bash
npm run dev
# or
yarn dev
```

This will start the development server at http://localhost:3001.

### Building for Production

To build the website for production:

```bash
npm run build
# or
yarn build
```

The built files will be in the `.next` directory.

### Static Export

To generate a static export:

```bash
npm run export
# or
yarn export
```

The exported static files will be in the `out` directory.

### Preview Production Build

To preview the production build:

```bash
npm run start
# or
yarn start
```

## Project Structure

```
website/
├── pages/              # Next.js pages
│   ├── _app.tsx        # Custom App component
│   ├── _document.tsx   # Custom Document component
│   ├── index.tsx       # Homepage
│   └── 404.tsx         # 404 page
├── public/             # Static assets
├── src/
│   ├── components/     # Reusable components
│   ├── hooks/          # Custom React hooks
│   ├── utils/          # Utility functions
│   └── index.css       # Global styles
├── docs/               # Documentation
│   └── COOKIE_CONSENT.md  # Cookie consent documentation
├── .next/              # Next.js build output
├── out/                # Static export output
├── next.config.js      # Next.js configuration
├── package.json        # Dependencies and scripts
├── postcss.config.js   # PostCSS configuration
├── tailwind.config.js  # Tailwind CSS configuration
├── tsconfig.json       # TypeScript configuration
└── README.md           # This file
```

## Cookie Consent

This project includes a GDPR-compliant cookie consent system that allows users to manage their preferences for cookies and tracking technologies. For more information, see [COOKIE_CONSENT.md](docs/COOKIE_CONSENT.md).

## License

MIT
