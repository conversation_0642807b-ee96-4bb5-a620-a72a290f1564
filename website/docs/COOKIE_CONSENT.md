# Cookie Consent Implementation

This document explains how the cookie consent system works in the HiThere website.

## Overview

The cookie consent system allows users to manage their preferences for cookies and tracking technologies used on the website. It complies with GDPR and similar privacy regulations by:

1. Showing a consent banner to new visitors
2. Allowing users to accept all, deny all, or customize their preferences
3. Storing consent preferences in localStorage
4. Only loading Google Tag Manager (GTM) when users have consented to analytics cookies

## Components

The system consists of the following components:

- `CookieConsent.tsx`: The main component that displays the consent banner and settings panel
- `GoogleTagManager.tsx`: A component that conditionally loads GTM based on user consent
- `useCookieConsent.ts`: A custom hook that manages consent state and provides utility functions

## Configuration

### Environment Variables

The following environment variables are used:

- `NEXT_PUBLIC_GTM_ID`: Your Google Tag Manager ID
- `NEXT_PUBLIC_COOKIE_CONSENT_ENABLED`: Set to 'true' to enable the cookie consent system

These variables should be set in the `.env` file. An example `.env.example` file is provided.

## Usage

### Basic Usage

The cookie consent system is automatically included in the application via `_app.tsx`. No additional setup is required.

### Checking Consent in Components

If you need to check if a user has consented to a specific type of cookie in your components, you can use the `useCookieConsent` hook:

```tsx
import { useCookieConsent } from '../hooks/useCookieConsent';

function MyComponent() {
  const { hasConsent } = useCookieConsent();
  
  // Check if user has consented to analytics
  if (hasConsent('analytics')) {
    // Do something that requires analytics consent
  }
  
  return <div>My Component</div>;
}
```

### Consent Types

The system supports the following consent types:

- `essential`: Required cookies that are necessary for the website to function
- `marketing`: Cookies used for marketing and advertising
- `analytics`: Cookies used for analytics and performance measurement
- `functional`: Cookies that enable enhanced functionality and personalization

## How It Works

1. When a user visits the site for the first time, the consent banner is displayed
2. The user can choose to:
   - Accept all cookies
   - Deny all cookies (except essential)
   - Customize their preferences
3. The user's choices are saved to localStorage
4. Google Tag Manager is only loaded if the user has consented to analytics cookies
5. On subsequent visits, the banner is not shown unless the user clears their localStorage

## Customization

### Styling

The cookie consent banner and settings panel use Tailwind CSS for styling. You can customize the appearance by modifying the classes in `CookieConsent.tsx`.

### Content

You can customize the text and descriptions by modifying the `consentOptions` array in `CookieConsent.tsx`.

## Testing

To test the cookie consent system:

1. Clear your localStorage (in DevTools: Application > Storage > Local Storage > Right-click > Clear)
2. Reload the page
3. The consent banner should appear
4. Test different consent options and verify that GTM is only loaded when analytics is enabled

## Troubleshooting

If the cookie consent banner is not appearing:

1. Check that `NEXT_PUBLIC_COOKIE_CONSENT_ENABLED` is set to 'true'
2. Clear your localStorage to reset any saved preferences
3. Check the console for any errors

If Google Tag Manager is not loading:

1. Verify that `NEXT_PUBLIC_GTM_ID` is set correctly
2. Check that the user has consented to analytics cookies
3. Look for any errors in the console
