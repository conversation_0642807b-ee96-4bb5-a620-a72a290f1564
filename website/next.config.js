/** @type {import('next').NextConfig} */
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['app.hithere.com'],
    formats: ['image/avif', 'image/webp'],
  },
  // Enable static exports
  output: 'export',
  // Optimize bundle size
  compiler: {
    // Enable tree-shaking for removing unused code
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Configure webpack
  webpack: (config, { dev, isServer }) => {
    // Optimize production builds
    if (!dev) {
      // Enable deterministic module IDs for better caching
      config.optimization.moduleIds = 'deterministic';

      // Optimize chunk splitting
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: 25,
        minSize: 20000,
        cacheGroups: {
          default: false,
          vendors: false,
          // Extract common dependencies
          commons: {
            name: 'commons',
            chunks: 'all',
            minChunks: 2,
            reuseExistingChunk: true,
          },
          // Bundle core libraries separately
          react: {
            name: 'react',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            priority: 40,
          },
          // Bundle framer-motion separately
          framer: {
            name: 'framer-motion',
            chunks: 'all',
            test: /[\\/]node_modules[\\/]framer-motion[\\/]/,
            priority: 30,
          },
          // Bundle utility libraries
          utils: {
            name: 'utils',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](clsx|tailwind-merge)[\\/]/,
            priority: 20,
          },
        },
      };
    }

    return config;
  },
};

module.exports = withBundleAnalyzer(nextConfig);
