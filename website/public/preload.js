/**
 * Preload script for optimizing performance
 * This script runs before the main bundle to preload critical resources
 */

// Preload critical fonts
const preloadFont = (href) => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'font';
  link.href = href;
  link.crossOrigin = 'anonymous';
  document.head.appendChild(link);
};

// Preload critical images
const preloadImage = (src, importance = 'high') => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'image';
  link.href = src;
  link.fetchPriority = importance;
  document.head.appendChild(link);
};

// Preconnect to external domains
const preconnect = (url, crossorigin = true) => {
  const link = document.createElement('link');
  link.rel = 'preconnect';
  link.href = url;
  if (crossorigin) {
    link.crossOrigin = 'anonymous';
  }
  document.head.appendChild(link);
};

// Execute preloading
document.addEventListener('DOMContentLoaded', () => {
  // Preconnect to domains
  preconnect('https://fonts.googleapis.com');
  preconnect('https://fonts.gstatic.com');
  
  // Preload critical fonts
  preloadFont('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
  
  // Preload hero image
  preloadImage('/hero-image.webp');
});
