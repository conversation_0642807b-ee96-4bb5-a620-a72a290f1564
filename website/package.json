{"name": "hithere-website", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "analyze": "ANALYZE=true next build", "export": "next build"}, "dependencies": {"clsx": "^2.1.0", "framer-motion": "^11.0.3", "lucide-react": "^0.330.0", "next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@next/bundle-analyzer": "^14.1.0", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2"}}