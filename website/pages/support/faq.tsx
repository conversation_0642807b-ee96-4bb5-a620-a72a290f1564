import Head from 'next/head'
import Navbar from '../../src/components/Navbar'
import Footer from '../../src/components/Footer'
import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import Link from 'next/link'

export default function FAQ() {
  const [openItem, setOpenItem] = useState<number | null>(0)

  const toggleItem = (index: number) => {
    setOpenItem(openItem === index ? null : index)
  }

  const faqItems = [
    {
      question: "What is HiThere?",
      answer: "HiThere is a comprehensive link-in-bio platform that allows creators, influencers, and businesses to share all their content, social profiles, and links from a single, customizable page. It's designed to be your digital hub, connecting your audience to everything you create and offer online."
    },
    {
      question: "Is HiThere free to use?",
      answer: "Yes, HiThere offers a free plan with essential features to get you started. We also offer premium plans with advanced features like analytics, custom domains, monetization options, and more for users who need additional capabilities."
    },
    {
      question: "How do I create my HiThere page?",
      answer: "Creating your HiThere page is simple! Just sign up for an account, choose your unique username, and start adding components like links, videos, announcements, and more. You can customize your page's appearance with colors, fonts, and backgrounds to match your personal brand."
    },
    {
      question: "Can I customize the appearance of my page?",
      answer: "Absolutely! HiThere offers extensive customization options. You can change your page's background color or image, select custom fonts, adjust your profile layout, and style individual components to create a cohesive look that represents your brand."
    },
    {
      question: "How do I add links to my page?",
      answer: "After logging in to your dashboard, click on the 'Add Component' button and select 'Link' from the options. You can then enter your link title, URL, and customize its appearance. Your new link will appear on your page immediately after saving."
    },
    {
      question: "Can I monetize my HiThere page?",
      answer: "Yes! HiThere offers direct monetization features that allow you to accept payments through providers like Stripe, PayPal, Mollie, and RazorPay. You can sell digital products, accept donations, or offer premium content to your audience directly from your page."
    },
    {
      question: "Does HiThere provide analytics?",
      answer: "Yes, HiThere provides detailed analytics to help you understand how visitors interact with your page. You can track views, clicks, and engagement metrics to optimize your content and strategy. Premium plans offer more advanced analytics features."
    },
    {
      question: "Can I use a custom domain with HiThere?",
      answer: "Yes, premium plans allow you to connect your own custom domain to your HiThere page, giving you a more professional and branded web presence. This feature helps establish credibility and makes your link easier to remember and share."
    },
    {
      question: "How do I add videos to my page?",
      answer: "HiThere supports both uploaded videos and embedded videos from platforms like YouTube and TikTok. To add a video, select the appropriate video component from the 'Add Component' menu in your dashboard, then either upload your video or paste the link to your YouTube or TikTok content."
    },
    {
      question: "Is there a limit to how many links or components I can add?",
      answer: "Free accounts have some limitations on the number of components you can add. Premium plans offer increased or unlimited components, allowing you to showcase all your content without restrictions."
    }
  ]

  return (
    <>
      <Head>
        <title>Frequently Asked Questions | HiThere</title>
        <meta name="description" content="Find answers to common questions about HiThere, our features, pricing, and how to make the most of your link-in-bio page." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow pt-16">
          <section className="section bg-gradient-to-br from-background via-primary-50 to-secondary-50">
            <div className="container-custom">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text text-center">
                Frequently Asked Questions
              </h1>
              <p className="text-xl mb-12 text-foreground/80 font-light text-center max-w-3xl mx-auto">
                Find answers to common questions about HiThere and how to make the most of your link-in-bio page.
              </p>
            </div>
          </section>

          <section className="section bg-background">
            <div className="container-custom max-w-3xl">
              <div className="space-y-4">
                {faqItems.map((item, index) => (
                  <div key={index} className="card backdrop-blur-sm overflow-hidden">
                    <button
                      className="w-full flex justify-between items-center p-6 text-left"
                      onClick={() => toggleItem(index)}
                    >
                      <h3 className="text-xl font-bold font-heading text-primary">{item.question}</h3>
                      <div className="ml-4">
                        {openItem === index ? (
                          <ChevronUp className="w-5 h-5 text-primary" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-primary" />
                        )}
                      </div>
                    </button>
                    <div
                      className={`px-6 overflow-hidden transition-all duration-300 ease-in-out ${
                        openItem === index ? 'max-h-96 pb-6' : 'max-h-0'
                      }`}
                    >
                      <p className="text-foreground/80">{item.answer}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-16 text-center">
                <h2 className="text-2xl font-bold mb-4 font-heading text-primary">Still have questions?</h2>
                <p className="mb-6 text-foreground/80">
                  We're here to help! Reach out to our support team for assistance.
                </p>
                <Link
                  href="/company/contact"
                  className="btn btn-primary"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  )
}
