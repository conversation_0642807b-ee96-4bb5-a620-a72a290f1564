import Head from 'next/head'
import Navbar from '../../src/components/Navbar'
import Footer from '../../src/components/Footer'
import { BookOpen, FileText, HelpCircle, MessageCircle, Video } from 'lucide-react'
import Link from 'next/link'

export default function Assistance() {
  return (
    <>
      <Head>
        <title>Assistance | HiThere</title>
        <meta name="description" content="Get help with your HiThere account. Find resources, tutorials, and support options to resolve any issues you may encounter." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow pt-16">
          <section className="section bg-gradient-to-br from-background via-primary-50 to-secondary-50">
            <div className="container-custom">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text text-center">
                How Can We Help?
              </h1>
              <p className="text-xl mb-12 text-foreground/80 font-light text-center max-w-3xl mx-auto">
                Find the support you need to make the most of your HiThere experience.
              </p>
            </div>
          </section>

          <section className="section bg-background">
            <div className="container-custom max-w-6xl">
              <div className="grid md:grid-cols-2 gap-8 mb-16">
                <div className="card backdrop-blur-sm p-8">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <HelpCircle className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Frequently Asked Questions
                      </h2>
                      <p className="mb-6 text-foreground/80">
                        Find quick answers to common questions about HiThere features, account management, billing, and more.
                      </p>
                      <Link href="/support/faq" className="btn btn-outline">
                        Browse FAQs
                      </Link>
                    </div>
                  </div>
                </div>

                <div className="card backdrop-blur-sm p-8">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <BookOpen className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Getting Started Guide
                      </h2>
                      <p className="mb-6 text-foreground/80">
                        New to HiThere? Our step-by-step guide will help you set up your page and make the most of all our features.
                      </p>
                      <Link href="/support/getting-started" className="btn btn-outline">
                        Read Guide
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card backdrop-blur-sm p-8 mb-16">
                <h2 className="text-3xl font-bold mb-8 font-heading text-primary text-center">
                  Video Tutorials
                </h2>
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="bg-background/10 rounded-lg overflow-hidden">
                    <div className="aspect-video bg-primary/5 relative flex items-center justify-center">
                      <Video className="w-12 h-12 text-primary/50" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center cursor-pointer hover:bg-primary/30 transition-colors">
                          <div className="w-0 h-0 border-t-8 border-b-8 border-l-12 border-transparent border-l-primary ml-1"></div>
                        </div>
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-bold mb-2 font-heading text-primary">
                        Setting Up Your Profile
                      </h3>
                      <p className="text-sm text-foreground/70">
                        Learn how to create and customize your HiThere profile to reflect your personal brand.
                      </p>
                    </div>
                  </div>

                  <div className="bg-background/10 rounded-lg overflow-hidden">
                    <div className="aspect-video bg-primary/5 relative flex items-center justify-center">
                      <Video className="w-12 h-12 text-primary/50" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center cursor-pointer hover:bg-primary/30 transition-colors">
                          <div className="w-0 h-0 border-t-8 border-b-8 border-l-12 border-transparent border-l-primary ml-1"></div>
                        </div>
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-bold mb-2 font-heading text-primary">
                        Adding Components
                      </h3>
                      <p className="text-sm text-foreground/70">
                        Discover how to add and customize different components like links, videos, and lead forms.
                      </p>
                    </div>
                  </div>

                  <div className="bg-background/10 rounded-lg overflow-hidden">
                    <div className="aspect-video bg-primary/5 relative flex items-center justify-center">
                      <Video className="w-12 h-12 text-primary/50" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center cursor-pointer hover:bg-primary/30 transition-colors">
                          <div className="w-0 h-0 border-t-8 border-b-8 border-l-12 border-transparent border-l-primary ml-1"></div>
                        </div>
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-bold mb-2 font-heading text-primary">
                        Monetization Features
                      </h3>
                      <p className="text-sm text-foreground/70">
                        Learn how to set up payment options and start earning from your HiThere page.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-8 mb-16">
                <div className="card backdrop-blur-sm p-8">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <FileText className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Documentation
                      </h2>
                      <p className="mb-6 text-foreground/80">
                        Explore our comprehensive documentation for detailed information about all HiThere features and capabilities.
                      </p>
                      <a href="#" className="btn btn-outline">
                        View Documentation
                      </a>
                    </div>
                  </div>
                </div>

                <div className="card backdrop-blur-sm p-8">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <MessageCircle className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Contact Support
                      </h2>
                      <p className="mb-6 text-foreground/80">
                        Need personalized help? Our support team is ready to assist you with any questions or issues.
                      </p>
                      <Link href="/company/contact" className="btn btn-outline">
                        Contact Us
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card backdrop-blur-sm p-8 text-center">
                <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                  Community Support
                </h2>
                <p className="mb-6 text-foreground/80 max-w-2xl mx-auto">
                  Join our community of HiThere users to share tips, get inspiration, and connect with others who are building their digital presence.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a href="#" className="btn btn-outline">
                    Join Discord
                  </a>
                  <a href="#" className="btn btn-outline">
                    Facebook Group
                  </a>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  )
}
