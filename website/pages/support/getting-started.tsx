import Head from 'next/head'
import Navbar from '../../src/components/Navbar'
import Footer from '../../src/components/Footer'
import { CheckCircle } from 'lucide-react'

export default function GettingStarted() {
  return (
    <>
      <Head>
        <title>Getting Started | HiThere</title>
        <meta name="description" content="Learn how to set up and optimize your HiThere page with our step-by-step guide for beginners." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow pt-16">
          <section className="section bg-gradient-to-br from-background via-primary-50 to-secondary-50">
            <div className="container-custom">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text text-center">
                Getting Started with HiThere
              </h1>
              <p className="text-xl mb-12 text-foreground/80 font-light text-center max-w-3xl mx-auto">
                A step-by-step guide to creating and optimizing your link-in-bio page.
              </p>
            </div>
          </section>

          <section className="section bg-background">
            <div className="container-custom max-w-4xl">
              <div className="space-y-16">
                {/* Step 1 */}
                <div className="card backdrop-blur-sm">
                  <div className="flex items-start gap-6">
                    <div className="step-number">1</div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Create Your Account
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        Sign up for HiThere using your email address or social media accounts. Choose a unique username that represents you or your brand – this will become your custom URL (hithere.com/yourusername).
                      </p>
                      <div className="mt-6 space-y-3">
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Visit <a href="https://app.hithere.com/register" className="text-primary hover:underline">app.hithere.com/register</a> to create your account</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Choose a memorable username that aligns with your brand</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Verify your email address to secure your account</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="card backdrop-blur-sm">
                  <div className="flex items-start gap-6">
                    <div className="step-number">2</div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Set Up Your Profile
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        Personalize your profile with a profile picture, banner image, and bio. These elements help visitors recognize your brand and understand what you're all about.
                      </p>
                      <div className="mt-6 space-y-3">
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Upload a clear profile picture (recommended size: 400x400px)</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Add a banner image or choose a background color</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Write a concise bio that explains who you are and what you do</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="card backdrop-blur-sm">
                  <div className="flex items-start gap-6">
                    <div className="step-number">3</div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Add Your Components
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        Start adding components to your page. Begin with the most important links and content you want to showcase to your audience.
                      </p>
                      <div className="mt-6 space-y-3">
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Add links to your website, social media profiles, and other important pages</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Include videos from YouTube, TikTok, or upload your own</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Create announcements for important updates or promotions</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Add lead capture forms to grow your email list</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 4 */}
                <div className="card backdrop-blur-sm">
                  <div className="flex items-start gap-6">
                    <div className="step-number">4</div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Customize Your Page
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        Make your page stand out by customizing its appearance to match your personal brand or aesthetic.
                      </p>
                      <div className="mt-6 space-y-3">
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Choose colors that reflect your brand identity</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Select fonts that enhance readability and style</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Arrange components in a logical order of importance</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 5 */}
                <div className="card backdrop-blur-sm">
                  <div className="flex items-start gap-6">
                    <div className="step-number">5</div>
                    <div>
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Share Your Page
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        Now that your page is ready, it's time to share it with your audience across all your platforms.
                      </p>
                      <div className="mt-6 space-y-3">
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Add your HiThere link to all your social media bios</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Share your page in posts and stories to increase visibility</p>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="w-5 h-5 text-primary mr-3" />
                          <p className="text-foreground/80">Include your link in email signatures and marketing materials</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-16 text-center">
                <h2 className="text-2xl font-bold mb-4 font-heading text-primary">Ready to Get Started?</h2>
                <p className="mb-6 text-foreground/80">
                  Create your HiThere page today and start connecting with your audience in a more meaningful way.
                </p>
                <a
                  href="https://app.hithere.com/register"
                  className="btn btn-primary btn-lg"
                >
                  Create Your Page
                </a>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  )
}
