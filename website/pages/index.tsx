import { lazy, Suspense } from 'react'
import { CheckCircle2, Globe, InstagramIcon, TwitterIcon } from '../src/utils/icons'
import { LazyMotion, domAnimation } from '../src/utils/motion'
import Head from 'next/head'
import Navbar from '../src/components/Navbar'
import Footer from '../src/components/Footer'

// Lazy load the Features and HowItWorks sections
const FeaturesSection = lazy(() => import('../src/components/FeaturesSection'))
const HowItWorksSection = lazy(() => import('../src/components/HowItWorksSection'))
const CTASection = lazy(() => import('../src/components/CTASection'))

export default function Home() {
  return (
    <>
      <Head>
        <title>HiThere - One Link for Your Entire Digital World</title>
        <meta name="description" content="Share all your content, social profiles, and links with a single, customizable page. Perfect for creators, influencers, and businesses." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <LazyMotion features={domAnimation}>
            <div className="pt-16">
              {/* Hero Section - Optimized for LCP */}
              <section className="section bg-gradient-to-br from-background via-primary-50 to-secondary-50 overflow-hidden">
                <div className="container-custom relative">
                  <div className="grid md:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
                    <div className="text-left">
                      <h1 className="text-4xl md:text-6xl font-bold mb-6 font-heading gradient-text">
                        One Link for Your Entire Digital World
                      </h1>
                      <p className="text-xl mb-8 text-foreground/80 font-light">
                        Share all your content, social profiles, and links with a single, customizable page. Perfect for creators, influencers, and businesses.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4">
                        <a
                          href="https://app.hithere.com/register"
                          className="btn btn-primary btn-lg rounded-full shadow-lg hover:shadow-xl transition-all"
                        >
                          Get Started Free
                        </a>
                        <a
                          href="#how-it-works"
                          className="btn btn-outline btn-lg rounded-full hover:bg-secondary/10 transition-all"
                        >
                          Learn More
                        </a>
                      </div>
                      <div className="mt-8 flex items-center text-sm text-foreground/60">
                        <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                        <span>No credit card required • Free forever plan available</span>
                      </div>
                    </div>

                    <div className="relative">
                      <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/30 rounded-full filter blur-xl opacity-70"></div>
                      <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-secondary/30 rounded-full filter blur-xl opacity-70"></div>

                      {/* Device mockup - Simplified for better performance */}
                      <div className="relative z-10 bg-white rounded-3xl shadow-2xl overflow-hidden border-8 border-white">
                        <div className="aspect-[9/16] bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl overflow-hidden">
                          {/* Profile mockup content - Simplified */}
                          <div className="h-1/3 bg-gradient-to-r from-primary-500 to-secondary-500 relative">
                            <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 w-24 h-24 rounded-full border-4 border-white bg-primary-200 flex items-center justify-center">
                              <span className="text-2xl font-bold text-primary-700">JD</span>
                            </div>
                          </div>
                          <div className="pt-16 px-6 text-center">
                            <h3 className="text-xl font-bold">Jane Doe</h3>
                            <p className="text-sm text-gray-600 mb-4">Digital Creator & Influencer</p>

                            {/* Link buttons - Simplified SVGs */}
                            <div className="space-y-3">
                              <div className="bg-white rounded-lg shadow-md p-3 flex items-center">
                                <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center mr-3">
                                  <Globe className="w-4 h-4 text-primary-600" />
                                </div>
                                <span className="text-sm font-medium">My Website</span>
                              </div>
                              <div className="bg-white rounded-lg shadow-md p-3 flex items-center">
                                <div className="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                                  <InstagramIcon />
                                </div>
                                <span className="text-sm font-medium">Instagram</span>
                              </div>
                              <div className="bg-white rounded-lg shadow-md p-3 flex items-center">
                                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                  <TwitterIcon />
                                </div>
                                <span className="text-sm font-medium">Twitter</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Lazy load the rest of the sections */}
              <Suspense fallback={<div className="h-20 bg-white"></div>}>
                <FeaturesSection />
              </Suspense>

              <Suspense fallback={<div className="h-20 bg-gray-50"></div>}>
                <HowItWorksSection />
              </Suspense>

              <Suspense fallback={<div className="h-20 bg-white"></div>}>
                <CTASection />
              </Suspense>
            </div>
          </LazyMotion>
        </main>
        <Footer />
      </div>
    </>
  )
}
