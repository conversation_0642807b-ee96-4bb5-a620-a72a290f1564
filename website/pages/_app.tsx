import { AppProps } from 'next/app'
import Head from 'next/head'
import dynamic from 'next/dynamic'
import '../src/index.css'

// Dynamically import components to avoid SSR issues
const CookieConsent = dynamic(() => import('../src/components/CookieConsent'), {
  ssr: false,
})

const GoogleTagManager = dynamic(() => import('../src/components/GoogleTagManager'), {
  ssr: false,
})

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="description" content="HiThere - One Link for Your Entire Digital World" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <Component {...pageProps} />
      <CookieConsent />
      {process.env.NEXT_PUBLIC_GTM_ID && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
      )}
    </>
  )
}
