import Head from 'next/head'
import Navbar from '../../src/components/Navbar'
import Footer from '../../src/components/Footer'

export default function WhatsNew() {
  return (
    <>
      <Head>
        <title>What's New | HiThere</title>
        <meta name="description" content="Stay updated with the latest features, improvements, and news about HiThere." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow pt-16">
          <section className="py-12 md:py-20 bg-gradient-to-br from-background via-primary-50 to-secondary-50">
            <div className="container-custom">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text text-center">
                What's New
              </h1>
              <p className="text-xl mb-6 text-foreground/80 font-light text-center max-w-3xl mx-auto">
                Stay updated with the latest features, improvements, and news about HiThere.
              </p>
            </div>
          </section>

          <section className="py-12 md:py-20 bg-background">
            <div className="container-custom max-w-4xl">
              <div className="space-y-12">
                {/* Latest Update */}
                <div className="card backdrop-blur-sm">
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:w-1/4">
                      <div className="text-sm text-primary/70 mb-1">March 15, 2023</div>
                      <div className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                        New Feature
                      </div>
                    </div>
                    <div className="md:w-3/4">
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Embedded Video Component
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        We're excited to announce our new Embedded Video component, supporting YouTube and TikTok videos with advanced features like password protection, autoplay options, and customizable thumbnails.
                      </p>
                      <p className="mb-4 text-foreground/80">
                        This new component makes it easier than ever to share your video content with your audience, all while maintaining the sleek, professional look of your HiThere page.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Previous Update */}
                <div className="card backdrop-blur-sm">
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:w-1/4">
                      <div className="text-sm text-primary/70 mb-1">February 28, 2023</div>
                      <div className="inline-block px-3 py-1 rounded-full bg-secondary/10 text-secondary-foreground text-xs font-medium">
                        Enhancement
                      </div>
                    </div>
                    <div className="md:w-3/4">
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Rich Text Editor for Bio
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        We've upgraded the Profile Bio section with a rich text editor that supports formatting options like bold, italic, headings, and lists. Now you can express yourself with more style and clarity.
                      </p>
                      <p className="mb-4 text-foreground/80">
                        The formatted text displays beautifully in both the editor and on your live profile, ensuring a consistent experience for you and your visitors.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Older Update */}
                <div className="card backdrop-blur-sm mb-8">
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:w-1/4">
                      <div className="text-sm text-primary/70 mb-1">January 15, 2023</div>
                      <div className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                        New Feature
                      </div>
                    </div>
                    <div className="md:w-3/4">
                      <h2 className="text-2xl font-bold mb-4 font-heading text-primary">
                        Direct Monetization
                      </h2>
                      <p className="mb-4 text-foreground/80">
                        Turn your HiThere page into a revenue stream with our new Direct Monetization feature. Integrate with popular payment providers including Stripe, PayPal, Mollie, and RazorPay to sell digital products, accept donations, or offer premium content.
                      </p>
                      <p className="mb-4 text-foreground/80">
                        Customizable thank you pages and seamless payment flows make it easy for your audience to support you financially.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  )
}
