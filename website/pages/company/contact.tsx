import Head from 'next/head'
import Navbar from '../../src/components/Navbar'
import Footer from '../../src/components/Footer'
import { Mail, MapPin, Phone } from 'lucide-react'

export default function Contact() {
  return (
    <>
      <Head>
        <title>Contact Us | HiThere</title>
        <meta name="description" content="Get in touch with the HiThere team. We're here to help with any questions or feedback you may have." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow pt-16">
          <section className="py-12 md:py-20 bg-gradient-to-br from-background via-primary-50 to-secondary-50">
            <div className="container-custom">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text text-center">
                Contact Us
              </h1>
              <p className="text-xl mb-6 text-foreground/80 font-light text-center max-w-3xl mx-auto">
                We'd love to hear from you. Reach out with any questions, feedback, or inquiries.
              </p>
            </div>
          </section>

          <section className="py-12 md:py-20 bg-background">
            <div className="container-custom max-w-6xl">
              <div className="grid md:grid-cols-3 gap-8 mb-12">
                <div className="card backdrop-blur-sm text-center p-6">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 font-heading text-primary">Email Us</h3>
                  <p className="text-foreground/80 mb-4">
                    For general inquiries and support
                  </p>
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    <EMAIL>
                  </a>
                </div>

                <div className="card backdrop-blur-sm text-center p-6">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 font-heading text-primary">Call Us</h3>
                  <p className="text-foreground/80 mb-4">
                    Monday to Friday, 9am to 5pm EST
                  </p>
                  <a href="tel:******-123-4567" className="text-primary hover:underline">
                    +****************
                  </a>
                </div>

                <div className="card backdrop-blur-sm text-center p-6">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <MapPin className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 font-heading text-primary">Visit Us</h3>
                  <p className="text-foreground/80 mb-4">
                    Our headquarters
                  </p>
                  <address className="not-italic text-primary">
                    123 Tech Plaza<br />
                    San Francisco, CA 94107
                  </address>
                </div>
              </div>

              <div className="card backdrop-blur-sm p-6 mb-8">
                <h2 className="text-2xl font-bold mb-6 font-heading text-primary text-center">Send Us a Message</h2>
                <form className="max-w-2xl mx-auto">
                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-foreground/80 mb-2">
                        Your Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        className="w-full px-4 py-2 rounded-md border border-primary/20 bg-background/5 text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                        placeholder="John Doe"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-foreground/80 mb-2">
                        Your Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        className="w-full px-4 py-2 rounded-md border border-primary/20 bg-background/5 text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>
                  <div className="mb-6">
                    <label htmlFor="subject" className="block text-sm font-medium text-foreground/80 mb-2">
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      className="w-full px-4 py-2 rounded-md border border-primary/20 bg-background/5 text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder="How can we help you?"
                      required
                    />
                  </div>
                  <div className="mb-6">
                    <label htmlFor="message" className="block text-sm font-medium text-foreground/80 mb-2">
                      Message
                    </label>
                    <textarea
                      id="message"
                      rows={5}
                      className="w-full px-4 py-2 rounded-md border border-primary/20 bg-background/5 text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder="Your message here..."
                      required
                    ></textarea>
                  </div>
                  <div className="text-center">
                    <button
                      type="submit"
                      className="btn btn-primary btn-lg"
                    >
                      Send Message
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  )
}
