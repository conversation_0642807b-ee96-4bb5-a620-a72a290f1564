import { useState } from 'react'
import Head from 'next/head'
import Navbar from '../src/components/Navbar'
import Footer from '../src/components/Footer'
import { LazyMotion, domAnimation, m } from '../src/utils/motion'

// Dati di esempio per le card di showcase (estesi)
const showcaseProfiles = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Photographer',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    banner: 'bg-gradient-to-r from-blue-500 to-purple-600',
    style: 'dark',
    links: ['Portfolio', 'Instagram', 'Book a Session'],
    url: '/showcase/alex-johnson',
    description: 'Professional photographer specializing in portrait and landscape photography.'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Digital Artist',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    banner: 'bg-gradient-to-r from-pink-500 to-orange-400',
    style: 'gradient',
    links: ['Art Gallery', 'Commission Work', 'Tutorials'],
    url: '/showcase/sophia-lee',
    description: 'Creating vibrant digital art and illustrations for clients worldwide.'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Fitness Coach',
    image: 'https://randomuser.me/api/portraits/men/85.jpg',
    banner: 'bg-gradient-to-r from-green-400 to-teal-500',
    style: 'light',
    links: ['Workout Plans', 'Nutrition Guide', 'Book a Session'],
    url: '/showcase/michael-chen',
    description: 'Helping people achieve their fitness goals through personalized training programs.'
  },
  {
    id: 4,
    name: 'Emma Wilson',
    role: 'Fashion Designer',
    image: 'https://randomuser.me/api/portraits/women/68.jpg',
    banner: 'bg-gradient-to-r from-purple-600 to-indigo-600',
    style: 'dark',
    links: ['Latest Collection', 'Shop Now', 'Fashion Blog'],
    url: '/showcase/emma-wilson',
    description: 'Creating sustainable fashion with a focus on ethical production methods.'
  },
  {
    id: 5,
    name: 'David Kim',
    role: 'Music Producer',
    image: 'https://randomuser.me/api/portraits/men/42.jpg',
    banner: 'bg-gradient-to-r from-red-500 to-yellow-500',
    style: 'gradient',
    links: ['Listen Now', 'Upcoming Shows', 'Contact'],
    url: '/showcase/david-kim',
    description: 'Producing electronic music and collaborating with artists around the world.'
  },
  {
    id: 6,
    name: 'Olivia Martinez',
    role: 'Travel Blogger',
    image: 'https://randomuser.me/api/portraits/women/29.jpg',
    banner: 'bg-gradient-to-r from-cyan-500 to-blue-500',
    style: 'light',
    links: ['Travel Guides', 'Photo Gallery', 'Book Recommendations'],
    url: '/showcase/olivia-martinez',
    description: 'Sharing travel tips and adventures from destinations around the globe.'
  },
  {
    id: 7,
    name: 'James Taylor',
    role: 'Chef & Food Writer',
    image: 'https://randomuser.me/api/portraits/men/22.jpg',
    banner: 'bg-gradient-to-r from-amber-500 to-red-600',
    style: 'dark',
    links: ['Recipes', 'Cooking Classes', 'Restaurant Reviews'],
    url: '/showcase/james-taylor',
    description: 'Creating delicious recipes and sharing culinary knowledge with food enthusiasts.'
  },
  {
    id: 8,
    name: 'Ava Thompson',
    role: 'Yoga Instructor',
    image: 'https://randomuser.me/api/portraits/women/57.jpg',
    banner: 'bg-gradient-to-r from-purple-400 to-pink-400',
    style: 'gradient',
    links: ['Classes', 'Meditation Guides', 'Wellness Tips'],
    url: '/showcase/ava-thompson',
    description: 'Teaching yoga and mindfulness practices for a balanced and healthy lifestyle.'
  },
  {
    id: 9,
    name: 'Ryan Garcia',
    role: 'Tech Reviewer',
    image: 'https://randomuser.me/api/portraits/men/55.jpg',
    banner: 'bg-gradient-to-r from-gray-700 to-gray-900',
    style: 'dark',
    links: ['Latest Reviews', 'YouTube Channel', 'Tech News'],
    url: '/showcase/ryan-garcia',
    description: 'Reviewing the latest gadgets and technology products with honest opinions.'
  },
  {
    id: 10,
    name: 'Lily Wang',
    role: 'Illustrator',
    image: 'https://randomuser.me/api/portraits/women/79.jpg',
    banner: 'bg-gradient-to-r from-yellow-400 to-orange-500',
    style: 'light',
    links: ['Portfolio', 'Shop', 'Commission Info'],
    url: '/showcase/lily-wang',
    description: 'Creating whimsical illustrations for books, products, and digital media.'
  },
  {
    id: 11,
    name: 'Marcus Johnson',
    role: 'Personal Trainer',
    image: 'https://randomuser.me/api/portraits/men/33.jpg',
    banner: 'bg-gradient-to-r from-blue-600 to-indigo-700',
    style: 'gradient',
    links: ['Training Programs', 'Fitness Tips', 'Contact Me'],
    url: '/showcase/marcus-johnson',
    description: 'Helping clients transform their bodies and improve their overall health.'
  },
  {
    id: 12,
    name: 'Zoe Rodriguez',
    role: 'Makeup Artist',
    image: 'https://randomuser.me/api/portraits/women/38.jpg',
    banner: 'bg-gradient-to-r from-pink-600 to-purple-500',
    style: 'dark',
    links: ['Portfolio', 'Book Now', 'Tutorials'],
    url: '/showcase/zoe-rodriguez',
    description: 'Creating stunning makeup looks for special events, photoshoots, and tutorials.'
  }
]

// Componente per la card del profilo
const ProfileCard = ({ profile }: { profile: typeof showcaseProfiles[0] }) => {
  const getBgColor = () => {
    switch (profile.style) {
      case 'dark':
        return 'bg-gray-900 text-white'
      case 'light':
        return 'bg-gray-100 text-gray-900'
      case 'gradient':
        return `${profile.banner} text-white`
      default:
        return 'bg-gray-900 text-white'
    }
  }

  const getLinkBgColor = () => {
    switch (profile.style) {
      case 'dark':
        return 'bg-gray-800 hover:bg-gray-700'
      case 'light':
        return 'bg-white hover:bg-gray-200 text-gray-900 border border-gray-300'
      case 'gradient':
        return 'bg-white/20 backdrop-blur-sm hover:bg-white/30'
      default:
        return 'bg-gray-800 hover:bg-gray-700'
    }
  }

  return (
    <div className="showcase-card rounded-xl overflow-hidden shadow-xl transition-all duration-300 hover:shadow-2xl transform hover:-translate-y-1 h-full">
      <div className={`w-full h-full flex flex-col ${getBgColor()}`}>
        {/* Banner */}
        <div className={`h-24 w-full ${profile.style === 'gradient' ? '' : profile.banner}`}></div>
        
        {/* Profile info */}
        <div className="relative px-4 pt-14 pb-4 flex-1 flex flex-col">
          <div className="absolute left-1/2 transform -translate-x-1/2 -top-10">
            <div className="w-20 h-20 rounded-full border-4 border-white overflow-hidden">
              <img 
                src={profile.image} 
                alt={profile.name} 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          
          <div className="text-center mb-4">
            <h3 className="font-bold text-lg">{profile.name}</h3>
            <p className={`text-sm ${profile.style === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
              {profile.role}
            </p>
            <p className={`text-xs mt-2 ${profile.style === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              {profile.description}
            </p>
          </div>
          
          {/* Links */}
          <div className="flex-1 flex flex-col gap-2 mt-2">
            {profile.links.map((link, index) => (
              <div 
                key={index}
                className={`py-2 px-3 rounded-md text-center text-sm transition-colors ${getLinkBgColor()}`}
              >
                {link}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Filtri per la pagina showcase
const styleFilters = [
  { id: 'all', label: 'All Styles' },
  { id: 'dark', label: 'Dark Theme' },
  { id: 'light', label: 'Light Theme' },
  { id: 'gradient', label: 'Gradient Theme' }
]

export default function Showcase() {
  const [activeFilter, setActiveFilter] = useState('all')
  
  const filteredProfiles = activeFilter === 'all' 
    ? showcaseProfiles 
    : showcaseProfiles.filter(profile => profile.style === activeFilter)

  return (
    <>
      <Head>
        <title>Showcase - HiThere</title>
        <meta name="description" content="Explore beautiful profile examples created with HiThere. Get inspired and create your own personalized page." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <LazyMotion features={domAnimation}>
            <div className="pt-16">
              {/* Hero Section */}
              <section className="section bg-background relative overflow-hidden">
                {/* Elementi decorativi fluttuanti */}
                <div className="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float">
                  <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
                </div>
                <div className="absolute bottom-40 left-10 w-24 h-24 opacity-20 animate-float-delayed">
                  <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
                </div>
                
                <div className="container-custom">
                  <div className="text-center max-w-3xl mx-auto mb-12">
                    <m.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <h1 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text">
                        Showcase Gallery
                      </h1>
                      <p className="text-xl text-primary/80">
                        Get inspired by these beautiful profiles created with HiThere
                      </p>
                    </m.div>
                  </div>

                  {/* Filtri */}
                  <div className="flex justify-center mb-12">
                    <div className="inline-flex bg-gray-100 rounded-lg p-1">
                      {styleFilters.map(filter => (
                        <button
                          key={filter.id}
                          onClick={() => setActiveFilter(filter.id)}
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                            activeFilter === filter.id
                              ? 'bg-primary text-white'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                        >
                          {filter.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Grid di profili */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                    {filteredProfiles.map(profile => (
                      <m.div
                        key={profile.id}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5 }}
                      >
                        <ProfileCard profile={profile} />
                      </m.div>
                    ))}
                  </div>
                </div>
              </section>
            </div>
          </LazyMotion>
        </main>
        <Footer />
      </div>
    </>
  )
}
