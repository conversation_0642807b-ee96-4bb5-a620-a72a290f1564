/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#ffffff',
          50: '#ffffff',
          100: '#f8f8f8',
          200: '#f0f0f0',
          300: '#e0e0e0',
          400: '#d0d0d0',
          500: '#c0c0c0',
          600: '#a0a0a0',
          700: '#808080',
          800: '#606060',
          900: '#404040',
          950: '#202020',
        },
        secondary: {
          DEFAULT: '#ffffff',
          50: '#ffffff',
          100: '#f8f8f8',
          200: '#f0f0f0',
          300: '#e0e0e0',
          400: '#d0d0d0',
          500: '#c0c0c0',
          600: '#a0a0a0',
          700: '#808080',
          800: '#606060',
          900: '#404040',
          950: '#202020',
        },
        accent: {
          DEFAULT: '#ffffff',
          50: '#ffffff',
          100: '#f8f8f8',
          200: '#f0f0f0',
          300: '#e0e0e0',
          400: '#d0d0d0',
          500: '#c0c0c0',
          600: '#a0a0a0',
          700: '#808080',
          800: '#606060',
          900: '#404040',
          950: '#202020',
        },
        background: '#000000',
        foreground: '#ffffff',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        heading: ['Playfair Display', 'serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'slide-in-right': 'slideInRight 0.5s ease-out',
        'bounce-slow': 'bounce 3s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
