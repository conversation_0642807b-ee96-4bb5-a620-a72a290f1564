import { useState, useEffect } from 'react';

// Define cookie consent types
export type ConsentType = 'essential' | 'marketing' | 'analytics' | 'functional';

// Define cookie consent state
export interface CookieConsentState {
  essential: boolean;
  marketing: boolean;
  analytics: boolean;
  functional: boolean;
  hasConsented: boolean;
}

// Default consent state
const defaultConsentState: CookieConsentState = {
  essential: true, // Essential cookies are always enabled
  marketing: false,
  analytics: false,
  functional: false,
  hasConsented: false,
};

/**
 * Custom hook for managing cookie consent
 */
export function useCookieConsent() {
  const [consent, setConsent] = useState<CookieConsentState>(defaultConsentState);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load consent settings from localStorage on mount
  useEffect(() => {
    const savedConsent = localStorage.getItem('cookieConsent');
    
    if (savedConsent) {
      try {
        const parsedConsent = JSON.parse(savedConsent) as CookieConsentState;
        setConsent(parsedConsent);
      } catch (error) {
        console.error('Error parsing saved cookie consent:', error);
      }
    }
    
    setIsLoaded(true);
  }, []);

  // Function to save consent settings
  const saveConsent = (newConsent: Partial<CookieConsentState>) => {
    const updatedConsent = {
      ...consent,
      ...newConsent,
      hasConsented: true,
    };

    // Save to localStorage
    localStorage.setItem('cookieConsent', JSON.stringify(updatedConsent));
    setConsent(updatedConsent);

    return updatedConsent;
  };

  // Function to accept all cookies
  const acceptAll = () => {
    return saveConsent({
      marketing: true,
      analytics: true,
      functional: true,
    });
  };

  // Function to deny all cookies (except essential)
  const denyAll = () => {
    return saveConsent({
      marketing: false,
      analytics: false,
      functional: false,
    });
  };

  // Function to toggle a specific consent type
  const toggleConsent = (type: ConsentType) => {
    if (type === 'essential') return consent; // Essential cookies cannot be toggled
    
    return saveConsent({
      [type]: !consent[type],
    });
  };

  // Function to check if a specific consent type is enabled
  const hasConsent = (type: ConsentType) => {
    return consent[type];
  };

  // Function to check if user has made a consent choice
  const hasConsentChoice = () => {
    return consent.hasConsented;
  };

  return {
    consent,
    isLoaded,
    acceptAll,
    denyAll,
    toggleConsent,
    saveConsent,
    hasConsent,
    hasConsentChoice,
  };
}
