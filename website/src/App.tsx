import { lazy, Suspense } from 'react'
import Navbar from './components/Navbar'
import Footer from './components/Footer'

// Lazy load HomePage component
const HomePage = lazy(() => import('./pages/HomePage'))

function App() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <Suspense fallback={
          <div className="flex items-center justify-center min-h-[80vh]">
            <div className="animate-pulse text-2xl font-heading text-primary">Loading...</div>
          </div>
        }>
          <HomePage />
        </Suspense>
      </main>
      <Footer />
    </div>
  )
}

export default App
