// Web Vitals reporting utility
// Simple implementation without external dependencies

type MetricName = 'CLS' | 'FID' | 'LCP' | 'FCP' | 'TTFB';

interface WebVitalsMetric {
  name: MetricName;
  value: number;
  delta?: number;
  id?: string;
}

type ReportHandler = (metric: WebVitalsMetric) => void;

// Function to report Web Vitals
export const reportWebVitals = (onReport: ReportHandler): void => {
  // Report First Contentful Paint
  if ('performance' in window) {
    // Use Performance Observer when available
    if ('PerformanceObserver' in window) {
      try {
        // Create observer for LCP
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            onReport({
              name: 'LCP',
              value: lastEntry.startTime,
            });
          }
        });

        // Start observing LCP
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

        // Create observer for FCP
        const fcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const firstEntry = entries[0];
          if (firstEntry) {
            onReport({
              name: 'FCP',
              value: firstEntry.startTime,
            });
          }
        });

        // Start observing FCP
        fcpObserver.observe({ type: 'paint', buffered: true });
      } catch (e) {
        console.error('Failed to observe performance metrics:', e);
      }
    }

    // Report TTFB using Navigation Timing API
    setTimeout(() => {
      const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navEntry) {
        onReport({
          name: 'TTFB',
          value: navEntry.responseStart,
        });
      }
    }, 0);
  }
};
