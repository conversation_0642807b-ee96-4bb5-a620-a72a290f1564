import { lazy, Suspense } from 'react'
// Icons are used in the components that are lazy loaded

// Import only the essential motion component
import { LazyMotion, domAnimation, m } from 'framer-motion'

// Lazy load the sections
const FeaturesSection = lazy(() => import('../components/FeaturesSection'))
const HowItWorksSection = lazy(() => import('../components/HowItWorksSection'))
const ComponentsPreviewSection = lazy(() => import('../components/ComponentsPreviewSection'))
const ProfileExamplesSection = lazy(() => import('../components/ProfileExamplesSection'))
const CTASection = lazy(() => import('../components/CTASection'))
const PortfolioSection = lazy(() => import('../components/PortfolioSection'))
const ShowcaseSection = lazy(() => import('../components/ShowcaseSection'))
const ComponentShowcaseSection = lazy(() => import('../components/ComponentShowcaseSection'))

const HomePage = () => {
  return (
    <LazyMotion features={domAnimation}>
      <div className="pt-16">
        {/* Hero Section - Statamic-inspired with floating elements */}
        <section className="section bg-background overflow-hidden relative">
          {/* Floating elements - similar to Statamic's design */}
          <div className="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float">
            <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
          </div>
          <div className="absolute bottom-40 left-10 w-24 h-24 opacity-20 animate-float-delayed">
            <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
          </div>

          <div className="container-custom relative">
            <div className="flex flex-col items-center text-center max-w-4xl mx-auto">
              <m.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-5xl md:text-7xl font-bold mb-6 font-heading text-primary"
              >
                <span className="gradient-text">The answer</span> to your digital presence needs.
              </m.h1>
              <m.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-xl mb-8 text-primary/80 font-light max-w-2xl"
              >
                Showcase everything in one refined space with a powerful, customizable profile page.
              </m.p>
              <m.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-4 mb-16"
              >
                <a
                  href="https://app.hithere.com/register"
                  className="btn btn-primary btn-lg text-base"
                >
                  Create Your Page
                </a>
                <a
                  href="https://app.hithere.com/demo"
                  className="btn btn-outline btn-lg text-base"
                >
                  Try the Demo
                </a>
              </m.div>

              {/* Phone mockup with animation */}
              <m.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="relative"
              >
                <div className="phone-mockup">
                  <div className="bg-gray-900 h-full w-full flex flex-col">
                    {/* Profile header */}
                    <div className="relative">
                      <div className="w-full h-40 bg-gray-800"></div>
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center">
                        <div className="w-20 h-20 rounded-full bg-gray-700 mb-2 overflow-hidden">
                          <img
                            src="https://randomuser.me/api/portraits/women/44.jpg"
                            alt="Profile"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h3 className="text-white text-lg font-medium">Olivia Smith</h3>
                        <p className="text-gray-400 text-xs">Content Creator</p>
                      </div>
                    </div>

                    {/* Links */}
                    <div className="flex-1 px-4 pt-16 pb-4 flex flex-col gap-3 overflow-y-auto">
                      <div className="w-full py-3 px-4 bg-gray-800 rounded-md text-white text-center">
                        About Me
                      </div>
                      <div className="w-full py-3 px-4 bg-gray-800 rounded-md text-white text-center">
                        YouTube
                      </div>
                      <div className="w-full py-3 px-4 bg-gray-800 rounded-md text-white text-center">
                        Blog
                      </div>
                      <div className="w-full py-3 px-4 bg-gray-800 rounded-md text-white text-center">
                        Shop
                      </div>

                      {/* Social icons */}
                      <div className="flex justify-center gap-4 mt-4">
                        <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                          </svg>
                        </div>
                        <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                          </svg>
                        </div>
                        <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                          </svg>
                        </div>
                        <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-2 16h-2v-6h2v6zm-1-6.891c-.607 0-1.1-.496-1.1-1.109 0-.612.492-1.109 1.1-1.109s1.1.497 1.1 1.109c0 .613-.493 1.109-1.1 1.109zm8 6.891h-1.998v-2.861c0-1.881-2.002-1.722-2.002 0v2.861h-2v-6h2v1.093c.872-1.616 4-1.736 4 1.548v3.359z"/>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Statamic-inspired floating elements */}
                <div className="absolute -right-16 top-1/4 w-32 h-32 opacity-30 animate-float-delayed">
                  <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
                </div>
                <div className="absolute -left-12 bottom-1/4 w-24 h-24 opacity-30 animate-float">
                  <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
                </div>
              </m.div>
            </div>
          </div>
        </section>

        {/* Trusted by section - Statamic-inspired */}
        <section className="py-12 bg-background">
          <div className="container-custom">
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-center mb-8"
            >
              <p className="text-primary/60 text-sm">Powering websites from startups to personal blogs and portfolios</p>
            </m.div>
            <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16">
              <div className="w-24 h-12 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
                <div className="text-primary font-bold">Company 1</div>
              </div>
              <div className="w-24 h-12 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
                <div className="text-primary font-bold">Company 2</div>
              </div>
              <div className="w-24 h-12 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
                <div className="text-primary font-bold">Company 3</div>
              </div>
              <div className="w-24 h-12 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
                <div className="text-primary font-bold">Company 4</div>
              </div>
              <div className="w-24 h-12 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
                <div className="text-primary font-bold">Company 5</div>
              </div>
            </div>
          </div>
        </section>

        {/* Lazy load the rest of the sections */}
        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <FeaturesSection />
        </Suspense>

        {/* Portfolio Section - Keeping the portfolio section */}
        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <PortfolioSection />
        </Suspense>

        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <HowItWorksSection />
        </Suspense>

        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <ComponentsPreviewSection />
        </Suspense>

        {/* Component Showcase Section - Scrolling component cards */}
        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <ComponentShowcaseSection />
        </Suspense>

        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <ProfileExamplesSection />
        </Suspense>

        {/* Showcase Section - Statamic-inspired with scrolling cards */}
        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <ShowcaseSection />
        </Suspense>

        <Suspense fallback={<div className="h-20 bg-background"></div>}>
          <CTASection />
        </Suspense>
      </div>
    </LazyMotion>
  )
}

export default HomePage
