import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'
import './index.css'

// Performance measurement
const startTime = performance.now()

// Create root with error handling
const rootElement = document.getElementById('root')

if (!rootElement) {
  throw new Error('Root element not found')
}

// Create root with concurrent mode
const root = createRoot(rootElement)

// Render app with error boundary
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)

// Log performance metrics
const endTime = performance.now()
console.log(`Initial render time: ${(endTime - startTime).toFixed(2)}ms`)

// Report Web Vitals in development
if (process.env.NODE_ENV !== 'production') {
  import('./utils/reportWebVitals').then(({ reportWebVitals }) => {
    reportWebVitals(console.log)
  })
}
