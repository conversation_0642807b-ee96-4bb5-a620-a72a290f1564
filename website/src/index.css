@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-full text-sm font-medium transition-colors
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-400 focus-visible:ring-offset-2
    disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary text-background hover:bg-primary/80 h-10 py-2 px-6 border border-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-background hover:bg-secondary/80 h-10 py-2 px-6 border border-secondary;
  }

  .btn-outline {
    @apply border border-primary bg-transparent text-primary hover:bg-primary/10 h-10 py-2 px-6;
  }

  .btn-lg {
    @apply h-12 px-10 text-base;
  }

  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section {
    @apply py-16 md:py-32;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70;
  }

  .card {
    @apply rounded-xl border border-primary/20 bg-background/5 backdrop-blur-sm p-6;
  }

  .phone-mockup {
    @apply relative mx-auto w-[280px] h-[560px] rounded-[40px] border-[10px] border-gray-900 overflow-hidden shadow-2xl;
  }

  .phone-mockup-small {
    @apply relative mx-auto w-[180px] h-[360px] rounded-[30px] border-[8px] border-gray-900 overflow-hidden shadow-xl;
  }

  .step-number {
    @apply w-12 h-12 rounded-full border border-primary/30 flex items-center justify-center text-xl font-heading text-primary mb-4;
  }

  /* Statamic-inspired animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float 6s ease-in-out 2s infinite;
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Showcase card hover effect */
  .showcase-card {
    transition: all 0.3s ease;
  }

  .showcase-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}
