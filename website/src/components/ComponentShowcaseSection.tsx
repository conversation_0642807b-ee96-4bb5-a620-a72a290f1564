import { m } from '../utils/motion'
import { useState, useRef, useEffect } from 'react'

// Dati di esempio per i componenti di showcase
const showcaseComponents = [
  {
    id: 1,
    name: 'Link Component',
    description: 'Standard clickable links with customizable titles and optional icons.',
    image: '/images/components/link-component.webp',
    category: 'basic',
    variants: [
      {
        name: 'Social Media Links',
        preview: (
          <div className="flex flex-col gap-2 w-full">
            <div className="w-full py-3 px-4 bg-blue-600 rounded-md text-white text-center flex items-center">
              <div className="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                </svg>
              </div>
              <span>Twitter</span>
            </div>
            <div className="w-full py-3 px-4 bg-pink-600 rounded-md text-white text-center flex items-center">
              <div className="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                </svg>
              </div>
              <span>Instagram</span>
            </div>
            <div className="w-full py-3 px-4 bg-red-600 rounded-md text-white text-center flex items-center">
              <div className="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                </svg>
              </div>
              <span>YouTube</span>
            </div>
          </div>
        )
      }
    ]
  },
  {
    id: 2,
    name: 'Post Component',
    description: 'Text-based announcements with customizable background and text colors.',
    image: '/images/components/announcement-component.webp',
    category: 'content',
    variants: [
      {
        name: 'Announcement Card',
        preview: (
          <div className="w-full p-4 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-md text-white">
            <h3 className="font-bold text-lg mb-2">New Album Release!</h3>
            <p className="text-sm">Check out my latest album "Digital Dreams" now available on all streaming platforms.</p>
            <div className="mt-3 text-xs font-medium bg-white/20 py-1 px-3 rounded inline-block">
              Listen Now
            </div>
          </div>
        )
      }
    ]
  },
  {
    id: 3,
    name: 'Card Component',
    description: 'Swipeable card stack with images, titles, and links that users can browse through.',
    image: '/images/components/shuffle-component.webp',
    category: 'interactive',
    variants: [
      {
        name: 'Image Cards',
        preview: (
          <div className="relative w-full h-48 overflow-hidden rounded-md">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70"></div>
            <img
              src="https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2l0eXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60"
              alt="City view"
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-0 left-0 p-4 text-white">
              <h3 className="font-bold text-lg">Tokyo Travel Guide</h3>
              <p className="text-sm text-white/80">Everything you need to know for your trip</p>
            </div>
          </div>
        )
      }
    ]
  },
  {
    id: 4,
    name: 'Mini Lead Component',
    description: 'Lead capture forms with customizable fields to collect visitor information.',
    image: '/images/components/minilead-component.webp',
    category: 'interactive',
    variants: [
      {
        name: 'Contact Form',
        preview: (
          <div className="w-full p-4 bg-white rounded-md shadow-md">
            <h3 className="font-bold text-gray-800 mb-3">Join My Newsletter</h3>
            <div className="space-y-3">
              <input type="text" placeholder="Your Name" className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" />
              <input type="email" placeholder="Your Email" className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" />
              <button className="w-full py-2 bg-blue-600 text-white rounded-md text-sm font-medium">
                Subscribe
              </button>
            </div>
          </div>
        )
      }
    ]
  },
  {
    id: 5,
    name: 'Video Component',
    description: 'Video component with thumbnail display and playback controls.',
    image: '/images/components/video-component.webp',
    category: 'media',
    variants: [
      {
        name: 'Landscape Video',
        preview: (
          <div className="w-full rounded-md overflow-hidden relative">
            <div className="aspect-video bg-gray-900 relative">
              <img
                src="https://images.unsplash.com/photo-1618329027137-a520b57c6606?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8eW91dHViZSUyMHRodW1ibmFpbHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60"
                alt="Video thumbnail"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 rounded-full bg-white/90 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-900" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )
      }
    ]
  },
  {
    id: 6,
    name: 'Embedded Component',
    description: 'YouTube and TikTok video embedding with customizable controls.',
    image: '/images/components/embedded-component.webp',
    category: 'media',
    variants: [
      {
        name: 'YouTube Embed',
        preview: (
          <div className="w-full rounded-md overflow-hidden">
            <div className="aspect-video bg-gray-900 relative">
              <div className="absolute inset-0 flex items-center justify-center flex-col">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-600 mb-2" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                </svg>
                <span className="text-white text-sm">YouTube Video</span>
              </div>
            </div>
          </div>
        )
      }
    ]
  },
  {
    id: 7,
    name: 'Payment Component',
    description: 'Direct monetization through multiple payment providers.',
    image: '/images/components/payment-component.webp',
    category: 'monetization',
    variants: [
      {
        name: 'Stripe Payment',
        preview: (
          <div className="w-full p-4 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-md text-white">
            <h3 className="font-bold text-lg mb-2">Premium Content Access</h3>
            <p className="text-sm mb-3">Get exclusive access to my premium content for just $9.99/month.</p>
            <button className="w-full py-2 bg-white text-indigo-700 rounded-md text-sm font-medium">
              Subscribe Now
            </button>
          </div>
        )
      }
    ]
  }
]

// Componente per la card del componente
const ComponentCard = ({ component }: { component: typeof showcaseComponents[0] }) => {
  return (
    <div className="showcase-card rounded-xl overflow-hidden shadow-xl transition-all duration-300 hover:shadow-2xl transform hover:-translate-y-1 bg-white dark:bg-gray-800">
      <div className="p-5">
        <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2">{component.name}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{component.description}</p>

        <div className="bg-gray-100 dark:bg-gray-900 rounded-lg p-4">
          {component.variants[0].preview}
        </div>
      </div>
    </div>
  )
}

const ComponentShowcaseSection = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showLeftArrow, setShowLeftArrow] = useState(false)
  const [showRightArrow, setShowRightArrow] = useState(true)

  // Gestione dello scroll orizzontale
  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current
      setShowLeftArrow(scrollLeft > 0)
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10) // 10px di tolleranza
    }
  }

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll)
      // Verifica iniziale
      handleScroll()
      return () => scrollContainer.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: 'smooth' })
    }
  }

  return (
    <section className="section bg-background relative overflow-hidden">
      {/* Elementi decorativi fluttuanti */}
      <div className="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-40 left-10 w-24 h-24 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>

      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text">
              Powerful Components
            </h2>
            <p className="text-xl text-primary/80">
              Build your page with our versatile, customizable components
            </p>
          </m.div>
        </div>

        {/* Showcase Carousel */}
        <div className="relative">
          {/* Freccia sinistra */}
          {showLeftArrow && (
            <button
              onClick={scrollLeft}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 backdrop-blur-sm rounded-full p-3 shadow-lg"
              aria-label="Scroll left"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}

          {/* Contenitore scrollabile */}
          <div
            ref={scrollContainerRef}
            className="flex overflow-x-auto pb-8 hide-scrollbar snap-x snap-mandatory"
            style={{ scrollbarWidth: 'none' }}
          >
            <div className="flex gap-6 px-4">
              {showcaseComponents.map((component) => (
                <div key={component.id} className="w-[320px] flex-shrink-0 snap-center">
                  <ComponentCard component={component} />
                </div>
              ))}
            </div>
          </div>

          {/* Freccia destra */}
          {showRightArrow && (
            <button
              onClick={scrollRight}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 backdrop-blur-sm rounded-full p-3 shadow-lg"
              aria-label="Scroll right"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <a href="https://app.hithere.com/register" className="btn btn-primary">
            Start Building Your Page
          </a>
        </div>
      </div>
    </section>
  )
}

export default ComponentShowcaseSection
