import { useState, useEffect, useCallback } from 'react'
import { Menu, X } from '../utils/icons'
import Link from 'next/link'

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Optimize scroll handler with useCallback
  const handleScroll = useCallback(() => {
    // Use requestAnimationFrame to throttle scroll events
    requestAnimationFrame(() => {
      setIsScrolled(window.scrollY > 10)
    })
  }, [])

  useEffect(() => {
    // Add passive option to improve scroll performance
    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  // Toggle mobile menu with memoized callback
  const toggleMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(prev => !prev)
  }, [])

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-background/80 backdrop-blur-md border-b border-primary/10' : 'bg-transparent'
      }`}
    >
      <div className="container-custom py-5 flex items-center justify-between">
        <Link href="/" className="flex items-center">
          <span className="text-2xl font-bold font-heading text-primary">Link in Bio</span>
        </Link>

        {/* Desktop Navigation - Statamic-inspired */}
        <nav className="hidden md:flex items-center space-x-6">
          {/* Developers Dropdown */}
          <div className="relative group">
            <button className="text-primary/80 hover:text-primary transition-colors text-sm flex items-center">
              Developers
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div className="absolute left-0 mt-2 w-64 bg-background/95 backdrop-blur-md border border-primary/10 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
              <div className="py-4 px-4">
                <Link href="/documentation" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Documentation</div>
                  <div className="text-xs text-primary/60">Learn how to build and customize your pages.</div>
                </Link>
                <Link href="/tutorials" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Tutorials</div>
                  <div className="text-xs text-primary/60">Watch tutorials and see feature demos.</div>
                </Link>
              </div>
            </div>
          </div>

          {/* Features Link */}
          <a href="#features" className="text-primary/80 hover:text-primary transition-colors text-sm">
            Features
          </a>

          {/* Product Dropdown */}
          <div className="relative group">
            <button className="text-primary/80 hover:text-primary transition-colors text-sm flex items-center">
              Product
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div className="absolute left-0 mt-2 w-64 bg-background/95 backdrop-blur-md border border-primary/10 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
              <div className="py-4 px-4">
                <Link href="/blog" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Blog</div>
                  <div className="text-xs text-primary/60">Latest news, tips, and updates.</div>
                </Link>
                <a href="#features" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Features</div>
                  <div className="text-xs text-primary/60">Loaded with all the features you need.</div>
                </a>
                <Link href="/support" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Support</div>
                  <div className="text-xs text-primary/60">Get help when you need it.</div>
                </Link>
              </div>
            </div>
          </div>

          {/* Portfolio Link - Keeping the portfolio section */}
          <a href="#portfolio" className="text-primary/80 hover:text-primary transition-colors text-sm">
            Portfolio
          </a>

          {/* Showcase Link */}
          <Link href="/showcase" className="text-primary/80 hover:text-primary transition-colors text-sm">
            Showcase
          </Link>

          {/* Components Link */}
          <Link href="/components" className="text-primary/80 hover:text-primary transition-colors text-sm">
            Components
          </Link>

          {/* Community Dropdown */}
          <div className="relative group">
            <button className="text-primary/80 hover:text-primary transition-colors text-sm flex items-center">
              Community
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div className="absolute left-0 mt-2 w-64 bg-background/95 backdrop-blur-md border border-primary/10 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
              <div className="py-4 px-4">
                <Link href="/discord" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Discord Chat</div>
                  <div className="text-xs text-primary/60">Join our community on Discord.</div>
                </Link>
                <Link href="/testimonials" className="block py-3 text-primary/80 hover:text-primary text-sm group">
                  <div className="font-medium mb-1">Testimonials</div>
                  <div className="text-xs text-primary/60">See what others are saying.</div>
                </Link>
              </div>
            </div>
          </div>

          {/* Auth Buttons */}
          <div className="flex items-center space-x-3 ml-2">
            <a
              href="https://app.hithere.com/login"
              className="btn btn-outline text-sm"
            >
              Sign In
            </a>
            <a
              href="https://app.hithere.com/register"
              className="btn btn-primary text-sm"
            >
              Create Your Page
            </a>
          </div>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-primary"
          onClick={toggleMobileMenu}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Navigation - Statamic-inspired */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-background/95 backdrop-blur-md border-b border-primary/10">
          <div className="container-custom py-6 flex flex-col space-y-5">
            {/* Developers Section */}
            <div className="py-2">
              <div className="text-primary/80 font-medium text-sm mb-2">Developers</div>
              <div className="pl-4 flex flex-col space-y-3">
                <Link
                  href="/documentation"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Documentation
                </Link>
                <Link
                  href="/tutorials"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Tutorials
                </Link>
              </div>
            </div>

            {/* Features Link */}
            <a
              href="#features"
              className="text-primary/80 hover:text-primary transition-colors py-2 text-sm"
              onClick={toggleMobileMenu}
            >
              Features
            </a>

            {/* Product Section */}
            <div className="py-2">
              <div className="text-primary/80 font-medium text-sm mb-2">Product</div>
              <div className="pl-4 flex flex-col space-y-3">
                <Link
                  href="/blog"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Blog
                </Link>
                <a
                  href="#features"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Features
                </a>
                <Link
                  href="/support"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Support
                </Link>
              </div>
            </div>

            {/* Portfolio Link */}
            <a
              href="#portfolio"
              className="text-primary/80 hover:text-primary transition-colors py-2 text-sm"
              onClick={toggleMobileMenu}
            >
              Portfolio
            </a>

            {/* Showcase Link */}
            <Link
              href="/showcase"
              className="text-primary/80 hover:text-primary transition-colors py-2 text-sm"
              onClick={toggleMobileMenu}
            >
              Showcase
            </Link>

            {/* Components Link */}
            <Link
              href="/components"
              className="text-primary/80 hover:text-primary transition-colors py-2 text-sm"
              onClick={toggleMobileMenu}
            >
              Components
            </Link>

            {/* Community Section */}
            <div className="py-2">
              <div className="text-primary/80 font-medium text-sm mb-2">Community</div>
              <div className="pl-4 flex flex-col space-y-3">
                <Link
                  href="/discord"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Discord Chat
                </Link>
                <Link
                  href="/testimonials"
                  className="text-primary/70 hover:text-primary transition-colors py-1 text-sm"
                  onClick={toggleMobileMenu}
                >
                  Testimonials
                </Link>
              </div>
            </div>

            <div className="flex flex-col space-y-3 pt-3">
              <a
                href="https://app.hithere.com/login"
                className="btn btn-outline w-full justify-center text-sm"
              >
                Sign In
              </a>
              <a
                href="https://app.hithere.com/register"
                className="btn btn-primary w-full justify-center text-sm"
              >
                Create Your Page
              </a>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}

export default Navbar
