import { m } from '../utils/motion'

const HowItWorksSection = () => {
  return (
    <section id="how-it-works" className="section bg-background relative overflow-hidden">
      {/* Statamic-inspired floating elements */}
      <div className="absolute top-20 left-10 w-32 h-32 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-40 right-10 w-24 h-24 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>

      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading text-primary">
              From setup to success in <span className="gradient-text">minutes</span>
            </h2>
            <p className="text-xl text-primary/80">
              Our intuitive platform makes it easy to create and share your digital presence.
            </p>
          </m.div>
        </div>

        {/* Statamic-inspired steps with code-like display */}
        <div className="max-w-5xl mx-auto mb-16">
          <div className="bg-background/30 backdrop-blur-sm border border-primary/10 rounded-xl overflow-hidden">
            <div className="flex items-center px-4 py-2 border-b border-primary/10">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-primary/20"></div>
                <div className="w-3 h-3 rounded-full bg-primary/20"></div>
                <div className="w-3 h-3 rounded-full bg-primary/20"></div>
              </div>
              <div className="ml-4 text-primary/60 text-sm">How it works</div>
            </div>

            <div className="p-6 md:p-8">
              {/* Step 1 */}
              <m.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="mb-8 flex flex-col md:flex-row gap-6 items-start"
              >
                <div className="step-number flex-shrink-0">1</div>
                <div>
                  <h3 className="text-2xl font-bold mb-3 font-heading text-primary">Sign Up & Create</h3>
                  <p className="text-primary/80 mb-4">
                    Create your account and set up your profile with your brand colors, fonts, and images.
                  </p>
                  <div className="bg-background/20 p-4 rounded-md border border-primary/10">
                    <code className="text-sm text-primary/90 font-mono">
                      <span className="text-primary/50">{"// Create your profile"}</span><br />
                      {"const "}<span className="text-primary">{"profile"}</span>{" = new "}<span className="text-primary">{"Profile"}</span>({'{'}
                      <br />
                      {"  username: "}<span className="text-primary/70">{'"yourname"'}</span>,<br />
                      {"  theme: "}<span className="text-primary/70">{'"custom"'}</span>,<br />
                      {"  bio: "}<span className="text-primary/70">{'"Your awesome bio here"'}</span><br />
                      {'}'});
                    </code>
                  </div>
                </div>
              </m.div>

              {/* Step 2 */}
              <m.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-8 flex flex-col md:flex-row gap-6 items-start"
              >
                <div className="step-number flex-shrink-0">2</div>
                <div>
                  <h3 className="text-2xl font-bold mb-3 font-heading text-primary">Add Your Content</h3>
                  <p className="text-primary/80 mb-4">
                    Add links, videos, music, social profiles, and more to showcase your digital world.
                  </p>
                  <div className="bg-background/20 p-4 rounded-md border border-primary/10">
                    <code className="text-sm text-primary/90 font-mono">
                      <span className="text-primary/50">{"// Add components to your page"}</span><br />
                      {"profile."}<span className="text-primary">{"addComponent"}</span>({'{'}
                      <br />
                      {"  type: "}<span className="text-primary/70">{'"link"'}</span>,<br />
                      {"  title: "}<span className="text-primary/70">{'"My Website"'}</span>,<br />
                      {"  url: "}<span className="text-primary/70">{'"https://example.com"'}</span><br />
                      {'}'});
                    </code>
                  </div>
                </div>
              </m.div>

              {/* Step 3 */}
              <m.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex flex-col md:flex-row gap-6 items-start"
              >
                <div className="step-number flex-shrink-0">3</div>
                <div>
                  <h3 className="text-2xl font-bold mb-3 font-heading text-primary">Share & Analyze</h3>
                  <p className="text-primary/80 mb-4">
                    Share your unique link across all platforms and track performance with analytics.
                  </p>
                  <div className="bg-background/20 p-4 rounded-md border border-primary/10">
                    <code className="text-sm text-primary/90 font-mono">
                      <span className="text-primary/50">{"// Share and track results"}</span><br />
                      {"const "}<span className="text-primary">{"url"}</span>{" = "}<span className="text-primary/70">{'"hithere.com/"'}</span>{" + profile.username;"}<br />
                      <br />
                      <span className="text-primary">{"Analytics"}</span>{".track({"}{'}'}
                      <br />
                      {"  views: "}<span className="text-primary/70">{"true"}</span>,<br />
                      {"  clicks: "}<span className="text-primary/70">{"true"}</span>,<br />
                      {"  conversions: "}<span className="text-primary/70">{"true"}</span><br />
                      {'}'});
                    </code>
                  </div>
                </div>
              </m.div>
            </div>
          </div>
        </div>

        {/* Get Started Button */}
        <div className="text-center">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <a
              href="https://app.hithere.com/register"
              className="btn btn-primary btn-lg text-base"
            >
              Get Started Now
            </a>
          </m.div>
        </div>
      </div>
    </section>
  )
}

export default HowItWorksSection
