import { m } from '../utils/motion'

const PortfolioSection = () => {
  return (
    <section id="portfolio" className="section bg-background">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading text-primary">
              Our Portfolio
            </h2>
            <p className="text-xl text-primary/80">
              Explore some of our recent work and success stories.
            </p>
          </m.div>
        </div>

        {/* Portfolio Grid - Statamic-inspired layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {/* Portfolio Item 1 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="card group overflow-hidden"
          >
            <div className="relative overflow-hidden rounded-lg mb-4">
              <img
                src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80"
                alt="Portfolio Item 1"
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <span className="btn btn-primary">View Project</span>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 font-heading text-primary">Creative Agency Website</h3>
            <p className="text-primary/80 text-sm">
              A modern website for a creative agency with a focus on portfolio showcase and client acquisition.
            </p>
          </m.div>

          {/* Portfolio Item 2 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="card group overflow-hidden"
          >
            <div className="relative overflow-hidden rounded-lg mb-4">
              <img
                src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80"
                alt="Portfolio Item 2"
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <span className="btn btn-primary">View Project</span>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 font-heading text-primary">E-commerce Platform</h3>
            <p className="text-primary/80 text-sm">
              A comprehensive e-commerce solution with advanced product filtering and secure checkout.
            </p>
          </m.div>

          {/* Portfolio Item 3 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="card group overflow-hidden"
          >
            <div className="relative overflow-hidden rounded-lg mb-4">
              <img
                src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80"
                alt="Portfolio Item 3"
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <span className="btn btn-primary">View Project</span>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 font-heading text-primary">Mobile App Design</h3>
            <p className="text-primary/80 text-sm">
              A sleek, intuitive mobile application design for a fitness tracking platform.
            </p>
          </m.div>
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-16">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <a href="/portfolio" className="btn btn-outline">
              View All Projects
            </a>
          </m.div>
        </div>
      </div>
    </section>
  )
}

export default PortfolioSection
