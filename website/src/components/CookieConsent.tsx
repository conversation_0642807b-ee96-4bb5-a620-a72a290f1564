import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { cn } from '../utils/cn';
import { useCookieConsent, ConsentType } from '../hooks/useCookieConsent';

interface ConsentOption {
  id: ConsentType;
  label: string;
  description: string;
  required?: boolean;
}

// Define cookie consent options
const consentOptions: ConsentOption[] = [
  {
    id: 'essential',
    label: 'Essential',
    description: 'These cookies are necessary for the website to function and cannot be switched off.',
    required: true,
  },
  {
    id: 'marketing',
    label: 'Marketing',
    description: 'These cookies are used to track advertising effectiveness and to show you relevant ads.',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    description: 'These cookies help us understand how visitors interact with our website.',
  },
  {
    id: 'functional',
    label: 'Functional',
    description: 'These cookies enable enhanced functionality and personalization.',
  },
];

// Cookie consent component
const CookieConsent = () => {
  // Use the cookie consent hook
  const {
    consent,
    isLoaded,
    acceptAll,
    denyAll,
    toggleConsent,
    saveConsent,
    hasConsentChoice
  } = useCookieConsent();

  // State for showing detailed settings
  const [showSettings, setShowSettings] = useState(false);
  // State for showing the consent banner
  const [showBanner, setShowBanner] = useState(false);

  // Determine if we should show the banner
  useEffect(() => {
    if (isLoaded) {
      setShowBanner(!hasConsentChoice());
    }
  }, [isLoaded, hasConsentChoice]);

  // Handle accept all
  const handleAcceptAll = () => {
    acceptAll();
    setShowBanner(false);
  };

  // Handle deny all (except essential)
  const handleDeny = () => {
    denyAll();
    setShowBanner(false);
  };

  // Handle save settings
  const handleSaveSettings = () => {
    saveConsent({
      marketing: consent.marketing,
      analytics: consent.analytics,
      functional: consent.functional,
    });
    setShowBanner(false);
  };

  // Handle toggle consent
  const handleToggleConsent = (type: ConsentType) => {
    if (type === 'essential') return; // Essential cookies cannot be toggled
    toggleConsent(type);
  };

  // If cookie consent is disabled in environment, don't show the banner
  if (process.env.NEXT_PUBLIC_COOKIE_CONSENT_ENABLED !== 'true') {
    return null;
  }

  // If user has already consented or the hook hasn't loaded yet, don't show the banner
  if (!showBanner || !isLoaded) {
    return null;
  }

  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          transition={{ duration: 0.3 }}
          className="fixed bottom-4 left-0 right-0 z-50 mx-auto max-w-4xl px-4"
        >
          <div className="rounded-lg bg-white shadow-lg ring-1 ring-gray-200 dark:bg-gray-900 dark:ring-gray-800">
            {!showSettings ? (
              // Simple consent banner
              <div className="p-6">
                <p className="mb-4 text-base text-gray-700 dark:text-gray-300">
                  This site uses tracking technologies. You may opt in or opt out of the use of these technologies.
                </p>
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={() => handleDeny()}
                    className="rounded-full border border-gray-300 bg-white px-6 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                  >
                    Deny
                  </button>
                  <button
                    onClick={() => handleAcceptAll()}
                    className="rounded-full border border-gray-300 bg-white px-6 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                  >
                    Accept all
                  </button>
                  <button
                    onClick={() => setShowSettings(true)}
                    className="rounded-full bg-black px-6 py-2 text-sm font-medium text-white hover:bg-gray-800 dark:bg-white dark:text-black dark:hover:bg-gray-200"
                  >
                    Consent Settings
                  </button>
                </div>
              </div>
            ) : (
              // Detailed settings panel
              <div className="p-6">
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Your Privacy</h2>
                    <button
                      onClick={() => setShowSettings(false)}
                      className="rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
                    >
                      <X size={20} />
                    </button>
                  </div>
                  <p className="mt-2 text-base text-gray-700 dark:text-gray-300">
                    This site uses tracking technologies. You may opt in or opt out of the use of these technologies.
                  </p>
                </div>

                <div className="mb-8 space-y-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                  {consentOptions.map((option) => (
                    <div key={option.id} className="flex items-center justify-between border-b border-gray-100 pb-4 last:border-0 last:pb-0 dark:border-gray-800">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{option.label}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{option.description}</p>
                      </div>
                      <div className={cn("relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                        option.required ? "bg-green-500" : consent[option.id] ? "bg-green-500" : "bg-gray-200 dark:bg-gray-700")}>
                        <span
                          className={cn(
                            "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                            option.required || consent[option.id] ? "translate-x-6" : "translate-x-1"
                          )}
                        />
                        <input
                          type="checkbox"
                          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                          checked={option.required || consent[option.id]}
                          onChange={() => handleToggleConsent(option.id)}
                          disabled={option.required}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex flex-wrap justify-between gap-3">
                  <div>
                    <button
                      onClick={() => handleDeny()}
                      className="mr-3 rounded-full border border-gray-300 bg-white px-6 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                      Deny
                    </button>
                    <button
                      onClick={() => handleAcceptAll()}
                      className="rounded-full border border-gray-300 bg-white px-6 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                      Accept all
                    </button>
                  </div>
                  <button
                    onClick={handleSaveSettings}
                    className="rounded-full bg-black px-6 py-2 text-sm font-medium text-white hover:bg-gray-800 dark:bg-white dark:text-black dark:hover:bg-gray-200"
                  >
                    Save
                  </button>
                </div>

                <div className="mt-4 text-center text-xs text-gray-500 dark:text-gray-400">
                  Read how we handle your data in our{' '}
                  <a href="/privacy-policy" className="underline hover:text-gray-700 dark:hover:text-gray-300">
                    Privacy Policy
                  </a>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CookieConsent;
