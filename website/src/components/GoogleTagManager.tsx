import { useEffect } from 'react';

interface GoogleTagManagerProps {
  gtmId: string;
}

/**
 * GoogleTagManager component that conditionally loads GTM based on cookie consent
 */
const GoogleTagManager = ({ gtmId }: GoogleTagManagerProps) => {
  useEffect(() => {
    // Check if user has consented to analytics cookies
    const savedConsent = localStorage.getItem('cookieConsent');
    
    if (!savedConsent) {
      console.log('No cookie consent found, GTM not initialized');
      return;
    }

    try {
      const consent = JSON.parse(savedConsent);
      
      // Only initialize GTM if user has consented to analytics
      if (consent.hasConsented && consent.analytics) {
        // Add Google Tag Manager script
        const script = document.createElement('script');
        script.innerHTML = `
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${gtmId}');
        `;
        document.head.appendChild(script);

        // Add Google Tag Manager noscript iframe
        const noscript = document.createElement('noscript');
        const iframe = document.createElement('iframe');
        iframe.src = `https://www.googletagmanager.com/ns.html?id=${gtmId}`;
        iframe.height = '0';
        iframe.width = '0';
        iframe.style.display = 'none';
        iframe.style.visibility = 'hidden';
        noscript.appendChild(iframe);
        document.body.insertBefore(noscript, document.body.firstChild);

        console.log('Google Tag Manager initialized with ID:', gtmId);
      } else {
        console.log('User has not consented to analytics, GTM not initialized');
      }
    } catch (error) {
      console.error('Error parsing cookie consent:', error);
    }
  }, [gtmId]);

  return null; // This component doesn't render anything
};

export default GoogleTagManager;
