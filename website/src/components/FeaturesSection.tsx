import { m } from '../utils/motion'

const FeaturesSection = () => {
  return (
    <section id="features" className="section bg-background relative overflow-hidden">
      {/* Statamic-inspired floating elements */}
      <div className="absolute top-40 right-10 w-32 h-32 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-20 left-10 w-24 h-24 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>

      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading text-primary">
              Enjoy a platform designed to <span className="gradient-text">adapt to you</span>
            </h2>
            <p className="text-xl text-primary/80">
              Powerful features that make managing your online presence simple and effective.
            </p>
          </m.div>
        </div>

        {/* Feature Grid - Statamic-inspired */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-16 max-w-6xl mx-auto mb-20">
          {/* Feature 1 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="flex flex-col h-full">
              <div className="mb-4 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Customize Everything</h3>
              <p className="text-primary/70 text-sm mb-4">
                Personalize your profile with custom colors, fonts, and layouts to match your brand identity.
              </p>
            </div>
          </m.div>

          {/* Feature 2 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex flex-col h-full">
              <div className="mb-4 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Powerful Components</h3>
              <p className="text-primary/70 text-sm mb-4">
                Choose from a variety of components to showcase your content, from videos to social links.
              </p>
            </div>
          </m.div>

          {/* Feature 3 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="flex flex-col h-full">
              <div className="mb-4 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Live Preview</h3>
              <p className="text-primary/70 text-sm mb-4">
                See your changes in real-time with our live preview feature, ensuring your page looks perfect.
              </p>
            </div>
          </m.div>

          {/* Feature 4 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="flex flex-col h-full">
              <div className="mb-4 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Analytics</h3>
              <p className="text-primary/70 text-sm mb-4">
                Track visitor engagement and link clicks with detailed analytics to optimize your content.
              </p>
            </div>
          </m.div>

          {/* Feature 5 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="flex flex-col h-full">
              <div className="mb-4 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Monetization</h3>
              <p className="text-primary/70 text-sm mb-4">
                Turn your audience into revenue with integrated payment options and subscription features.
              </p>
            </div>
          </m.div>

          {/* Feature 6 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="flex flex-col h-full">
              <div className="mb-4 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Privacy Controls</h3>
              <p className="text-primary/70 text-sm mb-4">
                Protect your content with password protection and privacy settings for complete control.
              </p>
            </div>
          </m.div>
        </div>

        {/* Testimonials - Statamic-inspired */}
        <div className="mt-24 relative">
          <div className="absolute -top-10 right-20 w-32 h-32 opacity-20 animate-float">
            <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
          </div>

          <div className="text-center max-w-3xl mx-auto mb-16">
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <p className="text-3xl md:text-4xl font-bold mb-4 font-heading text-primary italic">
                "A game-changer that elevated my online presence with style."
              </p>
              <p className="text-xl text-primary/80 mt-6">
                Isabella Davis
              </p>
            </m.div>
          </div>

          {/* User examples */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* User 1 */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="card"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img
                    src="https://randomuser.me/api/portraits/women/32.jpg"
                    alt="Chris Nina"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-primary">chrisnina</h3>
                  <p className="text-sm text-primary/60">Chris Nina</p>
                </div>
              </div>
              <div className="text-center py-2 px-4 bg-gray-800/20 rounded-md text-primary/80 text-sm mb-2">
                Music
              </div>
              <div className="text-center py-2 px-4 bg-gray-800/20 rounded-md text-primary/80 text-sm">
                Newsletter
              </div>
            </m.div>

            {/* User 2 */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="card"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img
                    src="https://randomuser.me/api/portraits/men/54.jpg"
                    alt="Alexi"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-primary">alexi</h3>
                  <p className="text-sm text-primary/60">Alex Johnson</p>
                </div>
              </div>
              <div className="text-center py-2 px-4 bg-gray-800/20 rounded-md text-primary/80 text-sm mb-2">
                Videos
              </div>
              <div className="text-center py-2 px-4 bg-gray-800/20 rounded-md text-primary/80 text-sm">
                Newsletter
              </div>
            </m.div>

            {/* User 3 */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="card"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img
                    src="https://randomuser.me/api/portraits/women/68.jpg"
                    alt="Sophia Morris"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-primary">sophiamorris</h3>
                  <p className="text-sm text-primary/60">Sophia Morris</p>
                </div>
              </div>
              <div className="text-center py-2 px-4 bg-gray-800/20 rounded-md text-primary/80 text-sm mb-2">
                Podcast
              </div>
              <div className="text-center py-2 px-4 bg-gray-800/20 rounded-md text-primary/80 text-sm">
                Live Stream
              </div>
            </m.div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection
