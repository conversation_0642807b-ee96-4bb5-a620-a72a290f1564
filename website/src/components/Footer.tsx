import { Instagram, Twitter, Facebook, Linkedin, Github } from '../utils/icons'
import Link from 'next/link'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-background border-t border-primary/10">
      <div className="container-custom py-16">
        {/* Statamic-inspired footer with more columns and better organization */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
          {/* Column 1: Main info */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-block mb-4">
              <span className="text-2xl font-bold font-heading text-primary">Link in Bio</span>
            </Link>
            <p className="text-primary/70 mb-6 text-sm">
              One link for your entire digital world. Share everything you create, curate and sell from a single link.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-primary/50 hover:text-primary transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-primary/50 hover:text-primary transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-primary/50 hover:text-primary transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-primary/50 hover:text-primary transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="#" className="text-primary/50 hover:text-primary transition-colors">
                <Github size={20} />
              </a>
            </div>
          </div>

          {/* Column 2: Product */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-4 text-primary">Product</h3>
            <ul className="space-y-3">
              <li><a href="#features" className="text-primary/70 hover:text-primary transition-colors text-sm">Features</a></li>
              <li><a href="#pricing" className="text-primary/70 hover:text-primary transition-colors text-sm">Pricing</a></li>
              <li><a href="#portfolio" className="text-primary/70 hover:text-primary transition-colors text-sm">Portfolio</a></li>
              <li><a href="#" className="text-primary/70 hover:text-primary transition-colors text-sm">Integrations</a></li>
              <li><a href="#" className="text-primary/70 hover:text-primary transition-colors text-sm">Changelog</a></li>
            </ul>
          </div>

          {/* Column 3: Resources */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-4 text-primary">Resources</h3>
            <ul className="space-y-3">
              <li><Link href="/blog" className="text-primary/70 hover:text-primary transition-colors text-sm">Blog</Link></li>
              <li><Link href="/documentation" className="text-primary/70 hover:text-primary transition-colors text-sm">Documentation</Link></li>
              <li><Link href="/tutorials" className="text-primary/70 hover:text-primary transition-colors text-sm">Tutorials</Link></li>
              <li><a href="#" className="text-primary/70 hover:text-primary transition-colors text-sm">Developers</a></li>
            </ul>
          </div>

          {/* Column 4: Support */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-4 text-primary">Support</h3>
            <ul className="space-y-3">
              <li><Link href="/support/faq" className="text-primary/70 hover:text-primary transition-colors text-sm">FAQ</Link></li>
              <li><Link href="/support/getting-started" className="text-primary/70 hover:text-primary transition-colors text-sm">Getting Started</Link></li>
              <li><Link href="/support/assistance" className="text-primary/70 hover:text-primary transition-colors text-sm">Assistance</Link></li>
              <li><Link href="/discord" className="text-primary/70 hover:text-primary transition-colors text-sm">Discord Community</Link></li>
            </ul>
          </div>

          {/* Column 5: Company */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-4 text-primary">Company</h3>
            <ul className="space-y-3">
              <li><Link href="/company/about" className="text-primary/70 hover:text-primary transition-colors text-sm">About Us</Link></li>
              <li><Link href="/company/whats-new" className="text-primary/70 hover:text-primary transition-colors text-sm">What's New</Link></li>
              <li><Link href="/company/contact" className="text-primary/70 hover:text-primary transition-colors text-sm">Contact</Link></li>
              <li><a href="#" className="text-primary/70 hover:text-primary transition-colors text-sm">Careers</a></li>
            </ul>
          </div>
        </div>

        {/* Newsletter Section - Statamic-inspired */}
        <div className="border-t border-primary/10 mt-12 pt-12 pb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="font-heading font-semibold text-lg mb-2 text-primary">Sign up for our newsletter</h3>
              <p className="text-primary/70 text-sm">A monthly digest of the latest updates, tips, and resources.</p>
            </div>
            <div>
              <div className="flex flex-col sm:flex-row gap-3">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="bg-background border border-primary/20 rounded-full px-4 py-2 text-sm text-primary focus:outline-none focus:ring-2 focus:ring-primary/30 flex-grow"
                />
                <button className="btn btn-primary">Subscribe</button>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-primary/10 mt-6 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-primary/50 text-sm">
            &copy; {currentYear} Link in Bio. All rights reserved.
          </p>
          <div className="mt-4 md:mt-0">
            <ul className="flex space-x-6 text-sm">
              <li><Link href="/legal/privacy" className="text-primary/50 hover:text-primary transition-colors">Privacy</Link></li>
              <li><Link href="/legal/terms" className="text-primary/50 hover:text-primary transition-colors">Terms</Link></li>
              <li><Link href="/legal/cookies" className="text-primary/50 hover:text-primary transition-colors">Cookies</Link></li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
