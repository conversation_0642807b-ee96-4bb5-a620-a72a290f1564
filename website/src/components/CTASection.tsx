import { m } from '../utils/motion'

const CTASection = () => {
  return (
    <section className="section bg-background relative overflow-hidden">
      {/* Statamic-inspired floating elements */}
      <div className="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-20 left-10 w-24 h-24 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>

      <div className="container-custom">
        <m.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-5xl mx-auto"
        >
          {/* Statamic-inspired CTA card */}
          <div className="bg-background/30 backdrop-blur-sm border border-primary/10 rounded-xl p-8 md:p-12 relative overflow-hidden">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-transparent opacity-50"></div>

            <div className="relative z-10">
              <div className="text-center mb-10">
                <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading text-primary">
                  Ready to take <span className="gradient-text">Link in Bio</span> for a spin?
                </h2>
                <p className="text-xl text-primary/80 mb-10 max-w-3xl mx-auto">
                  Keep your card in your wallet. Our platform is free to try, and you can upgrade anytime to unlock premium features.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="https://app.hithere.com/register"
                    className="btn btn-primary btn-lg text-base"
                  >
                    Create Your Page
                  </a>
                  <a
                    href="https://app.hithere.com/demo"
                    className="btn btn-outline btn-lg text-base"
                  >
                    Try the Demo
                  </a>
                </div>
              </div>

              <div className="text-center text-primary/60 text-sm">
                If you're a developer, why not <a href="/documentation" className="text-primary underline">check out our API</a>?
              </div>
            </div>
          </div>

          {/* Testimonial with image - Statamic-inspired */}
          <div className="mt-24 mb-8 flex flex-col items-center text-center">
            <div className="w-24 h-24 rounded-full overflow-hidden mb-6 border-2 border-primary/20">
              <img
                src="https://randomuser.me/api/portraits/women/44.jpg"
                alt="Isabella"
                className="w-full h-full object-cover"
              />
            </div>
            <p className="text-2xl font-heading text-primary mb-2">Isabella Davis</p>
            <p className="text-lg text-primary/70 mb-2">Content Creator</p>
            <p className="text-lg text-primary/70 mb-6 max-w-2xl italic">
              "A game-changer that elevated my online presence with style. I've seen a 40% increase in engagement since switching to Link in Bio."
            </p>

            {/* Social proof icons */}
            <div className="flex items-center justify-center space-x-6 text-primary/50">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <span>10K+ Views</span>
              </div>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                </svg>
                <span>40% Engagement</span>
              </div>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Revenue Growth</span>
              </div>
            </div>
          </div>
        </m.div>
      </div>
    </section>
  )
}

export default CTASection
