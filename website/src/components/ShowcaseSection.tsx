import { m } from '../utils/motion'
import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'

// Dati di esempio per le card di showcase
const showcaseProfiles = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Photographer',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    banner: 'bg-gradient-to-r from-blue-500 to-purple-600',
    style: 'dark',
    links: ['Portfolio', 'Instagram', 'Book a Session'],
    url: '/showcase/alex-johnson'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Digital Artist',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    banner: 'bg-gradient-to-r from-pink-500 to-orange-400',
    style: 'gradient',
    links: ['Art Gallery', 'Commission Work', 'Tutorials'],
    url: '/showcase/sophia-lee'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Fitness Coach',
    image: 'https://randomuser.me/api/portraits/men/85.jpg',
    banner: 'bg-gradient-to-r from-green-400 to-teal-500',
    style: 'light',
    links: ['Workout Plans', 'Nutrition Guide', 'Book a Session'],
    url: '/showcase/michael-chen'
  },
  {
    id: 4,
    name: 'Emma <PERSON>',
    role: 'Fashion Designer',
    image: 'https://randomuser.me/api/portraits/women/68.jpg',
    banner: 'bg-gradient-to-r from-purple-600 to-indigo-600',
    style: 'dark',
    links: ['Latest Collection', 'Shop Now', 'Fashion Blog'],
    url: '/showcase/emma-wilson'
  },
  {
    id: 5,
    name: 'David Kim',
    role: 'Music Producer',
    image: 'https://randomuser.me/api/portraits/men/42.jpg',
    banner: 'bg-gradient-to-r from-red-500 to-yellow-500',
    style: 'gradient',
    links: ['Listen Now', 'Upcoming Shows', 'Contact'],
    url: '/showcase/david-kim'
  },
  {
    id: 6,
    name: 'Olivia Martinez',
    role: 'Travel Blogger',
    image: 'https://randomuser.me/api/portraits/women/29.jpg',
    banner: 'bg-gradient-to-r from-cyan-500 to-blue-500',
    style: 'light',
    links: ['Travel Guides', 'Photo Gallery', 'Book Recommendations'],
    url: '/showcase/olivia-martinez'
  },
  {
    id: 7,
    name: 'James Taylor',
    role: 'Chef & Food Writer',
    image: 'https://randomuser.me/api/portraits/men/22.jpg',
    banner: 'bg-gradient-to-r from-amber-500 to-red-600',
    style: 'dark',
    links: ['Recipes', 'Cooking Classes', 'Restaurant Reviews'],
    url: '/showcase/james-taylor'
  },
  {
    id: 8,
    name: 'Ava Thompson',
    role: 'Yoga Instructor',
    image: 'https://randomuser.me/api/portraits/women/57.jpg',
    banner: 'bg-gradient-to-r from-purple-400 to-pink-400',
    style: 'gradient',
    links: ['Classes', 'Meditation Guides', 'Wellness Tips'],
    url: '/showcase/ava-thompson'
  }
]

// Componente per la card del profilo
const ProfileCard = ({ profile }: { profile: typeof showcaseProfiles[0] }) => {
  const getBgColor = () => {
    switch (profile.style) {
      case 'dark':
        return 'bg-gray-900 text-white'
      case 'light':
        return 'bg-gray-100 text-gray-900'
      case 'gradient':
        return `${profile.banner} text-white`
      default:
        return 'bg-gray-900 text-white'
    }
  }

  const getLinkBgColor = () => {
    switch (profile.style) {
      case 'dark':
        return 'bg-gray-800 hover:bg-gray-700'
      case 'light':
        return 'bg-white hover:bg-gray-200 text-gray-900 border border-gray-300'
      case 'gradient':
        return 'bg-white/20 backdrop-blur-sm hover:bg-white/30'
      default:
        return 'bg-gray-800 hover:bg-gray-700'
    }
  }

  return (
    <div className="showcase-card rounded-xl overflow-hidden shadow-xl transition-all duration-300 hover:shadow-2xl transform hover:-translate-y-1">
      <div className={`w-full h-full flex flex-col ${getBgColor()}`}>
        {/* Banner */}
        <div className={`h-24 w-full ${profile.style === 'gradient' ? '' : profile.banner}`}></div>
        
        {/* Profile info */}
        <div className="relative px-4 pt-14 pb-4 flex-1 flex flex-col">
          <div className="absolute left-1/2 transform -translate-x-1/2 -top-10">
            <div className="w-20 h-20 rounded-full border-4 border-white overflow-hidden">
              <img 
                src={profile.image} 
                alt={profile.name} 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          
          <div className="text-center mb-4">
            <h3 className="font-bold text-lg">{profile.name}</h3>
            <p className={`text-sm ${profile.style === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
              {profile.role}
            </p>
          </div>
          
          {/* Links */}
          <div className="flex-1 flex flex-col gap-2 mt-2">
            {profile.links.map((link, index) => (
              <div 
                key={index}
                className={`py-2 px-3 rounded-md text-center text-sm transition-colors ${getLinkBgColor()}`}
              >
                {link}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

const ShowcaseSection = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showLeftArrow, setShowLeftArrow] = useState(false)
  const [showRightArrow, setShowRightArrow] = useState(true)

  // Gestione dello scroll orizzontale
  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current
      setShowLeftArrow(scrollLeft > 0)
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10) // 10px di tolleranza
    }
  }

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll)
      // Verifica iniziale
      handleScroll()
      return () => scrollContainer.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: 'smooth' })
    }
  }

  return (
    <section className="section bg-background relative overflow-hidden">
      {/* Elementi decorativi fluttuanti */}
      <div className="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-40 left-10 w-24 h-24 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading gradient-text">
              Created with HiThere
            </h2>
            <p className="text-xl text-primary/80">
              Explore some of the profiles powered by our platform
            </p>
          </m.div>
        </div>

        {/* Showcase Carousel */}
        <div className="relative">
          {/* Freccia sinistra */}
          {showLeftArrow && (
            <button 
              onClick={scrollLeft}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 backdrop-blur-sm rounded-full p-3 shadow-lg"
              aria-label="Scroll left"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          
          {/* Contenitore scrollabile */}
          <div 
            ref={scrollContainerRef}
            className="flex overflow-x-auto pb-8 hide-scrollbar snap-x snap-mandatory"
            style={{ scrollbarWidth: 'none' }}
          >
            <div className="flex gap-6 px-4">
              {showcaseProfiles.map((profile) => (
                <div key={profile.id} className="w-[280px] flex-shrink-0 snap-center">
                  <ProfileCard profile={profile} />
                </div>
              ))}
            </div>
          </div>
          
          {/* Freccia destra */}
          {showRightArrow && (
            <button 
              onClick={scrollRight}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 backdrop-blur-sm rounded-full p-3 shadow-lg"
              aria-label="Scroll right"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <Link href="/showcase" className="btn btn-primary">
            View All Showcases
          </Link>
        </div>
      </div>
    </section>
  )
}

export default ShowcaseSection
