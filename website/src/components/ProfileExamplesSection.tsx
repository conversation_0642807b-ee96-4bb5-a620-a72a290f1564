import { m } from '../utils/motion'

const ProfileExamplesSection = () => {
  return (
    <section className="section bg-background relative overflow-hidden">
      {/* Statamic-inspired floating elements */}
      <div className="absolute top-20 left-10 w-32 h-32 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-40 right-10 w-24 h-24 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading text-primary">
              Showcase your <span className="gradient-text">unique style</span>
            </h2>
            <p className="text-xl text-primary/80">
              See how creators, artists, and professionals use Link in Bio to express themselves.
            </p>
          </m.div>
        </div>

        {/* Profile Examples Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {/* Profile Example 1 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="card overflow-hidden"
          >
            <div className="relative rounded-lg overflow-hidden mb-4">
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 h-24 w-full"></div>
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
                <div className="w-20 h-20 rounded-full border-4 border-white bg-white overflow-hidden">
                  <img
                    src="https://randomuser.me/api/portraits/men/32.jpg"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-white text-lg font-medium mt-2">David Kim</h3>
                <p className="text-white/80 text-xs">Photographer</p>
              </div>
            </div>
            <div className="pt-16 pb-2">
              <div className="w-full py-2 px-3 bg-blue-500/10 rounded-md text-blue-500 text-center text-sm mb-2">
                Portfolio
              </div>
              <div className="w-full py-2 px-3 bg-blue-500/10 rounded-md text-blue-500 text-center text-sm">
                Book a Session
              </div>
            </div>
          </m.div>

          {/* Profile Example 2 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="card overflow-hidden"
          >
            <div className="relative rounded-lg overflow-hidden mb-4">
              <div className="bg-gradient-to-br from-pink-500 to-orange-400 h-24 w-full"></div>
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
                <div className="w-20 h-20 rounded-full border-4 border-white bg-white overflow-hidden">
                  <img
                    src="https://randomuser.me/api/portraits/women/44.jpg"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-white text-lg font-medium mt-2">Emma Wilson</h3>
                <p className="text-white/80 text-xs">Fashion Designer</p>
              </div>
            </div>
            <div className="pt-16 pb-2">
              <div className="w-full py-2 px-3 bg-pink-500/10 rounded-md text-pink-500 text-center text-sm mb-2">
                Latest Collection
              </div>
              <div className="w-full py-2 px-3 bg-pink-500/10 rounded-md text-pink-500 text-center text-sm">
                Shop Now
              </div>
            </div>
          </m.div>

          {/* Profile Example 3 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="card overflow-hidden"
          >
            <div className="relative rounded-lg overflow-hidden mb-4">
              <div className="bg-gradient-to-br from-green-500 to-teal-400 h-24 w-full"></div>
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
                <div className="w-20 h-20 rounded-full border-4 border-white bg-white overflow-hidden">
                  <img
                    src="https://randomuser.me/api/portraits/men/85.jpg"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-white text-lg font-medium mt-2">James Taylor</h3>
                <p className="text-white/80 text-xs">Fitness Coach</p>
              </div>
            </div>
            <div className="pt-16 pb-2">
              <div className="w-full py-2 px-3 bg-green-500/10 rounded-md text-green-500 text-center text-sm mb-2">
                Workout Plans
              </div>
              <div className="w-full py-2 px-3 bg-green-500/10 rounded-md text-green-500 text-center text-sm">
                Nutrition Guide
              </div>
            </div>
          </m.div>

          {/* Profile Example 4 */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="card overflow-hidden"
          >
            <div className="relative rounded-lg overflow-hidden mb-4">
              <div className="bg-gradient-to-br from-purple-600 to-indigo-600 h-24 w-full"></div>
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
                <div className="w-20 h-20 rounded-full border-4 border-white bg-white overflow-hidden">
                  <img
                    src="https://randomuser.me/api/portraits/women/68.jpg"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-white text-lg font-medium mt-2">Sophia Chen</h3>
                <p className="text-white/80 text-xs">Music Artist</p>
              </div>
            </div>
            <div className="pt-16 pb-2">
              <div className="w-full py-2 px-3 bg-purple-500/10 rounded-md text-purple-500 text-center text-sm mb-2">
                Latest Album
              </div>
              <div className="w-full py-2 px-3 bg-purple-500/10 rounded-md text-purple-500 text-center text-sm">
                Tour Dates
              </div>
            </div>
          </m.div>
        </div>

        {/* More Examples Button */}
        <div className="text-center mt-12">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <a href="/examples" className="btn btn-outline">
              View More Examples
            </a>
          </m.div>
        </div>
      </div>
    </section>
  )
}

export default ProfileExamplesSection
