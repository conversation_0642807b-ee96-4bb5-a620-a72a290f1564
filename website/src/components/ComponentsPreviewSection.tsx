import { m } from '../utils/motion'

const ComponentsPreviewSection = () => {
  return (
    <section className="section bg-background relative overflow-hidden">
      {/* Statamic-inspired floating elements */}
      <div className="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      <div className="absolute bottom-40 left-10 w-24 h-24 opacity-20 animate-float-delayed">
        <div className="w-full h-full rounded-full bg-primary/20 blur-xl"></div>
      </div>
      
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-heading text-primary">
              Powerful <span className="gradient-text">components</span> for every need
            </h2>
            <p className="text-xl text-primary/80">
              Customize your page with our wide range of components designed to showcase your content.
            </p>
          </m.div>
        </div>

        {/* Components Preview Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {/* Component 1: Link */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="card overflow-hidden"
          >
            <div className="mb-4">
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Link Component</h3>
              <p className="text-primary/70 text-sm">
                Create beautiful, customizable links to your external content.
              </p>
            </div>
            <div className="bg-gray-900 rounded-lg overflow-hidden p-4">
              <div className="w-full py-3 px-4 bg-gray-800 rounded-md text-white text-center mb-3 flex items-center justify-between">
                <span>My YouTube Channel</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </div>
              <div className="w-full py-3 px-4 bg-gray-800 rounded-md text-white text-center flex items-center justify-between">
                <span>My Online Store</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </div>
            </div>
          </m.div>

          {/* Component 2: Video */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="card overflow-hidden"
          >
            <div className="mb-4">
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Video Component</h3>
              <p className="text-primary/70 text-sm">
                Embed videos from various platforms with custom thumbnails.
              </p>
            </div>
            <div className="bg-gray-900 rounded-lg overflow-hidden">
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1611162616475-46b635cb6868?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" 
                  alt="Video thumbnail" 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-14 h-14 rounded-full bg-primary/80 flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z" />
                    </svg>
                  </div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                  <p className="text-white text-sm font-medium">Latest Tutorial: Creating Your Bio</p>
                </div>
              </div>
            </div>
          </m.div>

          {/* Component 3: Social Icons */}
          <m.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="card overflow-hidden"
          >
            <div className="mb-4">
              <h3 className="text-xl font-bold mb-2 font-heading text-primary">Social Icons</h3>
              <p className="text-primary/70 text-sm">
                Connect all your social profiles in one place with customizable icons.
              </p>
            </div>
            <div className="bg-gray-900 rounded-lg overflow-hidden p-6">
              <div className="flex justify-center gap-4">
                <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                </div>
                <div className="w-10 h-10 rounded-full bg-pink-600 flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0z" />
                    <path d="M12 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8z" />
                  </svg>
                </div>
                <div className="w-10 h-10 rounded-full bg-sky-500 flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                  </svg>
                </div>
                <div className="w-10 h-10 rounded-full bg-red-600 flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                  </svg>
                </div>
              </div>
            </div>
          </m.div>
        </div>

        {/* Custom Themes Preview */}
        <div className="mt-24 mb-16">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6 font-heading text-primary">
                Personalized themes for your unique style
              </h2>
              <p className="text-lg text-primary/80">
                Choose from a variety of themes or create your own with custom colors and fonts.
              </p>
            </m.div>
          </div>

          {/* Theme Previews */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Theme 1: Dark */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="relative"
            >
              <div className="phone-mockup-small">
                <div className="bg-gray-900 h-full w-full flex flex-col">
                  {/* Profile header */}
                  <div className="relative">
                    <div className="w-full h-24 bg-gray-800"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 translate-y-0 flex flex-col items-center">
                      <div className="w-16 h-16 rounded-full bg-gray-700 mb-2 overflow-hidden">
                        <img
                          src="https://randomuser.me/api/portraits/men/32.jpg"
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <h3 className="text-white text-sm font-medium">Alex Johnson</h3>
                      <p className="text-gray-400 text-xs">Photographer</p>
                    </div>
                  </div>

                  {/* Links */}
                  <div className="flex-1 px-3 pt-16 pb-3 flex flex-col gap-2 overflow-y-auto">
                    <div className="w-full py-2 px-3 bg-gray-800 rounded-md text-white text-center text-xs">
                      Portfolio
                    </div>
                    <div className="w-full py-2 px-3 bg-gray-800 rounded-md text-white text-center text-xs">
                      Instagram
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-center mt-4 text-primary font-medium">Dark Theme</p>
            </m.div>

            {/* Theme 2: Gradient */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative"
            >
              <div className="phone-mockup-small">
                <div className="bg-gradient-to-br from-purple-900 via-pink-800 to-purple-900 h-full w-full flex flex-col">
                  {/* Profile header */}
                  <div className="relative">
                    <div className="w-full h-24 bg-black/20"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 translate-y-0 flex flex-col items-center">
                      <div className="w-16 h-16 rounded-full bg-pink-200 mb-2 overflow-hidden">
                        <img
                          src="https://randomuser.me/api/portraits/women/44.jpg"
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <h3 className="text-white text-sm font-medium">Sophia Lee</h3>
                      <p className="text-pink-200 text-xs">Digital Artist</p>
                    </div>
                  </div>

                  {/* Links */}
                  <div className="flex-1 px-3 pt-16 pb-3 flex flex-col gap-2 overflow-y-auto">
                    <div className="w-full py-2 px-3 bg-white/10 backdrop-blur-sm rounded-md text-white text-center text-xs">
                      Art Gallery
                    </div>
                    <div className="w-full py-2 px-3 bg-white/10 backdrop-blur-sm rounded-md text-white text-center text-xs">
                      Commission Work
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-center mt-4 text-primary font-medium">Gradient Theme</p>
            </m.div>

            {/* Theme 3: Light */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="relative"
            >
              <div className="phone-mockup-small">
                <div className="bg-gray-100 h-full w-full flex flex-col">
                  {/* Profile header */}
                  <div className="relative">
                    <div className="w-full h-24 bg-blue-100"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 translate-y-0 flex flex-col items-center">
                      <div className="w-16 h-16 rounded-full bg-white mb-2 overflow-hidden border-2 border-blue-200">
                        <img
                          src="https://randomuser.me/api/portraits/men/85.jpg"
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <h3 className="text-gray-800 text-sm font-medium">Michael Chen</h3>
                      <p className="text-gray-500 text-xs">Fitness Coach</p>
                    </div>
                  </div>

                  {/* Links */}
                  <div className="flex-1 px-3 pt-16 pb-3 flex flex-col gap-2 overflow-y-auto">
                    <div className="w-full py-2 px-3 bg-blue-500 rounded-md text-white text-center text-xs">
                      Workout Plans
                    </div>
                    <div className="w-full py-2 px-3 bg-blue-500 rounded-md text-white text-center text-xs">
                      Book a Session
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-center mt-4 text-primary font-medium">Light Theme</p>
            </m.div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ComponentsPreviewSection
