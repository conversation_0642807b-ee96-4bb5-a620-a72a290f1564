# HiThere

A comprehensive full-stack Linktree clone built with React, Node.js, and MongoDB. This application allows users to create personalized profile pages with various interactive components, track visitor engagement, and customize their online presence.

![HiThere](https://github.com/yourusername/linktree-clone/raw/main/frontend/public/preview.png)

## 🌟 Features

### User Management
- **Secure Authentication**: JWT-based authentication with token validation
- **User Profiles**: Customizable profiles with unique slugs
- **Country-Specific Profiles**: Create different profile versions for different countries
- **Verified Profiles**: Blue and gold star verification badges to show profile authenticity
- **Referral System**: Invite others with personal referral links and track referred users

### Component System
- **Multiple Component Types**:
  - **Links**: Standard clickable links with icons
  - **Announcements**: Styled text announcements with custom colors
  - **Shuffle Cards**: Swipeable card stacks with images and links
  - **Mini Lead Forms**: Embedded lead capture forms
  - **Video Components**: Thumbnail-only video display in landscape mode
  - **Embedded Videos**: YouTube and TikTok video embedding with customizable controls
  - **Lottie Animations**: Interactive animations triggered by page load, user inactivity, or link clicks
  - **Payment Links**: Direct monetization through multiple payment providers (Stripe, PayPal, Mollie, RazorPay)

### Profile Customization
- **Visual Theming**: Customize colors, backgrounds, and fonts
- **Google Fonts Integration**: Select from a wide range of Google Fonts to personalize your profile typography
- **Page Background**: Upload custom background images with configurable properties (position, repeat, size, attachment)
- **Default Visuals**: Automatically generated banner backgrounds using Haikei service and DiceBear avatars for new profiles
- **Avatar Options**: Upload profile images with position and shape controls
- **Rich Text Bio**: Format your bio with rich text editor including bold, italic, headings, lists, and links
- **Emoji Support**: Add emoji to your bio and other text fields with an integrated emoji picker
- **QR Code Generation**: Generate custom QR codes with "Hi!" text in the center
- **Direct Redirect**: Option to forward visitors directly to a specific URL
- **SEO Optimization**: Customize title, description, and OpenGraph tags for better search visibility

### Advanced Features
- **Password Protection**: Secure links with password protection
- **Scheduled Visibility**: Set start and end dates for component visibility
- **Click Tracking**: Comprehensive analytics on visitor engagement
- **Lead Generation**: Collect and manage leads through mini forms
- **Analytics Integration**: Google Analytics (GA4) and Microsoft Clarity tracking
- **Direct Monetization**: Accept payments through multiple payment providers (Stripe, PayPal, Mollie, RazorPay)
- **Referral Program**: Earn from referred users with standard and VIP referral codes
- **Responsive Design**: Optimized for all device sizes

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast build tooling
- **TailwindCSS** for utility-first styling
- **ShadCN/UI** for consistent UI components
- **React Router** for client-side routing
- **Recharts** for data visualization
- **Axios** for API requests
- **React Icons** for comprehensive icon library
- **React Helmet Async** for dynamic SEO management
- **Lottie-web** for high-quality animations
- **UUID** for generating unique identifiers
- **HLS.js** for streaming video playback
- **TipTap** for rich text editing capabilities
- **Emoji-Picker-React** for emoji selection interface

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **MongoDB** with Mongoose for data modeling
- **JWT** for secure authentication
- **bcrypt** for password hashing
- **Multer** for file uploads
- **QRCode** and **canvas** for QR code generation
- **Payment APIs** for multiple payment processing integrations (Stripe, PayPal, Mollie, RazorPay)
- **Bunny CDN API** for video streaming services

## 🚀 Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or Atlas)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/linktree-clone.git
cd linktree-clone
```

2. Install dependencies
```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

3. Set up environment variables
```bash
# Backend
cd backend
cp .env.example .env
# Edit .env with your MongoDB connection string and JWT secret

# Frontend
cd ../frontend
cp .env.example .env
# Edit .env if your API URL is different
```

4. Start the development servers
```bash
# Start backend server
cd backend
npm run dev

# Start frontend server
cd ../frontend
npm run dev
```

5. Open your browser and navigate to `http://localhost:5173`

## 📁 Project Structure

```
linktree-clone/
├── backend/               # Backend code
│   ├── src/
│   │   ├── config/        # Configuration files
│   │   ├── controllers/   # Request handlers
│   │   ├── middleware/    # Express middleware
│   │   ├── models/        # Mongoose models
│   │   ├── routes/        # API routes
│   │   ├── utils/         # Utility functions
│   │   └── index.ts       # Entry point
│   ├── media/             # Uploaded media files
│   ├── .env               # Environment variables
│   └── package.json       # Dependencies
│
└── frontend/              # Frontend code
    ├── public/            # Static files
    ├── src/
    │   ├── api/           # API service functions
    │   ├── assets/        # Static assets
    │   ├── components/    # React components
    │   │   ├── ui/        # UI components from ShadCN
    │   │   └── ...        # Custom components
    │   ├── context/       # React context providers
    │   ├── data/          # Static data (countries, etc.)
    │   ├── hooks/         # Custom React hooks
    │   ├── pages/         # Page components
    │   ├── styles/        # Global styles
    │   ├── types/         # TypeScript types
    │   ├── App.tsx        # Main app component
    │   └── main.tsx       # Entry point
    ├── .env               # Environment variables
    └── package.json       # Dependencies
```

## 📊 Component Types

### Link
Standard clickable links with customizable titles and optional icons.

### Announcement
Text-based announcements with customizable background and text colors.

### Shuffle Cards
Swipeable card stack with images, titles, and links that users can browse through.

### Mini Lead
Lead capture forms with customizable fields to collect visitor information.

### Video
Video component with thumbnail-only display in landscape mode. Simplified for better user experience.

#### Bunny CDN Integration
- **Streaming Video Support**: Videos are uploaded to Bunny CDN for high-performance streaming
- **Automatic URL Construction**: System automatically constructs the proper streaming URL format
- **Thumbnail Management**: Upload and display custom thumbnails for videos
- **Password Protection**: Secure videos with password protection
- **Visual Indicators**: Lock icon displayed on password-protected videos
- **Unlock Experience**: Password dialog with validation and visual feedback
- **Preview Support**: Fully functional video player in both live and preview modes

### Embedded Videos
YouTube and TikTok video embedding with options for autoplay, muting, and showing/hiding controls. Includes advanced features like password protection, auto-expire, and scheduled display.

#### Password Protection for Embedded Content
- **Secure Access**: Password-protect YouTube and TikTok embeds
- **Placeholder Display**: Shows a lock icon and protected content message until unlocked
- **Consistent Experience**: Same password dialog and validation as video components
- **Preview Mode Support**: Password protection fully functional in both live and preview modes

#### TikTok Integration
- Embed TikTok videos directly in your profile
- Customizable preview with title and thumbnail
- Autoplay options for better engagement
- Same advanced features as YouTube embeds (password protection, scheduling, etc.)
- Responsive design that works across all devices

### Lottie Animations
Interactive animations that can be triggered by various events:
- **Page Load**: Animation appears automatically after a specified delay when the page loads
- **User Inactivity**: Animation appears after a set period of user inactivity
- **Link Click**: Animation is triggered when a specific link is clicked
- **Background Preloading**: Animations are preloaded in the background for instant display when triggered
- **Optimized Performance**: Efficient loading system that prevents performance issues

### Payment
Direct monetization through multiple payment providers, allowing visitors to make payments directly from your profile:
- **Multiple Payment Providers**: Support for Stripe, PayPal, Mollie, and RazorPay
- **Customizable Payment Links**: Add payment links with custom titles and descriptions
- **Payment Callback URLs**: Professionally designed callback pages for successful payments and cancellations
- **Thank You Page**: Customizable thank you page shown after successful payment
- **Cancellation Page**: Dedicated page for payment cancellations

### Amazon Affiliate
Monetize your profile with Amazon Affiliate links:
- **Affiliate Tracking ID**: Configure your Amazon Affiliate tracking ID in a dedicated settings page
- **Automatic Link Processing**: Automatically add your tracking ID to Amazon links in your components
- **Visual Indicators**: Amazon badge shown in the dashboard to identify links with affiliate tracking
- **Toggle Control**: Enable or disable automatic tracking with a simple switch
- **Privacy-Focused**: Badges only visible in the dashboard, not on the public profile

## 🔒 Security Features

- JWT authentication with expiration
- Password hashing with bcrypt
- Password-protected links and components
- Secure file uploads with type validation
- Referral tracking with privacy protection
- Verified profile badges for authenticity

### Enhanced Password Protection
- **Visual Feedback**: Lock icons and badges for password-protected content
- **Interactive Dialog**: User-friendly password entry with validation feedback
- **Error Animation**: Visual shake animation for incorrect password attempts
- **Consistent Experience**: Same protection system across all content types
- **Preview Mode Support**: Password protection fully functional in preview mode

## 📱 Responsive Design

The application is fully responsive and optimized for:
- Desktop browsers
- Tablets
- Mobile devices

## 📊 Analytics & SEO

- **Google Analytics Integration**: Track visitor behavior with GA4
- **Microsoft Clarity**: Gain insights through heatmaps and session recordings
- **SEO Optimization**: Customize meta tags for better search engine visibility
- **OpenGraph Support**: Control how your profile appears when shared on social media

## 💰 Monetization

- **Direct Payments**: Accept payments through multiple payment providers (Stripe, PayPal, Mollie, RazorPay)
- **Amazon Affiliate**: Automatically add your Amazon Affiliate tracking ID to Amazon links in your components
- **Referral Program**: Earn from users who sign up through your referral links
- **VIP Referral Codes**: Create limited-use referral codes for special promotions

## 🔄 UI Improvements

- Centered confirmation popups for better user experience
- Standardized spacing between UI elements
- Consistent visible/hide switch layout across all components
- Optimized tab organization in the dashboard

### Profile Background Image (v0.2)
- **Custom Background Images**: Upload and apply custom background images to your profile page
- **Draggable Widget**: Easily accessible background image widget with drag-and-drop functionality
- **Advanced Configuration**: Customize background properties including position, repeat, size, and attachment
- **Real-time Preview**: See changes to your background image and properties in real-time
- **Responsive Design**: Background images adapt to different screen sizes while maintaining visual appeal

### Google Fonts Integration (v0.2)
- **Typography Customization**: Select from hundreds of Google Fonts to personalize your profile
- **Font Preview**: Real-time preview of selected fonts in the profile editor
- **Consistent Typography**: Applied fonts are consistent across all profile elements
- **Performance Optimization**: Efficient font loading for optimal page performance
- **Responsive Typography**: Fonts scale appropriately across different device sizes

### Default Profile Visuals (v0.2)
- **Haikei Banner Backgrounds**: Automatically generated stylish banner backgrounds for new profiles
- **DiceBear Avatars**: Algorithmically generated avatars as default profile pictures
- **Personalized Experience**: Each new user gets unique default visuals
- **Seamless Onboarding**: New users have attractive profiles from the start without manual customization
- **Easy Replacement**: Default visuals can be easily replaced with custom uploads

## 📋 Dashboard Structure

### Main Navigation
- **Dashboard**: Overview and statistics
- **Actions**: Manage Lottie animations and interactive elements
- **Profile**: Customize your profile appearance
- **Settings**: Configure application settings
- **Direct Monetization**: Set up payment links with multiple providers (Stripe, PayPal, Mollie, RazorPay)
- **Amazon Affiliate**: Configure Amazon Affiliate tracking ID and settings
- **Referrals**: Manage your referral program
- **Menu Leads**: View and manage collected leads
- **Users**: User management (admin only)

### Profile Tabs
- **Profile Info**: Basic profile information
- **SEO**: Search engine optimization settings
- **Appearance**: Visual customization options including colors, Google Fonts selection, and background images
- **QR Code**: Generate and customize your profile QR code
- **Background**: Upload and configure custom background images with advanced properties
- **Typography**: Select and preview Google Fonts for your profile

### Actions Page
- **Lottie Animations**: Add and manage interactive animations
- **Active/Archived**: Toggle between active and archived animations
- **Animation Triggers**: Configure when animations appear (page load, inactivity, link click)
- **Animation Preview**: Visual feedback on animation loading status

### Settings Tabs
- **Verified Profile**: Enable blue or gold verification badges
- **Analytics**: Configure Google Analytics and Microsoft Clarity
- **Languages**: Set up language preferences

## 📝 Version History

### Version 0.4
- Added Amazon Affiliate integration with dedicated settings page
- Implemented automatic Amazon Affiliate tracking ID addition to Amazon links
- Added visual indicators (badges) in dashboard for links with Amazon Affiliate tracking
- Created privacy-focused design with badges only visible in dashboard, not on public profiles
- Improved UI with increased spacing between badges in link components
- Separated Amazon Affiliate settings from Direct Monetization for better organization

### Version 0.3
- Added support for multiple payment providers (Stripe, PayPal, Mollie, RazorPay)
- Implemented professionally designed payment callback pages for successful payments and cancellations
- Added rich text editor for bio with formatting options (bold, italic, headings, lists, links)
- Integrated emoji picker for bio and other text fields
- Enhanced UI for payment callback URLs with improved visual design and icons
- Ensured proper display of formatted content in profile preview and public profile
- Added comprehensive documentation for new features

### Version 0.2
- Added custom page background image functionality with draggable widget
- Implemented advanced background image configuration (position, repeat, size, attachment)
- Added Google Fonts integration for custom typography across profiles
- Implemented default banner backgrounds using Haikei service for new profiles
- Added DiceBear avatars as default profile pictures instead of empty placeholders
- Fixed UI issues with image uploaders to prevent duplicate image displays
- Enhanced real-time preview functionality for background images and fonts
- Improved responsive design for background images across different devices

### Version 0.1
- Initial release with core functionality
- Basic profile customization options
- Component system with links, announcements, videos, and more
- User management and authentication

## 📄 License

MIT
